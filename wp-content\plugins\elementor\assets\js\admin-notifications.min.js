/*! elementor - v3.31.0 - 11-08-2025 */
(()=>{var e={9730:e=>{"use strict";e.exports=elementorV2.query},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},18821:(e,t,r)=>{var n=r(70569),a=r(65474),o=r(37744),i=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||a(e,t)||o(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},24752:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var o=a(r(41594)),i=a(r(78304)),l=r(86956);(t.WhatsNewItemChips=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,n=e.itemIndex,a=[];return t&&a.push({color:"promotion",size:"small",label:t}),r&&r.forEach(function(e){a.push({variant:"outlined",size:"small",label:e})}),a.length?o.default.createElement(l.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},a.map(function(e,t){return o.default.createElement(l.Chip,(0,i.default)({key:"chip-".concat(n).concat(t)},e))})):null}).propTypes={chipPlan:n.string,chipTags:n.array,itemIndex:n.number.isRequired}},25206:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var o=a(r(41594)),i=r(86956),l=r(56971),s=r(94841),u=r(46555),c=r(24752);(t.WhatsNewItem=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,n=e.itemsLength,a=e.setIsOpen;return o.default.createElement(i.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&o.default.createElement(l.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),o.default.createElement(s.WrapperWithLink,{link:t.link},o.default.createElement(i.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&o.default.createElement(u.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),o.default.createElement(c.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&o.default.createElement(i.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&o.default.createElement(o.default.Fragment,null," ",o.default.createElement(i.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&o.default.createElement(i.Box,{sx:{pb:2}},o.default.createElement(i.Button,{href:t.ctaLink,target:t.ctaLink.startsWith("#")?"_self":"_blank",variant:"contained",size:"small",color:"promotion",onClick:t.ctaLink.startsWith("#")?function(){return a(!1)}:function(){}},t.cta)),r!==n-1&&o.default.createElement(i.Divider,{sx:{my:1}}))}).propTypes={item:n.object.isRequired,itemIndex:n.number.isRequired,itemsLength:n.number.isRequired,setIsOpen:n.func.isRequired}},30482:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var o=a(r(41594)),i=r(86956),l=r(12470),s=r(59190);(t.WhatsNewTopBar=function WhatsNewTopBar(e){var t=e.setIsOpen;return o.default.createElement(o.default.Fragment,null,o.default.createElement(i.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},o.default.createElement(i.Toolbar,{variant:"dense"},o.default.createElement(i.Typography,{variant:"overline",sx:{flexGrow:1}},(0,l.__)("What's New","elementor")),o.default.createElement(i.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},o.default.createElement(s.XIcon,null)))),o.default.createElement(i.Divider,null))}).propTypes={setIsOpen:n.func.isRequired}},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},40362:(e,t,r)=>{"use strict";var n=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,a,o,i){if(i!==n){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},41594:e=>{"use strict";e.exports=React},46120:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,n){elementorCommon.ajax.addRequest(e,{success:r,error:n,data:t})})}("notifications_get")}},46555:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var o=a(r(41594)),i=r(86956),l=r(94841);(t.WhatsNewItemThumbnail=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,n=e.link;return o.default.createElement(i.Box,{sx:{pb:2}},o.default.createElement(l.WrapperWithLink,{link:n},o.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))}).propTypes={imageSrc:n.string.isRequired,title:n.string.isRequired,link:n.string}},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56971:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var o=a(r(41594)),i=r(86956);(t.WhatsNewItemTopicLine=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return o.default.createElement(i.Stack,{direction:"row",divider:o.default.createElement(i.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&o.default.createElement(i.Box,null,t),r&&o.default.createElement(i.Box,null,r))}).propTypes={topic:n.string,date:n.string}},58644:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var o=a(r(41594)),i=r(9730),l=r(46120),s=r(86956),u=r(25206);(t.WhatsNewDrawerContent=function WhatsNewDrawerContent(e){var t=e.setIsOpen,r=(0,i.useQuery)({queryKey:["e-notifications"],queryFn:l.getNotifications}),n=r.isPending,a=r.error,c=r.data;return n?o.default.createElement(s.Box,null,o.default.createElement(s.LinearProgress,{color:"secondary"})):a?o.default.createElement(s.Box,null,"An error has occurred: ",a):c.map(function(e,r){return o.default.createElement(u.WhatsNewItem,{key:r,item:e,itemIndex:r,itemsLength:c.length,setIsOpen:t})})}).propTypes={setIsOpen:n.func.isRequired}},59190:(e,t,r)=>{"use strict";var n=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var o=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var o,i,l={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return l;if(o=t?n:r){if(o.has(e))return o.get(e);o.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(o=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?o(l,s,i):l[s]=e[s]);return l}(e,t)}(r(41594)),i=n(r(78304)),l=r(86956);t.XIcon=(0,o.forwardRef)(function(e,t){return o.default.createElement(l.SvgIcon,(0,i.default)({viewBox:"0 0 24 24"},e,{ref:t}),o.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),o.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))})},62688:(e,t,r)=>{e.exports=r(40362)()},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,l=[],s=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},74324:(e,t,r)=>{"use strict";var n=r(62688),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var o=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var o,i,l={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return l;if(o=t?n:r){if(o.has(e))return o.get(e);o.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(o=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?o(l,s,i):l[s]=e[s]);return l}(e,t)}(r(41594)),i=r(86956),l=r(9730),s=r(30482),u=r(58644);var c=new l.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}});(t.WhatsNew=function WhatsNew(e){var t,r,n=e.isOpen,a=e.setIsOpen,p=e.setIsRead,d=e.anchorPosition,f=void 0===d?"right":d;return(0,o.useEffect)(function(){n&&p(!0)},[n,p]),o.default.createElement(o.default.Fragment,null,o.default.createElement(l.QueryClientProvider,{client:c},o.default.createElement(i.DirectionProvider,{rtl:elementorCommon.config.isRTL},o.default.createElement(i.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},o.default.createElement(i.Drawer,{anchor:f,open:n,onClose:function onClose(){return a(!1)},ModalProps:{style:{zIndex:999999}}},o.default.createElement(i.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},o.default.createElement(s.WhatsNewTopBar,{setIsOpen:a}),o.default.createElement(i.Box,{sx:{padding:"16px"}},o.default.createElement(u.WhatsNewDrawerContent,{setIsOpen:a}))))))))}).propTypes={isOpen:n.bool.isRequired,setIsOpen:n.func.isRequired,setIsRead:n.func.isRequired,anchorPosition:n.oneOf(["left","top","right","bottom"])}},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},83876:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.BarButtonNotification=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,l={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return l;if(a=t?n:r){if(a.has(e))return a.get(e);a.set(e,l)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(l,s,i):l[s]=e[s]);return l}(e,t)}(r(41594)),l=a(r(18821)),s=r(74324),u=r(86956);(t.BarButtonNotification=function BarButtonNotification(e){var t=e.defaultIsRead,r=(0,i.useState)(!1),n=(0,l.default)(r,2),a=n[0],o=n[1],c=(0,i.useState)(t),p=(0,l.default)(c,2),d=p[0],f=p[1];return i.default.createElement(i.default.Fragment,null,i.default.createElement("button",{className:"e-admin-top-bar__bar-button",style:{backgroundColor:"transparent",border:"none"},onClick:function onClick(e){e.preventDefault(),o(!0)}},i.default.createElement(u.Badge,{color:"primary",variant:"dot",invisible:d,sx:{mx:.5}},i.default.createElement("i",{className:"e-admin-top-bar__bar-button-icon eicon-speakerphone"})),i.default.createElement("span",{className:"e-admin-top-bar__bar-button-title"},e.children)),i.default.createElement(s.WhatsNew,{isOpen:a,setIsOpen:o,setIsRead:f}))}).propTypes={defaultIsRead:n.bool,children:n.any.isRequired}},86956:e=>{"use strict";e.exports=elementorV2.ui},94841:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var o=a(r(41594)),i=r(86956);(t.WrapperWithLink=function WrapperWithLink(e){var t=e.link,r=e.children;return t?o.default.createElement(i.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r}).propTypes={link:n.string,children:n.any.isRequired}},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,__webpack_require__),a.exports}(()=>{"use strict";var e=__webpack_require__(83876);window.elementorNotificationCenter={BarButtonNotification:e.BarButtonNotification}})()})();