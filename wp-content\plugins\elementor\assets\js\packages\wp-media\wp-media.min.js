!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{getMediaAttachment:function(){return l},useWpMediaAttachment:function(){return c},useWpMediaFrame:function(){return f}});var n=window.elementorV2.query,o=window.elementorV2.utils;const r=(0,o.createError)({code:"wp_media_not_available",message:"`wp.media` is not available, make sure the `media-models` handle is set in the dependencies array"}),i=(0,o.createError)({code:"wp_plupload_settings_not_available",message:"`_wpPluploadSettings` is not available, make sure a wp media uploader is open"}),a=window;var u=()=>{if(!a.wp?.media)throw new r;return a.wp.media};function s(e){const{filesizeInBytes:t,filesizeHumanReadable:n,author:o,authorName:r,...i}=e;return{...i,filesize:{inBytes:t,humanReadable:n},author:{id:parseInt(o),name:r}}}async function l({id:e}){if(!e)return null;const t=u().attachment(e),n=t.toJSON();if("url"in n)return s(n);try{return s(await t.fetch())}catch{return null}}function c(e){return(0,n.useQuery)({queryKey:["wp-attachment",e],queryFn:()=>l({id:e}),enabled:!!e})}var d=window.React;const p=window;var m=()=>{if(!p._wpPluploadSettings)throw new i;return p._wpPluploadSettings};function f(e){const t=(0,d.useRef)();return(0,d.useEffect)(()=>()=>{w(t.current)},[]),{open:(n={})=>{w(t.current),t.current=function({onSelect:e,multiple:t,mediaTypes:n,selected:o,title:r,mode:i="browse"}){const a=u()({title:r,multiple:t,library:{type:g(n)}}).on("open",()=>{!function(e){e.uploader.uploader.param("uploadTypeCaller","elementor-wp-media-upload")}(a),function(e,t="browse"){e.content.mode(t)}(a,i),function(e,t){const n=("number"==typeof t?[t]:t)?.filter(e=>!!e).map(e=>u().attachment(e));e.state().get("selection").set(n||[])}(a,o)}).on("close",()=>w(a)).on("insert select",()=>function(e,t,n){const o=e.state().get("selection").toJSON().map(s);n(t?o:o[0])}(a,t,e));return function(e,t){const n=m().defaults.filters.mime_types?.[0]?.extensions;e.on("ready",()=>{m().defaults.filters.mime_types=[{extensions:b(t)}]}),e.on("close",()=>{m().defaults.filters.mime_types=n?[{extensions:n}]:[]})}(a,n),a}({...e,...n}),t.current?.open()}}}function w(e){e?.detach(),e?.remove()}const y=["avif","bmp","gif","ico","jpe","jpeg","jpg","png","webp"];function g(e){const t={image:y.map(e=>`image/${e}`),svg:["image/svg+xml"]};return e.reduce((e,n)=>e.concat(t[n]),[])}function b(e){const t={image:y,svg:["svg"]};return e.reduce((e,n)=>e.concat(t[n]),[]).join(",")}(window.elementorV2=window.elementorV2||{}).wpMedia=t}(),window.elementorV2.wpMedia?.init?.();