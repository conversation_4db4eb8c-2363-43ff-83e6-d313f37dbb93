<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Sandbox;

/**
 * Exception thrown when a not allowed function is used in a template.
 *
 * <AUTHOR> <<EMAIL>>
 */
final class SecurityNotAllowedFunctionError extends SecurityError
{
    private $functionName;
    public function __construct(string $message, string $functionName)
    {
        parent::__construct($message);
        $this->functionName = $functionName;
    }
    public function getFunctionName() : string
    {
        return $this->functionName;
    }
}
