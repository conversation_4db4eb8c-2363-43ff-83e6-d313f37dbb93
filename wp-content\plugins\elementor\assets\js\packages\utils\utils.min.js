!function(){"use strict";var e={d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{ElementorError:function(){return t},createError:function(){return r},debounce:function(){return c},ensureError:function(){return u},hash:function(){return i},useDebounceState:function(){return l}});class t extends Error{constructor(e,{code:n,context:t=null,cause:r=null}){super(e,{cause:r}),this.context=t,this.code=n}}const r=({code:e,message:n})=>class extends t{constructor({cause:t,context:r}={}){super(n,{cause:t,code:e,context:r})}},u=e=>{if(e instanceof Error)return e;let n,t=null;try{n=JSON.stringify(e)}catch(e){t=e,n="Unable to stringify the thrown value"}return new Error(`Unexpected non-error thrown: ${n}`,{cause:t})};var o=window.React;function c(e,n){let t=null;const r=()=>{t&&(clearTimeout(t),t=null)},u=(...u)=>{r(),t=setTimeout(()=>{e(...u),t=null},n)};return u.flush=(...n)=>{r(),e(...n)},u.cancel=r,u.pending=()=>!!t,u}function l(e={}){const{delay:n=300,initialValue:t=""}=e,[r,u]=(0,o.useState)(t),[l,i]=(0,o.useState)(t),s=(0,o.useRef)(null);(0,o.useEffect)(()=>()=>{s.current?.cancel?.()},[]);const a=(0,o.useCallback)(e=>{s.current?.cancel?.(),s.current=c(()=>{u(e)},n),s.current()},[n]);return{debouncedValue:r,inputValue:l,handleChange:e=>{i(e),a(e)},setInputValue:i}}function i(e){return JSON.stringify(e,(e,n)=>function(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}(n)?Object.keys(n).sort().reduce((e,t)=>(e[t]=n[t],e),{}):n)}(window.elementorV2=window.elementorV2||{}).utils=n}(),window.elementorV2.utils?.init?.();