/*! elementor - v3.31.0 - 11-08-2025 */
/*! For license information please see announcements-app.min.js.LICENSE.txt */
(()=>{var r={3073:(r,u)=>{"use strict";function _createForOfIteratorHelper(r,u){var c="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!c){if(Array.isArray(r)||(c=function _unsupportedIterableToArray(r,u){if(r){if("string"==typeof r)return _arrayLikeToArray(r,u);var c={}.toString.call(r).slice(8,-1);return"Object"===c&&r.constructor&&(c=r.constructor.name),"Map"===c||"Set"===c?Array.from(r):"Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?_arrayLikeToArray(r,u):void 0}}(r))||u&&r&&"number"==typeof r.length){c&&(r=c);var l=0,p=function F(){};return{s:p,n:function n(){return l>=r.length?{done:!0}:{done:!1,value:r[l++]}},e:function e(r){throw r},f:p}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var _,y=!0,m=!1;return{s:function s(){c=c.call(r)},n:function n(){var r=c.next();return y=r.done,r},e:function e(r){m=!0,_=r},f:function f(){try{y||null==c.return||c.return()}finally{if(m)throw _}}}}function _arrayLikeToArray(r,u){(null==u||u>r.length)&&(u=r.length);for(var c=0,l=Array(u);c<u;c++)l[c]=r[c];return l}Object.defineProperty(u,"__esModule",{value:!0}),u.appsEventTrackingDispatch=void 0;u.appsEventTrackingDispatch=function appsEventTrackingDispatch(r,u){var c=function objectCreator(r,c){var l,p=_createForOfIteratorHelper(r);try{for(p.s();!(l=p.n()).done;){var _=l.value;u.hasOwnProperty(_)&&null!==u[_]&&(c[_]=u[_])}}catch(r){p.e(r)}finally{p.f()}return c},l=[],p=["layout","site_part","error","document_name","document_type","view_type_clicked","tag","sort_direction","sort_type","action","grid_location","kit_name","page_source","element_position","element","event_type","modal_type","method","status","step","item","category","element_location","search_term","section","site_area"],_={},y={};!function init(){c(p,y),c(l,_);var u=r.split("/");_.placement=u[0],_.event=u[1],Object.keys(y).length&&(_.details=y)}(),$e.run(r,_)}},6269:(r,u,c)=>{"use strict";var l=c(12470).__,p=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=AnnouncementsHeader;var _=p(c(41594)),y=p(c(62688));function AnnouncementsHeader(r){var u=r.onClose;return _.default.createElement("div",{className:"announcements-heading-container"},_.default.createElement("i",{className:"eicon-elementor","aria-hidden":"true"}),_.default.createElement("span",{className:"heading-title"},l("Notifications","elementor")),_.default.createElement("button",{className:"close-button",onClick:function onClick(){return u("close")}},_.default.createElement("i",{className:"eicon-close","aria-hidden":"true"})))}AnnouncementsHeader.propTypes={onClose:y.default.func.isRequired}},7470:(r,u,c)=>{"use strict";var l=c(75206);u.createRoot=l.createRoot,u.hydrateRoot=l.hydrateRoot},9535:(r,u,c)=>{var l=c(89736);function _regenerator(){var u,c,p="function"==typeof Symbol?Symbol:{},_=p.iterator||"@@iterator",y=p.toStringTag||"@@toStringTag";function i(r,p,_,y){var v=p&&p.prototype instanceof Generator?p:Generator,b=Object.create(v.prototype);return l(b,"_invoke",function(r,l,p){var _,y,v,b=0,x=p||[],h=!1,g={p:0,n:0,v:u,a:d,f:d.bind(u,4),d:function d(r,c){return _=r,y=0,v=u,g.n=c,m}};function d(r,l){for(y=r,v=l,c=0;!h&&b&&!p&&c<x.length;c++){var p,_=x[c],O=g.p,w=_[2];r>3?(p=w===l)&&(v=_[(y=_[4])?5:(y=3,3)],_[4]=_[5]=u):_[0]<=O&&((p=r<2&&O<_[1])?(y=0,g.v=l,g.n=_[1]):O<w&&(p=r<3||_[0]>l||l>w)&&(_[4]=r,_[5]=l,g.n=w,y=0))}if(p||r>1)return m;throw h=!0,l}return function(p,x,O){if(b>1)throw TypeError("Generator is already running");for(h&&1===x&&d(x,O),y=x,v=O;(c=y<2?u:v)||!h;){_||(y?y<3?(y>1&&(g.n=-1),d(y,v)):g.n=v:g.v=v);try{if(b=2,_){if(y||(p="next"),c=_[p]){if(!(c=c.call(_,v)))throw TypeError("iterator result is not an object");if(!c.done)return c;v=c.value,y<2&&(y=0)}else 1===y&&(c=_.return)&&c.call(_),y<2&&(v=TypeError("The iterator does not provide a '"+p+"' method"),y=1);_=u}else if((c=(h=g.n<0)?v:r.call(l,g))!==m)break}catch(r){_=u,y=1,v=r}finally{b=1}}return{value:c,done:h}}}(r,_,y),!0),b}var m={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}c=Object.getPrototypeOf;var v=[][_]?c(c([][_]())):(l(c={},_,function(){return this}),c),b=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(v);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,l(r,y,"GeneratorFunction")),r.prototype=Object.create(b),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,l(b,"constructor",GeneratorFunctionPrototype),l(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",l(GeneratorFunctionPrototype,y,"GeneratorFunction"),l(b),l(b,y,"Generator"),l(b,_,function(){return this}),l(b,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},10564:r=>{function _typeof(u){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(u)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10739:r=>{r.exports=function _objectWithoutPropertiesLoose(r,u){if(null==r)return{};var c={};for(var l in r)if({}.hasOwnProperty.call(r,l)){if(-1!==u.indexOf(l))continue;c[l]=r[l]}return c},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,u,c)=>{var l=c(10564).default;r.exports=function toPrimitive(r,u){if("object"!=l(r)||!r)return r;var c=r[Symbol.toPrimitive];if(void 0!==c){var p=c.call(r,u||"default");if("object"!=l(p))return p;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},15118:(r,u,c)=>{var l=c(10564).default,p=c(36417);r.exports=function _possibleConstructorReturn(r,u){if(u&&("object"==l(u)||"function"==typeof u))return u;if(void 0!==u)throw new TypeError("Derived constructors may only return object or undefined");return p(r)},r.exports.__esModule=!0,r.exports.default=r.exports},18791:(r,u,c)=>{"use strict";var l=c(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;_interopRequireWildcard(c(41594));var p=_interopRequireWildcard(c(75206)),_=c(7470);function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var c=new WeakMap,p=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var _,y,m={__proto__:null,default:r};if(null===r||"object"!=l(r)&&"function"!=typeof r)return m;if(_=u?p:c){if(_.has(r))return _.get(r);_.set(r,m)}for(var v in r)"default"!==v&&{}.hasOwnProperty.call(r,v)&&((y=(_=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,v))&&(y.get||y.set)?_(m,v,y):m[v]=r[v]);return m})(r,u)}u.default={render:function render(r,u){var c;try{var l=(0,_.createRoot)(u);l.render(r),c=function unmountFunction(){l.unmount()}}catch(l){p.render(r,u),c=function unmountFunction(){p.unmountComponentAtNode(u)}}return{unmount:c}}}},18821:(r,u,c)=>{var l=c(70569),p=c(65474),_=c(37744),y=c(11018);r.exports=function _slicedToArray(r,u){return l(r)||p(r,u)||_(r,u)||y()},r.exports.__esModule=!0,r.exports.default=r.exports},19734:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=AnnouncementFooter;var p=l(c(41594)),_=l(c(62688));function AnnouncementFooter(r){var u=r.buttons,c=r.onClose;return p.default.createElement("div",{className:"announcement-footer-container"},Object.values(u).map(function(r,u){return p.default.createElement("a",{key:"button".concat(u),className:"button-item ".concat(r.variant),href:r.url,target:r.target,onClick:function onClick(){return c("cta")}},r.label)}))}AnnouncementFooter.propTypes={buttons:_.default.oneOfType([_.default.array,_.default.object]),onClose:_.default.func.isRequired}},28914:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=l(c(85707)),_=l(c(39805)),y=l(c(40989)),m=l(c(15118)),v=l(c(29402)),b=l(c(87861));function ownKeys(r,u){var c=Object.keys(r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);u&&(l=l.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),c.push.apply(c,l)}return c}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var c=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(c),!0).forEach(function(u){(0,p.default)(r,u,c[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(c)):ownKeys(Object(c)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(c,u))})}return r}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function EComponent(){return(0,_.default)(this,EComponent),function _callSuper(r,u,c){return u=(0,v.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,c||[],(0,v.default)(r).constructor):u.apply(r,c))}(this,EComponent,arguments)}return(0,b.default)(EComponent,r),(0,y.default)(EComponent,[{key:"getNamespace",value:function getNamespace(){return"announcement"}},{key:"defaultCommands",value:function defaultCommands(){return["close","cta","impression"].reduce(function(r,u){return _objectSpread(_objectSpread({},r),{},(0,p.default)({},u,function(){}))},{})}}])}($e.modules.ComponentBase)},29402:r=>{function _getPrototypeOf(u){return r.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},r.exports.__esModule=!0,r.exports.default=r.exports,_getPrototypeOf(u)}r.exports=_getPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},33929:(r,u,c)=>{var l=c(67114),p=c(89736);r.exports=function AsyncIterator(r,u){function n(c,p,_,y){try{var m=r[c](p),v=m.value;return v instanceof l?u.resolve(v.v).then(function(r){n("next",r,_,y)},function(r){n("throw",r,_,y)}):u.resolve(v).then(function(r){m.value=r,_(m)},function(r){return n("throw",r,_,y)})}catch(r){y(r)}}var c;this.next||(p(AsyncIterator.prototype),p(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),p(this,"_invoke",function(r,l,p){function f(){return new u(function(u,c){n(r,p,u,c)})}return c=c?c.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},36417:r=>{r.exports=function _assertThisInitialized(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r},r.exports.__esModule=!0,r.exports.default=r.exports},37744:(r,u,c)=>{var l=c(78113);r.exports=function _unsupportedIterableToArray(r,u){if(r){if("string"==typeof r)return l(r,u);var c={}.toString.call(r).slice(8,-1);return"Object"===c&&r.constructor&&(c=r.constructor.name),"Map"===c||"Set"===c?Array.from(r):"Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?l(r,u):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},39805:r=>{r.exports=function _classCallCheck(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")},r.exports.__esModule=!0,r.exports.default=r.exports},40362:(r,u,c)=>{"use strict";var l=c(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,r.exports=function(){function shim(r,u,c,p,_,y){if(y!==l){var m=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw m.name="Invariant Violation",m}}function getShim(){return shim}shim.isRequired=shim;var r={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return r.PropTypes=r,r}},40453:(r,u,c)=>{var l=c(10739);r.exports=function _objectWithoutProperties(r,u){if(null==r)return{};var c,p,_=l(r,u);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(r);for(p=0;p<y.length;p++)c=y[p],-1===u.indexOf(c)&&{}.propertyIsEnumerable.call(r,c)&&(_[c]=r[c])}return _},r.exports.__esModule=!0,r.exports.default=r.exports},40989:(r,u,c)=>{var l=c(45498);function _defineProperties(r,u){for(var c=0;c<u.length;c++){var p=u[c];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(r,l(p.key),p)}}r.exports=function _createClass(r,u,c){return u&&_defineProperties(r.prototype,u),c&&_defineProperties(r,c),Object.defineProperty(r,"prototype",{writable:!1}),r},r.exports.__esModule=!0,r.exports.default=r.exports},41594:r=>{"use strict";r.exports=React},42601:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=AnnouncementBody;var p=l(c(41594)),_=l(c(62688));function AnnouncementBody(r){var u=r.announcement,c=u.title,l=u.description,_=u.media;return p.default.createElement("div",{className:"announcement-body-container"},"image"===_.type&&p.default.createElement("div",{className:"announcement-body-media announcement-body-".concat(_.type)},p.default.createElement("img",{src:_.src,alt:"Announcement"})),p.default.createElement("div",{className:"announcement-body-content"},p.default.createElement("div",{className:"announcement-body-title"},c),p.default.createElement("div",{className:"announcement-body-description",dangerouslySetInnerHTML:{__html:l}})))}AnnouncementBody.propTypes={announcement:_.default.object.isRequired}},45498:(r,u,c)=>{var l=c(10564).default,p=c(11327);r.exports=function toPropertyKey(r){var u=p(r,"string");return"symbol"==l(u)?u:u+""},r.exports.__esModule=!0,r.exports.default=r.exports},46313:(r,u,c)=>{var l=c(9535),p=c(33929);r.exports=function _regeneratorAsyncGen(r,u,c,_,y){return new p(l().w(r,u,c,_),y||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},53051:(r,u,c)=>{var l=c(67114),p=c(9535),_=c(62507),y=c(46313),m=c(33929),v=c(95315),b=c(66961);function _regeneratorRuntime(){"use strict";var u=p(),c=u.m(_regeneratorRuntime),x=(Object.getPrototypeOf?Object.getPrototypeOf(c):c.__proto__).constructor;function n(r){var u="function"==typeof r&&r.constructor;return!!u&&(u===x||"GeneratorFunction"===(u.displayName||u.name))}var h={throw:1,return:2,break:3,continue:3};function a(r){var u,c;return function(l){u||(u={stop:function stop(){return c(l.a,2)},catch:function _catch(){return l.v},abrupt:function abrupt(r,u){return c(l.a,h[r],u)},delegateYield:function delegateYield(r,p,_){return u.resultName=p,c(l.d,b(r),_)},finish:function finish(r){return c(l.f,r)}},c=function t(r,c,p){l.p=u.prev,l.n=u.next;try{return r(c,p)}finally{u.next=l.n}}),u.resultName&&(u[u.resultName]=l.v,u.resultName=void 0),u.sent=l.v,u.next=l.n;try{return r.call(this,u)}finally{l.p=u.prev,l.n=u.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,c,l,p){return u.w(a(r),c,l,p&&p.reverse())},isGeneratorFunction:n,mark:u.m,awrap:function awrap(r,u){return new l(r,u)},AsyncIterator:m,async:function async(r,u,c,l,p){return(n(u)?y:_)(a(r),u,c,l,p)},keys:v,values:b}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},56441:r=>{"use strict";r.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},58155:r=>{function asyncGeneratorStep(r,u,c,l,p,_,y){try{var m=r[_](y),v=m.value}catch(r){return void c(r)}m.done?u(v):Promise.resolve(v).then(l,p)}r.exports=function _asyncToGenerator(r){return function(){var u=this,c=arguments;return new Promise(function(l,p){var _=r.apply(u,c);function _next(r){asyncGeneratorStep(_,l,p,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(_,l,p,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},58400:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"Announcement",{enumerable:!0,get:function get(){return _.default}}),Object.defineProperty(u,"AnnouncementBody",{enumerable:!0,get:function get(){return m.default}}),Object.defineProperty(u,"AnnouncementFooter",{enumerable:!0,get:function get(){return v.default}}),Object.defineProperty(u,"Announcements",{enumerable:!0,get:function get(){return p.default}}),Object.defineProperty(u,"AnnouncementsHeader",{enumerable:!0,get:function get(){return y.default}}),Object.defineProperty(u,"Overlay",{enumerable:!0,get:function get(){return b.default}});var p=l(c(93016)),_=l(c(84701)),y=l(c(6269)),m=l(c(42601)),v=l(c(19734)),b=l(c(70048))},61790:(r,u,c)=>{var l=c(53051)();r.exports=l;try{regeneratorRuntime=l}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=l:Function("r","regeneratorRuntime = r")(l)}},62507:(r,u,c)=>{var l=c(46313);r.exports=function _regeneratorAsync(r,u,c,p,_){var y=l(r,u,c,p,_);return y.next().then(function(r){return r.done?r.value:y.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},62688:(r,u,c)=>{r.exports=c(40362)()},65474:r=>{r.exports=function _iterableToArrayLimit(r,u){var c=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=c){var l,p,_,y,m=[],v=!0,b=!1;try{if(_=(c=c.call(r)).next,0===u){if(Object(c)!==c)return;v=!1}else for(;!(v=(l=_.call(c)).done)&&(m.push(l.value),m.length!==u);v=!0);}catch(r){b=!0,p=r}finally{try{if(!v&&null!=c.return&&(y=c.return(),Object(y)!==y))return}finally{if(b)throw p}}return m}},r.exports.__esModule=!0,r.exports.default=r.exports},66961:(r,u,c)=>{var l=c(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var u=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],c=0;if(u)return u.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&c>=r.length&&(r=void 0),{value:r&&r[c++],done:!r}}}}throw new TypeError(l(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,u){this.v=r,this.k=u},r.exports.__esModule=!0,r.exports.default=r.exports},70048:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=function Overlay(){return p.default.createElement("div",{className:"announcements-screen-overlay"})};var p=l(c(41594))},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},75206:r=>{"use strict";r.exports=ReactDOM},78113:r=>{r.exports=function _arrayLikeToArray(r,u){(null==u||u>r.length)&&(u=r.length);for(var c=0,l=Array(u);c<u;c++)l[c]=r[c];return l},r.exports.__esModule=!0,r.exports.default=r.exports},84701:(r,u,c)=>{"use strict";var l=c(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=Announcement;var p=l(c(41594)),_=l(c(40453)),y=c(58400),m=l(c(62688)),v=["cta"];function Announcement(r){var u=r.announcement,c=r.onClose,l=u.cta,m=(0,_.default)(u,v);return p.default.createElement("div",{className:"announcement-item"},p.default.createElement(y.AnnouncementBody,{announcement:m}),p.default.createElement(y.AnnouncementFooter,{buttons:l,onClose:c}))}Announcement.propTypes={announcement:m.default.object.isRequired,onClose:m.default.func.isRequired}},85707:(r,u,c)=>{var l=c(45498);r.exports=function _defineProperty(r,u,c){return(u=l(u))in r?Object.defineProperty(r,u,{value:c,enumerable:!0,configurable:!0,writable:!0}):r[u]=c,r},r.exports.__esModule=!0,r.exports.default=r.exports},87861:(r,u,c)=>{var l=c(91270);r.exports=function _inherits(r,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),u&&l(r,u)},r.exports.__esModule=!0,r.exports.default=r.exports},89736:r=>{function _regeneratorDefine(u,c,l,p){var _=Object.defineProperty;try{_({},"",{})}catch(u){_=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,u,c,l){if(u)_?_(r,u,{value:c,enumerable:!l,configurable:!l,writable:!l}):r[u]=c;else{var p=function o(u,c){_regeneratorDefine(r,u,function(r){return this._invoke(u,c,r)})};p("next",0),p("throw",1),p("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(u,c,l,p)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},91270:r=>{function _setPrototypeOf(u,c){return r.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,u){return r.__proto__=u,r},r.exports.__esModule=!0,r.exports.default=r.exports,_setPrototypeOf(u,c)}r.exports=_setPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},93016:(r,u,c)=>{"use strict";var l=c(96784),p=c(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=Announcements;var _=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var c=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var _,y,m={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return m;if(_=u?l:c){if(_.has(r))return _.get(r);_.set(r,m)}for(var v in r)"default"!==v&&{}.hasOwnProperty.call(r,v)&&((y=(_=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,v))&&(y.get||y.set)?_(m,v,y):m[v]=r[v]);return m}(r,u)}(c(41594)),y=l(c(85707)),m=l(c(18821)),v=c(58400),b=c(3073),x=l(c(62688));function ownKeys(r,u){var c=Object.keys(r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);u&&(l=l.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),c.push.apply(c,l)}return c}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var c=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(c),!0).forEach(function(u){(0,y.default)(r,u,c[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(c)):ownKeys(Object(c)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(c,u))})}return r}function Announcements(r){var u=r.announcements,c=r.unMount,l=(0,_.useState)(0),p=(0,m.default)(l,2),y=p[0],x=p[1],h=Object.values(u)[0].title||"";(0,_.useEffect)(function(){var r=setTimeout(function(){O("impression",{event_name:"element_impression",element_type:"popup",eventNonInteraction:1})},200);return function(){return clearTimeout(r)}},[u]);var g=function onCloseHandle(r){if(O(r,{event_name:"element_click",element_type:"button"}),u.shift(),0===u.length)return c();x(y+1)},O=function eventTrackingHandle(r,u){var c=_objectSpread(_objectSpread({},{event:"fireEvent",eventCategory:"editor",eventAction:"whats new popup",eventLabel:r,eventLabel2:h,event_action:r,event_location:"popup",event_context:"whats new",event_subcontext:h,element_id:"e-announcements-root"}),u);(0,b.appsEventTrackingDispatch)("announcement/".concat(r),c)};return _.default.createElement("div",{className:"announcements-container"},_.default.createElement(v.AnnouncementsHeader,{onClose:g}),_.default.createElement(v.Announcement,{key:"announcement-".concat(y),announcement:u[0],onClose:g}))}Announcements.propTypes={announcements:x.default.oneOfType([x.default.array,x.default.object]).isRequired,unMount:x.default.func.isRequired}},95315:r=>{r.exports=function _regeneratorKeys(r){var u=Object(r),c=[];for(var l in u)c.unshift(l);return function e(){for(;c.length;)if((l=c.pop())in u)return e.value=l,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports}},u={};function __webpack_require__(c){var l=u[c];if(void 0!==l)return l.exports;var p=u[c]={exports:{}};return r[c](p,p.exports,__webpack_require__),p.exports}(()=>{"use strict";var r=__webpack_require__(96784);var u=r(__webpack_require__(41594)),c=r(__webpack_require__(61790)),l=r(__webpack_require__(58155)),p=r(__webpack_require__(39805)),_=r(__webpack_require__(40989)),y=r(__webpack_require__(18791)),m=__webpack_require__(58400),v=r(__webpack_require__(28914));new(function(){return(0,_.default)(function AnnouncementIndex(){(0,p.default)(this,AnnouncementIndex),this.initAnnouncement()},[{key:"initAnnouncement",value:(r=(0,l.default)(c.default.mark(function _callee(){var r,l,p,_,b;return c.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(l=document.getElementById("e-announcements-root"),(p=null===(r=window.elementorAnnouncementsConfig)||void 0===r?void 0:r.announcements)&&l){c.next=1;break}return c.abrupt("return");case 1:return c.next=2,$e.components.register(new v.default);case 2:_=y.default.render(u.default.createElement(u.default.Fragment,null,u.default.createElement(m.Overlay,null),u.default.createElement(m.Announcements,{announcements:p,unMount:function unMount(){b(),l.remove()}})),l),b=_.unmount;case 3:case"end":return c.stop()}},_callee)})),function initAnnouncement(){return r.apply(this,arguments)})}]);var r}())})()})();