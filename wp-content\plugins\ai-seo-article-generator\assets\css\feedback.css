/**
 * Feedback System Styles
 */

/* Feedback page layout */
.ai-seo-feedback-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.ai-seo-feedback-form-section {
    flex: 1;
    background: #fff;
    padding: 25px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.ai-seo-feedback-whatsapp-section {
    width: 350px;
    background: #fff;
    padding: 25px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

/* WhatsApp button styling */
.ai-seo-whatsapp-button {
    background: #25d366 !important;
    color: #fff !important;
    border: none !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    justify-content: center;
    transition: background 0.3s ease;
}

.ai-seo-whatsapp-button:hover {
    background: #128c7e !important;
}

.ai-seo-whatsapp-button .dashicons-whatsapp:before {
    content: "\f227";
    font-size: 24px;
}

/* Quick actions */
.ai-seo-quick-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

.ai-seo-quick-actions h3 {
    margin-bottom: 15px;
}

.ai-seo-quick-actions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ai-seo-quick-actions li {
    margin-bottom: 10px;
}

.ai-seo-quick-actions a {
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e1e1e1;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.ai-seo-quick-actions a:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

/* Floating WhatsApp button */
.ai-seo-whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 9999;
}

.ai-seo-whatsapp-float-button {
    width: 60px;
    height: 60px;
    background: #25d366;
    border: none;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-seo-whatsapp-float-button:hover {
    background: #128c7e;
    transform: scale(1.1);
}

.ai-seo-whatsapp-float-button .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
}

/* Form styling */
.ai-seo-feedback-form-section .form-table th {
    width: 150px;
}

.ai-seo-feedback-form-section textarea {
    width: 100%;
    max-width: 600px;
}

.ai-seo-feedback-form-section select,
.ai-seo-feedback-form-section input[type="text"],
.ai-seo-feedback-form-section input[type="email"] {
    width: 100%;
    max-width: 400px;
}

/* Responsive design */
@media screen and (max-width: 1200px) {
    .ai-seo-feedback-container {
        flex-direction: column;
    }
    
    .ai-seo-feedback-whatsapp-section {
        width: 100%;
    }
}

@media screen and (max-width: 600px) {
    .ai-seo-whatsapp-float {
        bottom: 20px;
        right: 20px;
    }
    
    .ai-seo-whatsapp-float-button {
        width: 50px;
        height: 50px;
    }
    
    .ai-seo-whatsapp-float-button .dashicons {
        font-size: 24px;
        width: 24px;
        height: 24px;
    }
}