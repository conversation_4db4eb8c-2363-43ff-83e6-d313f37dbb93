<?php
/**
 * Feedback and Support functionality
 *
 * @package AI_SEO_Article_Generator
 * @since 1.0.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AI_SEO_Article_Generator_Feedback {
    
    /**
     * WhatsApp number for support
     */
    const WHATSAPP_NUMBER = '972546330446';
    
    /**
     * Support email
     */
    const SUPPORT_EMAIL = '<EMAIL>';
    
    /**
     * Initialize the feedback system
     */
    public function __construct() {
        // Add menu item
        add_action('admin_menu', array($this, 'add_feedback_menu'), 20);
        
        // Handle form submission
        add_action('admin_post_ai_seo_feedback_submit', array($this, 'handle_feedback_submission'));
        
        // Add WhatsApp button to plugin pages
        add_action('admin_footer', array($this, 'add_whatsapp_button'));
        
        // Enqueue assets
        add_action('admin_enqueue_scripts', array($this, 'enqueue_assets'));
    }
    
    /**
     * Add feedback menu item
     */
    public function add_feedback_menu() {
        add_submenu_page(
            'ai-seo-article-generator',
            __('Support & Feedback', 'ai-seo-article-generator'),
            __('Support & Feedback', 'ai-seo-article-generator'),
            'manage_options',
            'ai-seo-article-generator-feedback',
            array($this, 'render_feedback_page')
        );
    }
    
    /**
     * Enqueue feedback assets
     */
    public function enqueue_assets($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'ai-seo-article-generator') === false) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'ai-seo-article-generator-feedback',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/feedback.css',
            array(),
            AI_SEO_ARTICLE_GENERATOR_VERSION
        );
        
        // Enqueue JS
        wp_enqueue_script(
            'ai-seo-article-generator-feedback',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/feedback.js',
            array('jquery'),
            AI_SEO_ARTICLE_GENERATOR_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('ai-seo-article-generator-feedback', 'aiSeoFeedback', array(
            'whatsappNumber' => self::WHATSAPP_NUMBER,
            'pluginVersion' => AI_SEO_ARTICLE_GENERATOR_VERSION,
            'strings' => array(
                'messageSent' => __('Message sent successfully!', 'ai-seo-article-generator'),
                'messageError' => __('Error sending message. Please try again.', 'ai-seo-article-generator'),
                'whatsappMessage' => __('Welcome to AI SEO Article Generator Support, how can i help? :)', 'ai-seo-article-generator')
            )
        ));
    }
    
    /**
     * Render feedback page
     */
    public function render_feedback_page() {
        // Check if message was sent
        $message_sent = isset($_GET['message']) && $_GET['message'] === 'sent';
        $error = isset($_GET['error']) ? sanitize_text_field($_GET['error']) : '';
        
        ?>
        <div class="wrap ai-seo-article-generator-wrap">
            <h1><?php esc_html_e('Support & Feedback', 'ai-seo-article-generator'); ?></h1>
            
            <?php if ($message_sent): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php esc_html_e('Your message has been sent successfully! We\'ll get back to you soon.', 'ai-seo-article-generator'); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="notice notice-error is-dismissible">
                    <p><?php esc_html_e('There was an error sending your message. Please try again.', 'ai-seo-article-generator'); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="ai-seo-feedback-container">
                <div class="ai-seo-feedback-form-section">
                    <h2><?php esc_html_e('Send us a Message', 'ai-seo-article-generator'); ?></h2>
                    <p><?php esc_html_e('Have a question, found a bug, or want to request a feature? Let us know!', 'ai-seo-article-generator'); ?></p>
                    
                    <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                        <?php wp_nonce_field('ai_seo_feedback_nonce', 'feedback_nonce'); ?>
                        <input type="hidden" name="action" value="ai_seo_feedback_submit">
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="feedback_type"><?php esc_html_e('Type', 'ai-seo-article-generator'); ?></label>
                                </th>
                                <td>
                                    <select name="feedback_type" id="feedback_type" required>
                                        <option value=""><?php esc_html_e('Select type...', 'ai-seo-article-generator'); ?></option>
                                        <option value="bug"><?php esc_html_e('Bug Report', 'ai-seo-article-generator'); ?></option>
                                        <option value="feature"><?php esc_html_e('Feature Request', 'ai-seo-article-generator'); ?></option>
                                        <option value="support"><?php esc_html_e('Support Question', 'ai-seo-article-generator'); ?></option>
                                        <option value="review"><?php esc_html_e('Review/Testimonial', 'ai-seo-article-generator'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="feedback_subject"><?php esc_html_e('Subject', 'ai-seo-article-generator'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="feedback_subject" id="feedback_subject" class="regular-text" required>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="feedback_message"><?php esc_html_e('Message', 'ai-seo-article-generator'); ?></label>
                                </th>
                                <td>
                                    <textarea name="feedback_message" id="feedback_message" rows="6" class="large-text" required></textarea>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="feedback_email"><?php esc_html_e('Your Email', 'ai-seo-article-generator'); ?></label>
                                </th>
                                <td>
                                    <input type="email" name="feedback_email" id="feedback_email" class="regular-text" value="<?php echo esc_attr(wp_get_current_user()->user_email); ?>" required>
                                    <p class="description"><?php esc_html_e('We\'ll use this to respond to your message', 'ai-seo-article-generator'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <p class="submit">
                            <button type="submit" class="button button-primary">
                                <?php esc_html_e('Send Message', 'ai-seo-article-generator'); ?>
                            </button>
                        </p>
                    </form>
                </div>
                
                <div class="ai-seo-feedback-whatsapp-section">
                    <h2><?php esc_html_e('Need Instant Support?', 'ai-seo-article-generator'); ?></h2>
                    <p><?php esc_html_e('Chat with us on WhatsApp for immediate assistance!', 'ai-seo-article-generator'); ?></p>
                    
                    <button class="button button-hero ai-seo-whatsapp-button" onclick="aiSeoFeedback.openWhatsApp()">
                        <span class="dashicons dashicons-whatsapp"></span>
                        <?php esc_html_e('Chat on WhatsApp', 'ai-seo-article-generator'); ?>
                    </button>
                    
                    <div class="ai-seo-quick-actions">
                        <h3><?php esc_html_e('Quick Actions', 'ai-seo-article-generator'); ?></h3>
                        <ul>
                            <li>
                                <a href="#" onclick="aiSeoFeedback.quickAction('bug'); return false;">
                                    <span class="dashicons dashicons-warning"></span>
                                    <?php esc_html_e('Report a Bug', 'ai-seo-article-generator'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" onclick="aiSeoFeedback.quickAction('feature'); return false;">
                                    <span class="dashicons dashicons-lightbulb"></span>
                                    <?php esc_html_e('Request a Feature', 'ai-seo-article-generator'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="https://wordpress.org/support/plugin/ai-seo-article-generator/reviews/#new-post" target="_blank">
                                    <span class="dashicons dashicons-star-filled"></span>
                                    <?php esc_html_e('Leave a Review', 'ai-seo-article-generator'); ?>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Handle feedback form submission
     */
    public function handle_feedback_submission() {
        // Verify nonce
        if (!isset($_POST['feedback_nonce']) || !wp_verify_nonce($_POST['feedback_nonce'], 'ai_seo_feedback_nonce')) {
            wp_die(__('Security check failed', 'ai-seo-article-generator'));
        }
        
        // Check capability
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to submit feedback', 'ai-seo-article-generator'));
        }
        
        // Sanitize input
        $type = sanitize_text_field($_POST['feedback_type']);
        $subject = sanitize_text_field($_POST['feedback_subject']);
        $message = sanitize_textarea_field($_POST['feedback_message']);
        $email = sanitize_email($_POST['feedback_email']);
        
        // Prepare email content
        $to = self::SUPPORT_EMAIL;
        $email_subject = sprintf('[AI SEO Generator - %s] %s', $this->get_type_label($type), $subject);
        
        $email_body = sprintf(
            "Type: %s\n" .
            "From: %s\n" .
            "Site: %s\n" .
            "Plugin Version: %s\n" .
            "WordPress Version: %s\n" .
            "PHP Version: %s\n\n" .
            "Message:\n%s",
            $this->get_type_label($type),
            $email,
            get_site_url(),
            AI_SEO_ARTICLE_GENERATOR_VERSION,
            get_bloginfo('version'),
            phpversion(),
            $message
        );
        
        $headers = array(
            'Reply-To: ' . $email
        );
        
        // Send email
        $sent = wp_mail($to, $email_subject, $email_body, $headers);
        
        // Redirect back with message
        if ($sent) {
            wp_redirect(add_query_arg('message', 'sent', admin_url('admin.php?page=ai-seo-article-generator-feedback')));
        } else {
            wp_redirect(add_query_arg('error', 'send_failed', admin_url('admin.php?page=ai-seo-article-generator-feedback')));
        }
        exit;
    }
    
    /**
     * Get label for feedback type
     */
    private function get_type_label($type) {
        $labels = array(
            'bug' => __('Bug Report', 'ai-seo-article-generator'),
            'feature' => __('Feature Request', 'ai-seo-article-generator'),
            'support' => __('Support', 'ai-seo-article-generator'),
            'review' => __('Review', 'ai-seo-article-generator')
        );
        
        return isset($labels[$type]) ? $labels[$type] : __('General', 'ai-seo-article-generator');
    }
    
    /**
     * Add WhatsApp floating button
     */
    public function add_whatsapp_button() {
        $screen = get_current_screen();
        
        // Only show on our plugin pages
        if (!$screen || strpos($screen->id, 'ai-seo-article-generator') === false) {
            return;
        }
        
        // Don't show on feedback page (already has button)
        if ($screen->id === 'ai-seo-article-generator_page_ai-seo-article-generator-feedback') {
            return;
        }
        
        ?>
        <div class="ai-seo-whatsapp-float">
            <button class="ai-seo-whatsapp-float-button" onclick="aiSeoFeedback.openWhatsApp()" title="<?php esc_attr_e('Chat on WhatsApp', 'ai-seo-article-generator'); ?>">
                <span class="dashicons dashicons-whatsapp"></span>
            </button>
        </div>
        <?php
    }
}

// Initialize feedback system
new AI_SEO_Article_Generator_Feedback();