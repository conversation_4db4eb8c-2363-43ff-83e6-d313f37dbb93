/*! elementor - v3.31.0 - 11-08-2025 */
:root {
  --direction-multiplier: 1;
}

html[dir=rtl],
body.rtl {
  --direction-multiplier: -1;
}

.elementor-hidden {
  display: none;
}

.elementor-visibility-hidden {
  visibility: hidden;
}

.elementor-screen-only,
.screen-reader-text,
.screen-reader-text span,
.ui-helper-hidden-accessible {
  position: absolute;
  top: -10000em;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.elementor-clearfix:after {
  content: "";
  display: block;
  clear: both;
  width: 0;
  height: 0;
}

.e-logo-wrapper {
  background: var(--e-a-bg-logo);
  display: inline-block;
  padding: 0.75em;
  border-radius: 50%;
  line-height: 1;
}
.e-logo-wrapper i {
  color: var(--e-a-color-logo);
  font-size: 1em;
}

.elementor-control-unit-1 {
  width: 27px;
}

.elementor-control-unit-2 {
  width: 54px;
}

.elementor-control-unit-3 {
  width: 81px;
}

.elementor-control-unit-4 {
  width: 108px;
}

.elementor-control-unit-5 {
  max-width: 400px;
  width: 52%;
}

.elementor-tags-list {
  display: none;
  position: absolute;
  width: 260px;
  max-height: 300px;
  overflow: auto;
  padding-block-end: 5px;
  background-color: var(--e-a-bg-default);
  box-shadow: var(--e-a-popover-shadow);
  border: var(--e-a-border);
  border-radius: 3px;
  z-index: 10000;
}
.elementor-tags-list__group-title {
  font-weight: bold;
  font-size: 12px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}
.elementor-tags-list__group-title .eicon-info-circle {
  padding-inline-end: 5px;
  color: var(--e-a-color-primary-bold);
  font-size: 14px;
}
.elementor-tags-list__item {
  font-size: 10px;
  padding: 6px 15px;
  cursor: pointer;
}
.elementor-tags-list__item:before {
  content: ">";
  font-size: 8px;
  padding-inline-end: 5px;
}
.elementor-tags-list__item:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-tags-list__teaser {
  border-block-start: 2px solid var(--e-a-border-color);
  padding-block-start: 4px;
  margin-block-start: 4px;
}
.elementor-tags-list__teaser-text {
  padding: 2px 15px 8px;
  line-height: 1.5;
  font-size: 12px;
}
.elementor-tags-list__teaser-link {
  color: var(--e-a-color-primary-bold);
  text-decoration: underline;
  font-style: italic;
  font-weight: bold;
}

.elementor-dynamic-cover {
  display: flex;
  align-items: center;
  width: 100%;
  height: 27px;
  box-sizing: border-box;
}
.elementor-dynamic-cover__title {
  padding: 0 8px;
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-dynamic-cover__settings, .elementor-dynamic-cover__remove {
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.elementor-dynamic-cover__settings:hover, .elementor-dynamic-cover__remove:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-control-type-wysiwyg .elementor-dynamic-cover {
  margin-block-start: 10px;
}

.elementor-tag-settings-popup {
  position: absolute;
  width: 260px;
  background-color: var(--e-a-bg-default);
  border: var(--e-a-border);
  box-shadow: var(--e-a-popover-shadow);
  z-index: 1;
}
.elementor-tag-settings-popup:before {
  content: "";
  position: absolute;
  inset-block-start: -20px;
  inset-inline-start: 5px;
  border: 10px solid transparent;
  border-block-end-color: var(--e-a-border-color);
}
.elementor-tag-settings-popup .elementor-control-type-section:first-child {
  margin: 0;
}

.elementor-tag-controls-stack-empty {
  background-color: var(--e-a-bg-default);
  padding: 10px;
  font-size: 13px;
  text-align: center;
}

.elementor-control-dynamic input {
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-dynamic-switcher {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  height: 27px;
  cursor: pointer;
  border: var(--e-a-border-bold);
  background: var(--e-a-bg-default);
  border-inline-start-width: 0;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  transition: var(--e-a-transition-hover);
}
.elementor-control-dynamic-switcher:hover {
  background-color: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
.elementor-control-dynamic-switcher.e-control-tool {
  height: 20px;
  border: 0;
}
.elementor-control-dynamic-switcher-wrapper {
  display: flex;
}
.elementor-control-dynamic-switcher .eicon-database {
  font-size: 12px;
}
.elementor-control-dynamic-value .elementor-control-tag-area,
.elementor-control-dynamic-value .elementor-control-dynamic-switcher,
.elementor-control-dynamic-value .e-global__popover-toggle {
  display: none;
}

.elementor-panel-box-content {
  padding: 20px 20px 10px;
}

.elementor-button {
  display: inline-block;
}

.elementor-panel-alert {
  background-color: var(--e-a-bg-primary);
  padding: 15px;
  border-inline-start: 3px solid var(--e-a-color-primary);
  position: relative;
  font-size: 12px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.5;
  text-align: start;
  border-start-start-radius: 0;
  border-start-end-radius: 3px;
  border-end-start-radius: 0;
  border-end-end-radius: 3px;
}
.elementor-panel-alert a {
  color: inherit;
}
.elementor-panel-alert.elementor-panel-alert-info {
  border-color: var(--e-a-color-info);
  background: var(--e-a-bg-info);
}
.elementor-panel-alert.elementor-panel-alert-success {
  border-color: var(--e-a-color-success);
  background: var(--e-a-bg-success);
}
.elementor-panel-alert.elementor-panel-alert-warning {
  border-inline-start: 3px solid var(--e-a-color-warning);
  background: var(--e-a-bg-warning);
}
.elementor-panel-alert.elementor-panel-alert-danger {
  border-color: var(--e-a-color-danger);
  background: var(--e-a-bg-danger);
}

.elementor-descriptor, .elementor-control-field-description {
  font-size: 11px;
  font-style: italic;
  line-height: 1.4;
  color: var(--e-a-color-txt-muted);
}

.elementor-controls-popover {
  display: none;
  position: absolute;
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
  left: 0;
  right: 0;
  margin: -4px auto 5px;
  padding-block-start: 15px;
  width: 90%;
  z-index: 10000;
  background-color: var(--e-a-bg-default);
}
.elementor-controls-popover:before {
  content: "";
  position: absolute;
  inset-block-start: -16px;
  inset-inline-end: 22px;
  border: 8px solid transparent;
  border-block-end-color: var(--e-a-bg-default);
}
.elementor-controls-popover div.elementor-control {
  background-color: transparent;
}
.elementor-controls-popover div.elementor-control:before {
  content: none;
}

#elementor-panel-global .elementor-nerd-box .elementor-nerd-box-icon {
  margin-block-start: 20px;
}

.elementor-control {
  --control-title-size: 12px;
  position: relative;
  padding: 0 20px 15px;
}
.elementor-control a {
  font-weight: 500;
  text-decoration: none;
  border-block-end: 1px dotted transparent;
  transition: all ease-in-out 0.3s;
}
.elementor-control a:hover {
  border-block-end-color: inherit;
}
.elementor-control .elementor-control-content {
  display: flex;
  flex-direction: column;
}
.elementor-control .elementor-control-title {
  font-size: var(--control-title-size);
  line-height: 1;
  margin-inline-end: 5px;
}
.elementor-control .elementor-control-title:empty {
  display: none;
}
.elementor-control .elementor-control-spinner {
  display: flex;
  align-items: center;
}
.elementor-control.elementor-control-type-divider {
  padding: 0;
}
.elementor-control.elementor-control-type-divider .elementor-control-content {
  margin-inline: 20px;
  border-width: 0;
  border-block-start: var(--e-a-border);
  background-color: var(--e-a-bg-default);
  height: 15px;
}
.elementor-control.elementor-control-separator-before {
  padding-block-start: 15px;
}
.elementor-control.elementor-control-separator-before:before {
  content: "";
  position: absolute;
  inset: 0 20px auto;
  height: 1px;
  background-color: var(--e-a-border-color);
}
.elementor-control.elementor-control-separator-after {
  padding-block-end: 15px;
}
.elementor-control.elementor-control-separator-after:after {
  content: "";
  position: absolute;
  inset: auto 20px 0;
  height: 1px;
  background-color: var(--e-a-border-color);
}
.elementor-control.elementor-control-separator-after + .elementor-control-type-tabs + .elementor-control-separator-default, .elementor-control.elementor-control-separator-after:not(.elementor-hidden-control) + .elementor-control-separator-default {
  padding-block-start: 15px;
}
.elementor-control.elementor-control-deprecated {
  color: var(--e-a-color-warning);
}
.elementor-control.elementor-control-deprecated .elementor-control-field-description {
  color: var(--e-a-color-warning);
}
.elementor-control.elementor-control-hidden-label > * > .elementor-control-title, .elementor-control.elementor-control-hidden-label > * > * > .elementor-control-title {
  display: none;
}
.elementor-control.elementor-hidden-control {
  display: none;
}
.elementor-control.elementor-control-type-heading .elementor-control-title {
  font-weight: bold;
  margin: 0;
}
body:not(.elementor-device-widescreen) .elementor-control.elementor-control-responsive-widescreen {
  display: none;
}
body:not(.elementor-device-desktop) .elementor-control.elementor-control-responsive-desktop {
  display: none;
}
body:not(.elementor-device-laptop) .elementor-control.elementor-control-responsive-laptop {
  display: none;
}
body:not(.elementor-device-tablet_extra) .elementor-control.elementor-control-responsive-tablet_extra {
  display: none;
}
body:not(.elementor-device-tablet) .elementor-control.elementor-control-responsive-tablet {
  display: none;
}
body:not(.elementor-device-mobile_extra) .elementor-control.elementor-control-responsive-mobile_extra {
  display: none;
}
body:not(.elementor-device-mobile) .elementor-control.elementor-control-responsive-mobile {
  display: none;
}
.elementor-control-shape_divider_top .elementor-visual-choice-element-image label, .elementor-control-shape_divider_bottom .elementor-visual-choice-element-image label {
  padding: 4px;
}
.elementor-control-shape_divider_top img, .elementor-control-shape_divider_bottom img {
  aspect-ratio: 4/1;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
.elementor-control-shape_divider_bottom img {
  rotate: X 180deg;
}
.elementor-control-custom_css_pro .elementor-nerd-box-message, .elementor-control-custom_attributes_pro .elementor-nerd-box-message {
  margin-block-start: 5px;
}

.elementor-control.e-open .elementor-panel-heading-toggle i, .elementor-control.elementor-active .elementor-panel-heading-toggle i,
.elementor-panel-category.e-open .elementor-panel-heading-toggle i,
.elementor-panel-category.elementor-active .elementor-panel-heading-toggle i,
.elementor-panel-scheme-item.e-open .elementor-panel-heading-toggle i,
.elementor-panel-scheme-item.elementor-active .elementor-panel-heading-toggle i {
  transform: rotate(90deg);
}
.elementor-control:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle i,
.elementor-panel-category:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle i,
.elementor-panel-scheme-item:not(.e-open):not(.elementor-active) .elementor-panel-heading-toggle i {
  scale: calc(1 * var(--direction-multiplier)) 1;
}

.elementor-panel-heading {
  display: flex;
  align-items: center;
  gap: 5px;
  height: 48px;
  padding-inline: 20px;
  width: 100%;
  border: 0;
  border-block-start: var(--e-a-border);
  border-block-start-width: 2px;
  background-color: transparent;
  color: var(--e-a-color-txt-accent);
  cursor: pointer;
}
.elementor-panel-heading-toggle {
  width: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.elementor-panel-heading-title {
  font-weight: bold;
}
.elementor-panel-heading-category-chip {
  margin-inline-start: auto;
  background-color: var(--e-a-bg-chip);
  border-radius: 100px;
  padding: 5px 8px;
}
.elementor-panel-heading-category-chip i {
  margin-inline-start: 4px;
}
.elementor-panel-heading-promotion {
  margin-inline-start: auto;
}
.elementor-panel-heading-promotion a {
  color: var(--e-a-color-accent-promotion);
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 13px;
}
.elementor-panel-heading-promotion a i {
  margin-inline-end: 4px;
  font-size: 14px;
}
.elementor-panel-heading-promotion a:hover {
  color: var(--e-a-color-accent-promotion);
}
.elementor-panel-heading:focus-visible {
  color: var(--e-a-color-txt-hover);
}

#elementor-controls .elementor-control-type-section:first-child .elementor-panel-heading,
#elementor-panel-page-settings-controls .elementor-control-type-section:first-child .elementor-panel-heading,
#elementor-panel-editorPreferences-settings-controls .elementor-control-type-section:first-child .elementor-panel-heading {
  border-block-start: none;
}

.elementor-control-field {
  display: flex;
  align-items: center;
}

.elementor-label-block > .elementor-control-content > .elementor-control-field {
  flex-wrap: wrap;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  width: 100%;
  max-width: 100%;
  margin-block-start: 10px;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper > .elementor-choices label {
  width: auto;
  flex: 1 1 27px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.elementor-label-block.elementor-control-hidden-label:not(.elementor-control-dynamic) > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
}
.elementor-label-block.elementor-control-hidden-label.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
}

.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-title {
  flex-shrink: 0;
  max-width: 60%;
}
.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-inline-start: auto;
}

.elementor-control-field-description {
  margin-block-start: 10px;
}

.elementor-group-control-attachment_alert .elementor-control-field-description {
  margin-block-start: 0;
}

.elementor-control-start-end .eicon-h-align-left,
.elementor-control-start-end .eicon-h-align-right {
  scale: calc(1 * var(--direction-multiplier)) 1;
}

.elementor-update-preview {
  margin: 15px 15px 0;
  display: flex;
  align-items: center;
}

.elementor-update-preview-button-wrapper {
  flex-grow: 1;
  text-align: end;
}

.elementor-update-preview-button {
  padding: 8px 15px;
}

.elementor-control-direction-ltr input,
.elementor-control-direction-ltr textarea {
  direction: ltr;
}
.elementor-control-direction-rtl input,
.elementor-control-direction-rtl textarea {
  direction: rtl;
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  inset-block-start: -0.8em;
  inset-inline-start: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-alert {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
}
.elementor-control-alert-heading {
  font-weight: bold;
}

.elementor-control-type-button .elementor-control-input-wrapper {
  text-align: end;
}
.elementor-control-type-button .elementor-button {
  width: auto;
}
.elementor-control-type-button .elementor-button.elementor-button-center {
  display: block;
  margin: 0 auto;
}

.elementor-control-type-choose.elementor-label-block .elementor-choices {
  width: 100%;
}

.elementor-choices {
  display: flex;
  height: 27px;
  line-height: 27px;
  text-align: center;
  border-spacing: 1px;
  border-radius: var(--e-a-border-radius);
  overflow: hidden;
}
.elementor-choices .elementor-choices-label {
  border-block-start: var(--e-a-border-bold);
  border-block-end: var(--e-a-border-bold);
  border-inline-start: var(--e-a-border-bold);
  border-inline-end: none;
  font-size: 12px;
  transition: var(--e-a-transition-hover);
  cursor: pointer;
  overflow: hidden;
}
.elementor-choices .elementor-choices-label:nth-child(2) {
  border-start-start-radius: var(--e-a-border-radius);
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-choices .elementor-choices-label:last-child {
  border-inline-end: var(--e-a-border-bold);
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-choices input {
  display: none;
}
.elementor-choices input:hover + .elementor-choices-label {
  background-color: var(--e-a-bg-hover);
}
.elementor-choices input.e-choose-placeholder + .elementor-choices-label, .elementor-choices input:checked + .elementor-choices-label {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
}

.elementor-label-inline .elementor-choices {
  justify-content: flex-end;
}

.elementor-control-type-color.e-control-global .pickr {
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  flex-shrink: 0;
  border-color: var(--e-a-border-color-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-color.e-control-global .pickr:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-color.e-control-global .e-global__popover-toggle:not(.e-global__popover-toggle--active) ~ .pickr {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-type-color .elementor-control-title {
  flex-grow: 1;
}
.elementor-control-type-color .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
}

.elementor-group-control-css-filter .elementor-slider {
  height: 6px;
  box-shadow: 0 0 1px 1px inset rgba(0, 0, 0, 0.2);
}
.elementor-group-control-css-filter .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-block-start: 0;
  margin-block-end: 5px;
}

.elementor-group-control-blur .elementor-slider {
  background: url("../images/blur.png");
  background-size: cover;
  background-position: center;
}

.elementor-group-control-contrast .elementor-slider {
  background: url("../images/contrast.png");
  background-size: 100% 100%;
}

.elementor-group-control-hue .elementor-slider {
  background-image: linear-gradient(to right, red, orange, yellow, greenyellow, limegreen, deepskyblue, blue, darkviolet 95%);
}

.elementor-group-control-saturate .elementor-slider {
  background-image: linear-gradient(to right, gray, red);
}

.elementor-group-control-brightness .elementor-slider {
  background-image: linear-gradient(to right, black, white);
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  inset-block-start: -0.8em;
  inset-inline-start: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-type-dimensions .elementor-control-dimensions {
  display: flex;
}
.elementor-control-type-dimensions li {
  flex: 1;
  transition: flex-grow 0.3s ease-in-out;
}
.elementor-control-type-dimensions li input,
.elementor-control-type-dimensions li .elementor-link-dimensions {
  display: block;
  text-align: center;
  width: 100%;
  height: 27px;
}
.elementor-control-type-dimensions li input {
  border-inline-start: none;
  border-radius: 0;
  padding: var(--e-a-border-radius);
}
.elementor-control-type-dimensions li input:focus {
  border-inline-start: var(--e-a-border-bold);
  margin-inline-start: -1px;
  width: calc(100% + 1px);
}
.elementor-control-type-dimensions li input:focus + .elementor-control-dimension-label {
  color: var(--e-a-color-txt);
}
.elementor-control-type-dimensions li .elementor-link-dimensions {
  border: var(--e-a-border-bold);
  border-inline-start: none;
  background-color: var(--e-a-bg-default);
  padding: 0;
  outline: none;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  cursor: pointer;
}
.elementor-control-type-dimensions li:first-child input {
  border-inline-start: 1px solid var(--e-a-border-color-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-dimensions li:first-child input:focus {
  border-color: var(--e-a-border-color-focus);
  margin-inline-start: 0;
  width: 100%;
}
.elementor-control-type-dimensions li:last-child {
  max-width: 27px;
}
.elementor-control-type-dimensions.e-units-custom li.elementor-control-dimension:focus-within {
  flex: 2.5;
}
.elementor-control-type-dimensions .elementor-control-dimension-label {
  color: var(--e-a-color-txt-muted);
  display: block;
  text-align: center;
  font-size: 9px;
  padding-block-start: 5px;
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked .elementor-linked {
  display: none;
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
  border-color: var(--e-a-border-color-bold);
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) .elementor-unlinked {
  display: none;
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc(-2.5em + 12px) 0;
  margin-inline-end: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: var(--e-a-dropdown-shadow);
}

.elementor-responsive-switcher {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  width: 100%;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
  color: inherit;
}
.elementor-responsive-switcher:hover {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: var(--e-a-color-primary-bold);
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: var(--e-a-color-primary-bold);
}

.e-units-wrapper {
  position: relative;
  margin-inline-start: auto;
}
.e-units-wrapper .e-units-switcher {
  cursor: pointer;
  font-size: 10px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-hover);
  border-radius: var(--e-a-border-radius);
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  inset-block-start: -0.8em;
  inset-inline-start: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  box-shadow: var(--e-a-dropdown-shadow);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  font-size: 10px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: var(--e-a-color-primary-bold);
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-type-gaps .elementor-control-gaps {
  display: flex;
}
.elementor-control-type-gaps li {
  flex: 1;
  transition: flex-grow 0.3s ease-in-out;
}
.elementor-control-type-gaps li input,
.elementor-control-type-gaps li .elementor-link-gaps {
  display: block;
  text-align: center;
  width: 100%;
  height: 27px;
}
.elementor-control-type-gaps li input {
  border-inline-start: none;
  border-radius: 0;
  padding: var(--e-a-border-radius);
}
.elementor-control-type-gaps li input:focus {
  border-inline-start: var(--e-a-border-bold);
  margin-inline-start: -1px;
  width: calc(100% + 1px);
}
.elementor-control-type-gaps li input:focus + .elementor-control-gap-label {
  color: var(--e-a-color-txt);
}
.elementor-control-type-gaps li .elementor-link-gaps {
  border: var(--e-a-border-bold);
  border-inline-start: none;
  background-color: var(--e-a-bg-default);
  padding: 0;
  outline: none;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  cursor: pointer;
}
.elementor-control-type-gaps li:first-child input {
  border-inline-start: 1px solid var(--e-a-border-color-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-gaps li:first-child input:focus {
  border-color: var(--e-a-border-color-focus);
  margin-inline-start: 0;
  width: 100%;
}
.elementor-control-type-gaps li:last-child {
  max-width: 27px;
}
.elementor-control-type-gaps.e-units-custom li.elementor-control-gap:focus-within {
  flex: 2.5;
}
.elementor-control-type-gaps .elementor-control-gap-label {
  color: var(--e-a-color-txt-muted);
  display: block;
  text-align: center;
  font-size: 9px;
  padding-block-start: 5px;
}
.elementor-control-type-gaps .elementor-link-gaps.unlinked {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-gaps .elementor-link-gaps.unlinked .elementor-linked {
  display: none;
}
.elementor-control-type-gaps .elementor-link-gaps:not(.unlinked) {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
  border-color: var(--e-a-border-color-bold);
}
.elementor-control-type-gaps .elementor-link-gaps:not(.unlinked) .elementor-unlinked {
  display: none;
}

.elementor-control-type-icons .elementor-control-media__preview > * {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.elementor-control-type-icons .elementor-control-media__preview i {
  font-size: 70px;
}
.elementor-control-type-icons .elementor-control-media__preview svg {
  height: 75%;
}
.elementor-control-type-icons .elementor-control-icons--inline__svg i.eicon-upload {
  font-size: 15px;
}

.elementor-control-type-gallery .elementor-control-media__content {
  border: var(--e-a-border-bold);
  border-radius: 3px;
}
.elementor-control-type-gallery .elementor-control-gallery-status {
  font-size: 12px;
  height: 27px;
  padding-inline-start: 10px;
  border-block-end: var(--e-a-border-bold);
  display: flex;
}
.elementor-control-type-gallery .elementor-control-gallery-status > * {
  display: flex;
  align-items: center;
}
.elementor-control-type-gallery .elementor-control-gallery-status-title {
  flex-grow: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-content {
  position: relative;
  overflow: hidden;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnails {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(48px, 1fr));
  grid-gap: 10px;
  cursor: pointer;
  padding: 10px;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnails:hover + .elementor-control-gallery-edit, .elementor-control-type-gallery .elementor-control-gallery-thumbnails:focus + .elementor-control-gallery-edit {
  opacity: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnail {
  width: 48px;
  height: 48px;
  -o-object-fit: cover;
     object-fit: cover;
  border: var(--e-a-border);
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnail + .unoptimized__image {
  outline: 2px solid var(--e-a-btn-bg-danger-active);
  opacity: 0.6;
}
.elementor-control-type-gallery .elementor-control-gallery-edit {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px;
  font-size: 11px;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
  cursor: pointer;
  opacity: 0;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span {
  position: absolute;
  inset-block-start: 10px;
  inset-inline-end: 10px;
  width: 21px;
  height: 21px;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.2);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-gallery .elementor-control-gallery-edit span i {
  font-size: 11px;
  padding: 5px;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span:hover {
  background-color: rgba(0, 0, 0, 0.6);
}
.elementor-control-type-gallery .elementor-control-gallery-add {
  width: 48px;
  height: 48px;
  font-size: 14px;
}
.elementor-control-type-gallery .elementor-control-gallery-add i {
  margin: 0;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-clear,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-thumbnails,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-edit {
  display: none;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-content {
  padding: 10px;
}
.elementor-control-type-gallery.elementor-gallery-has-images .elementor-control-gallery-add {
  display: none;
}
.elementor-control-type-gallery.elementor-control-dynamic .elementor-control-gallery-clear {
  border-inline-start: var(--e-a-border);
}
.elementor-control-type-gallery .elementor-control-gallery-clear {
  color: currentColor;
  background-color: transparent;
  border: none;
  cursor: pointer;
  justify-content: center;
}
.elementor-control-type-gallery .elementor-control-gallery-clear:hover, .elementor-control-type-gallery .elementor-control-gallery-clear:focus {
  color: var(--e-a-color-danger);
}
.elementor-control-type-gallery .elementor-control-dynamic-switcher {
  border-width: 0;
  border-inline-start-width: 1px;
  border-block-end-width: 1px;
  border-radius: 0;
}

.e-global__popover {
  width: 288px;
  z-index: 1;
  font-size: 12px;
  padding-inline-start: 10px;
}
.e-global__popover-toggle {
  border: var(--e-a-border-bold);
  border-inline-end: 0;
  border-start-start-radius: 3px;
  border-start-end-radius: 0;
  border-end-start-radius: 3px;
  border-end-end-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.e-global__popover-toggle:hover {
  background-color: var(--e-a-bg-hover);
}
.e-global__popover-toggle--active {
  color: var(--e-a-color-primary-bold);
  background-color: var(--e-a-bg-active-bold);
}
.e-global__popover-container {
  box-shadow: var(--e-a-popover-shadow);
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-bg-default);
}
.e-global__popover-title {
  padding: 15px 20px;
  font-weight: 500;
  border-block-end: var(--e-a-border);
  display: flex;
}
.e-global__popover-title > i {
  margin-inline-end: 5px;
}
.e-global__popover-title-text {
  flex-grow: 1;
}
.e-global__popover-info {
  margin-inline-end: 10px;
  display: inline-block;
}
.e-global__popover-info-tooltip {
  width: 270px;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.9);
  color: var(--e-a-color-white);
  padding: 20px;
  border-radius: 3px;
}
.e-global__popover-info-tooltip:after {
  content: "";
  position: absolute;
  inset-block-end: -17px;
  inset-inline-start: 16px;
  border: 10px solid transparent;
  border-block-start-color: rgba(0, 0, 0, 0.9);
}
.e-global__popover-info i {
  font-size: 13px;
}
.e-global__preview-items-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 5px 0;
}
.e-global__preview-items-container::-webkit-scrollbar {
  width: 7px;
}
.e-global__preview-items-container::-webkit-scrollbar-thumb {
  background-color: #BABFC5;
  border-radius: 10px;
}
.e-global__manage-button {
  font-weight: 500;
  cursor: pointer;
}
.e-global__typography {
  padding-block: 10px;
  padding-inline: 35px 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.e-global__typography.e-global__preview-item--selected:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  inset-inline-start: 13px;
}
.e-global__color {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.e-global__color-preview-container {
  height: 20px;
  width: 20px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-inline-end: 10px;
  flex-shrink: 0;
  position: relative;
}
.e-global__color-preview-color, .e-global__color-preview-transparent-bg {
  border-radius: 3px;
  position: absolute;
  inset: 0;
}
.e-global__color-preview-transparent-bg {
  background-image: linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold)), linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold));
  background-size: 12px 12px;
  background-position: 0 0, calc(12px / 2) calc(12px / 2);
}
.e-global__color-title {
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-inline-end: 10px;
}
.e-global__color-hex {
  font-size: 10px;
  color: var(--e-a-color-txt-muted);
}
.e-global__color .pcr-button {
  background-color: var(--e-a-bg-default);
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--e-a-color-white);
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  text-shadow: 0 0 1px #000;
  z-index: 1;
}
.e-global__preview-item {
  cursor: pointer;
  position: relative;
}
.e-global__preview-item:hover {
  background-color: var(--e-a-bg-hover);
}
.e-global__confirm-delete i, .e-global__confirm-message-text i {
  color: var(--e-a-color-warning);
}
.e-global__confirm-input-wrapper {
  display: flex;
  align-items: center;
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  margin: 15px 0;
  padding: 2px;
}
.e-global__confirm-input-wrapper input {
  font-family: var(--e-a-font-family);
  font-size: 12px;
  padding: 2px;
  border: 0;
}

.e-control-global .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
  max-width: 135px;
  width: 100%;
}
.e-control-global.elementor-control .elementor-control-input-wrapper {
  display: flex;
  flex-direction: row;
  align-items: stretch;
}
.e-control-global .elementor-control-spinner {
  margin-inline-end: 4px;
}

.elementor-control-type-hidden {
  display: none !important;
}

.elementor-control-type-icon .select2-selection__rendered .eicon {
  margin-inline-end: 3px;
}

.elementor-control-type-image_dimensions .elementor-control-field-description {
  margin: 0 0 15px;
  line-height: 1.4;
}
.elementor-control-type-image_dimensions .elementor-control-input-wrapper {
  display: flex;
  align-items: flex-start;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field {
  width: 65px;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field input:focus + .elementor-image-dimensions-field-description {
  color: var(--e-a-color-txt);
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-separator {
  width: 20px;
  text-align: center;
  padding-block-start: 4px;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field-description {
  display: block;
  margin-block-start: 5px;
  color: var(--e-a-color-txt-disabled);
  font-size: 10px;
  text-align: center;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-apply-button {
  margin-inline-start: auto;
}

.elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__remove, .elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__content__remove {
  display: none;
}
.elementor-control-media.e-media-empty-placeholder .e-control-image-size {
  display: none;
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__content__upload-button {
  display: none;
}
.elementor-control-media .eicon-plus-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--e-a-color-white);
  font-size: 20px;
}
.elementor-control-media__content__upload-button {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-media__preview {
  height: 100%;
  background-size: cover;
  background-position: center;
}
.elementor-control-media-area {
  background-image: linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold)), linear-gradient(45deg, var(--e-a-border-color-bold) 25%, transparent 0, transparent 75%, var(--e-a-border-color-bold) 0, var(--e-a-border-color-bold));
  background-size: 16px 16px;
  background-position: 0 0, calc(16px / 2) calc(16px / 2);
  background-color: var(--e-a-bg-default);
  border: var(--e-a-border-bold);
  aspect-ratio: 21/9;
}
.elementor-control-media-area:not(:hover) .elementor-control-media__remove {
  display: none;
}
.elementor-control-media-area .eicon-video-camera {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 21px;
}
.elementor-control-media .elementor-control-media__content {
  aspect-ratio: 21/9;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}
.elementor-control-media .elementor-control-media__content:hover:after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  pointer-events: none;
}
.elementor-control-media .elementor-control-media__content:not(:hover) .elementor-control-media__tools {
  bottom: -30px;
}
.elementor-control-media__content {
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 27px;
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools > *:not(:first-child) {
  margin-inline-start: 1px;
}
.elementor-control-media__tool {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.9);
  font-size: 11px;
  transition: var(--e-a-transition-hover);
}
.elementor-control-media__tool:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
.elementor-control-media__remove {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.elementor-control-media__content__remove {
  position: absolute;
  z-index: 1;
  inset-block-start: 10px;
  inset-inline-end: 10px;
  width: 20px;
  height: 20px;
  font-size: 11px;
  color: var(--e-a-color-white);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: var(--e-border-radius);
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.2);
  transition: var(--e-a-transition-hover);
}
.elementor-control-media__content__remove:hover {
  background-color: rgba(0, 0, 0, 0.6);
}
.elementor-control-media.e-media-empty .elementor-control-file-area {
  display: none;
}
.elementor-control-media__warnings:empty {
  display: none;
}
.elementor-control-media__warnings:not(:empty) {
  margin-block-start: 10px;
}
.elementor-control-media__file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-block-start: 10px;
  border: var(--e-a-border);
}
.elementor-control-media__file__content {
  padding-inline-start: 5px;
  font-size: 12px;
}
.elementor-control-media__file__content__label {
  color: #9DA5AE;
}
.elementor-control-media__file__content__info {
  display: flex;
  align-items: center;
  font-weight: 500;
}
.elementor-control-media__file__content__info__icon {
  margin-inline-end: 5px;
}
.elementor-control-media__file__controls {
  display: flex;
  border-inline-start: var(--e-a-border);
}
.elementor-control-media__file__controls__upload-button, .elementor-control-media__file__controls__remove {
  width: 27px;
  height: 27px;
  cursor: pointer;
  align-items: center;
}
.elementor-control-media__file__controls__upload-button {
  display: flex;
  justify-content: center;
}
.elementor-control-media__file__controls__remove {
  border-inline-end: var(--e-a-border);
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__file__content__label {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__content__info {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__controls__remove {
  display: none;
}
.elementor-control-media .elementor-control-dynamic-switcher {
  border: none;
  border-radius: 0;
  background-color: rgba(0, 0, 0, 0.9);
  color: var(--e-a-color-white);
}
.elementor-control-media .elementor-control-dynamic-switcher:hover {
  background-color: rgba(0, 0, 0, 0.8);
  color: var(--e-a-color-white);
}
.elementor-control-media .e-control-image-size {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-block-start: 20px;
}
.elementor-control-media .e-control-image-size .elementor-control-input-wrapper {
  margin-inline-start: auto;
}

.elementor-control-type-media.elementor-control-dynamic-value .elementor-control-input-wrapper {
  border: none;
}

.elementor-control:not(.elementor-control-type-icons) .elementor-control-media__preview {
  background-color: var(--e-a-bg-active-bold);
}

.elementor-control-notice {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  border-radius: 3px;
  border: 1px solid var(--notice-control-color, var(--e-a-color-txt));
  color: var(--e-a-color-txt);
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  text-align: start;
  margin-block-start: 10px;
}
.elementor-control-notice-type-info {
  --notice-control-color: var(--e-a-color-info);
}
.elementor-control-notice-type-success {
  --notice-control-color: var(--e-a-color-success);
}
.elementor-control-notice-type-warning {
  --notice-control-color: var(--e-a-color-warning);
}
.elementor-control-notice-type-danger {
  --notice-control-color: var(--e-a-color-danger);
}
.elementor-control-notice-icon {
  flex-basis: 18px;
  color: var(--notice-control-color);
}
.elementor-control-notice-main {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 6px;
  flex: 1;
}
.elementor-control-notice-main-heading {
  font-weight: 700;
  font-style: italic;
}
.elementor-control-notice-main-content {
  font-style: italic;
  line-height: 1.5;
}
.elementor-control-notice-main-actions {
  display: flex;
  gap: 10px;
  padding-block-start: 8px;
}
.elementor-control-notice-main a {
  color: inherit;
  font-weight: 700;
  cursor: pointer;
}
.elementor-control-notice-main a:hover, .elementor-control-notice-main a:focus {
  color: inherit;
}
.elementor-control-notice-dismiss {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: transparent;
  border: 0;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.elementor-control-type-popover_toggle input {
  display: none;
}
.elementor-control-type-popover_toggle label {
  cursor: pointer;
}
.elementor-control-type-popover_toggle .elementor-control-input-wrapper {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:checked + .elementor-control-popover-toggle-toggle-label {
  color: var(--e-a-color-txt-active);
  background-color: var(--e-a-bg-active-bold);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:not(:checked) ~ .elementor-control-popover-toggle-reset-label {
  display: none;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle-label {
  height: 27px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--e-a-border-radius);
  border: var(--e-a-border-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle-label:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-reset-label {
  color: var(--e-a-color-txt-muted);
  margin-inline-end: 5px;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-reset-label:hover {
  color: var(--e-a-color-txt);
}

.elementor-controls-popover.e-controls-popover--typography {
  padding-block-start: 0;
}

.e-control-global .elementor-control-popover-toggle-toggle-label {
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
  flex-shrink: 0;
}

.elementor-control-type-repeater .elementor-control:not(.elementor-control-type-tab) {
  padding-inline-start: 10px;
  padding-inline-end: 10px;
  padding-block-end: 10px;
}
.elementor-control-type-repeater.elementor-repeater-has-minimum-rows .elementor-repeater-tool-remove {
  display: none;
}
.elementor-control-type-repeater.elementor-repeater-has-maximum-rows .elementor-repeater-tool-duplicate,
.elementor-control-type-repeater.elementor-repeater-has-maximum-rows .elementor-repeater-add {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-fields {
  margin: 10px 0;
}
.elementor-control-type-repeater .elementor-repeater-row-controls {
  border: var(--e-a-border-bold);
  border-block-start-width: 0;
  padding-block-start: 15px;
}
.elementor-control-type-repeater .elementor-repeater-row-controls:not(.editable) {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-row-tools {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--e-a-border-color-bold);
  transition: var(--e-a-transition-hover);
}
.elementor-control-type-repeater .elementor-repeater-row-tools > button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border: none;
  background-color: transparent;
  color: currentColor;
  cursor: pointer;
}
.elementor-control-type-repeater .elementor-repeater-row-tools > button:hover, .elementor-control-type-repeater .elementor-repeater-row-tools > button:focus {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title {
  flex: 1;
  justify-content: flex-start;
  padding: 0 10px;
  font-size: var(--control-title-size);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title .eicon,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title i,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title svg {
  margin-inline-end: 5px;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title img[src$=svg] {
  width: 1em;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-tool {
  width: 40px;
  border-inline-start: 1px solid var(--e-a-border-color-bold);
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-handle-sortable {
  width: 10px;
  border-inline-end: 1px solid var(--e-a-border-color-bold);
  cursor: move;
}
.elementor-control-type-repeater .elementor-button-wrapper {
  text-align: center;
  padding-block-start: 5px;
}

#elementor-controls,
#elementor-panel-page-settings-controls,
#elementor-panel-editorPreferences-settings-controls {
  padding-block-start: 15px;
}

.elementor-control-type-section {
  padding: 0;
}
.elementor-control-type-section.e-open {
  padding-block-end: 10px;
}
.elementor-control-type-section + .elementor-control:not(.elementor-control-type-section):before {
  display: none;
}
.elementor-control-type-section:not(:first-child):not(.elementor-control-type-section + .elementor-control-type-section) {
  margin-block-start: 25px;
}

.elementor-control-type-select .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-select .elementor-control-input-wrapper select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  font-size: var(--control-title-size);
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  text-transform: inherit;
  letter-spacing: inherit;
  line-height: inherit;
  flex-basis: 100%;
  padding-inline-start: 5px;
  padding-inline-end: 20px;
  cursor: pointer;
}
.elementor-control-type-select .elementor-control-input-wrapper select.e-select-placeholder {
  color: var(--e-a-color-txt-disabled);
}
.elementor-control-type-select .elementor-control-input-wrapper option.e-option-placeholder {
  display: none;
}
.elementor-control-type-select .elementor-control-input-wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  font-size: 12px;
  position: absolute;
  inset-block-start: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  pointer-events: none;
}
.elementor-control-type-select .elementor-control-field.elementor-control-field-select-small .elementor-control-input-wrapper {
  max-width: 80px;
}

.elementor-shadow-box .elementor-shadow-slider {
  margin-block-start: 10px;
}
.elementor-shadow-box .elementor-color-picker-wrapper .elementor-control-title {
  flex-grow: 1;
}

.elementor-control-type-slider.elementor-control-dynamic input {
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
}
.elementor-control-type-slider .elementor-control-unit-2 {
  width: 21%;
}
.elementor-control-type-slider.elementor-control-type-slider--multiple .elementor-control-input-wrapper {
  display: block;
}
.elementor-control-type-slider--multiple {
  padding-block-end: 40px;
}
.elementor-control-type-slider--multiple .elementor-slider {
  margin-block-start: 12px;
  width: 98%;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle {
  border-radius: 0;
  width: 10px;
  transform: translateY(calc(50% - 14px)) translateX(calc(4px * var(--direction-multiplier)));
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle:after {
  content: "";
  position: absolute;
  top: 2px;
  height: 12px;
  width: 11px;
  transform: rotate(45deg);
  background-color: var(--e-a-color-white);
  border-radius: 3px;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-lower:after {
  left: 5px;
  box-shadow: 2px -2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-upper:after {
  right: 5px;
  box-shadow: -2px 2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider .elementor-control-dynamic-switcher {
  border-inline-start-width: 0;
  border-start-start-radius: 0;
  border-start-end-radius: var(--e-a-border-radius);
  border-end-start-radius: 0;
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-slider .elementor-control-input-wrapper {
  display: flex;
  align-items: center;
}
.elementor-control-type-slider .elementor-dynamic-cover {
  margin-block-start: 10px;
}
.elementor-control-type-slider.e-units-custom .elementor-slider {
  display: none;
}
.elementor-control-type-slider.e-units-custom .elementor-slider-input {
  width: 100%;
  margin: 0;
  transition: none;
}

.elementor-slider {
  flex-grow: 1;
  height: 4px;
  background-color: var(--e-a-border-color-bold);
  border-radius: 5px;
  position: relative;
  cursor: pointer;
}
.elementor-slider-input {
  width: 21%;
  min-width: 54px;
  margin-inline-start: 12px;
  transition: width 0.3s ease-in-out;
}
.elementor-slider__extra {
  position: relative;
}
.elementor-slider__labels {
  display: flex;
  justify-content: space-between;
}
.elementor-slider__label {
  font-size: 9px;
  color: var(--e-a-color-txt-disabled);
}
.elementor-slider__scales {
  position: absolute;
  display: flex;
  justify-content: space-evenly;
  width: 100%;
  margin-block-start: 4px;
}
.elementor-slider__scale {
  width: 1px;
  height: 21px;
  background-color: var(--e-a-border-color-focus);
}
.elementor-slider .noUi-handle {
  height: 16px;
  width: 16px;
  background-color: var(--e-a-color-white);
  position: absolute;
  inset-inline-end: 0;
  transform: translateY(calc(50% - 14px)) translateX(calc(8px * var(--direction-multiplier)));
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}
.elementor-slider .noUi-connects {
  position: absolute;
  width: 100%;
  height: 4px;
}
.elementor-slider .noUi-connect {
  position: absolute;
  z-index: 0;
  inset: 0;
  will-change: transform;
  transform-origin: 0 0;
  background-color: var(--e-a-border-color-focus);
}
.elementor-slider .noUi-tooltip {
  position: absolute;
  top: calc(100% + 5px);
  left: calc(50% - 4px);
  transform: translateX(-50%);
  font-size: 10px;
}

.elementor-control-type-structure .elementor-control-field {
  display: initial;
}
.elementor-control-type-structure .elementor-control-structure-preset {
  padding: 3px;
  border-radius: var(--e-border-radius);
  display: inline-block;
  cursor: pointer;
  height: 50px;
}
.elementor-control-type-structure .elementor-control-structure-preset svg {
  height: 100%;
}
.elementor-control-type-structure .elementor-control-structure-preset path {
  fill: var(--e-a-border-color-bold);
}
.elementor-control-type-structure .elementor-control-structure-reset {
  padding: 15px 20px 0;
  font-size: 11px;
  cursor: pointer;
  color: var(--e-a-color-txt-muted);
  border-block-start: var(--e-a-border);
  margin: 0 -20px;
}
.elementor-control-type-structure .elementor-control-structure-reset:hover {
  color: var(--e-a-color-txt);
}
.elementor-control-type-structure .elementor-control-structure-title {
  margin: 10px -20px 0;
}
.elementor-control-type-structure .elementor-control-structure-title:before {
  height: 10px;
  box-shadow: inset 0 2px 4px rgba(127, 127, 127, 0.1);
}
.elementor-control-type-structure .elementor-control-structure-presets {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.elementor-control-type-structure .elementor-control-structure-presets input {
  display: none;
}
.elementor-control-type-structure .elementor-control-structure-presets input:checked + .elementor-control-structure-preset path {
  fill: var(--e-a-border-color-focus);
}
.elementor-control-type-structure .elementor-control-structure-preset-wrapper {
  margin-block-end: 15px;
}
.elementor-control-type-structure .elementor-control-structure-preset-title {
  text-align: center;
  padding-block-start: 5px;
  font-style: italic;
  font-size: 11px;
  color: #9DA5AE;
}

.elementor-control-type-switcher .elementor-control-input-wrapper {
  text-align: end;
}
.elementor-control-type-switcher .elementor-switch {
  position: relative;
  display: inline-block;
  vertical-align: top;
  height: 20px;
  background-color: var(--e-a-bg-default);
  border-radius: 18px;
  cursor: pointer;
}
.elementor-control-type-switcher .elementor-switch-input {
  display: none;
}
.elementor-control-type-switcher .elementor-switch-label {
  position: relative;
  display: block;
  height: inherit;
  font-size: 10px;
  background: var(--e-a-bg-active-bold);
  border-radius: inherit;
  transition: 0.15s ease-out;
  transition-property: opacity, background;
}
.elementor-control-type-switcher .elementor-switch-label:before, .elementor-control-type-switcher .elementor-switch-label:after {
  position: absolute;
  top: 0;
  width: 50%;
  text-align: center;
  line-height: 20px;
  transition: inherit;
}
.elementor-control-type-switcher .elementor-switch-label:before {
  content: attr(data-off);
  right: 5px;
  color: var(--e-a-color-txt-muted);
}
.elementor-control-type-switcher .elementor-switch-label:after {
  content: attr(data-on);
  left: 5px;
  color: var(--e-a-btn-color);
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label {
  background: var(--e-a-btn-bg-primary);
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:before {
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:after {
  opacity: 1;
}
.elementor-control-type-switcher .elementor-switch-handle {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 10px;
  transition: left 0.15s ease-out;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-handle {
  left: initial;
  right: 1px;
}

.elementor-control-type-tabs {
  display: none;
  font-size: var(--control-title-size);
}
.elementor-control-type-tabs:has(> :not(.elementor-control-type-tab.elementor-hidden-control)) {
  display: flex;
}

.elementor-control-type-tab {
  text-align: center;
  width: 100%;
  padding: 0;
  line-height: 25px;
  border-block-start: var(--e-a-border-bold);
  border-block-end: var(--e-a-border-bold);
  border-inline-end: var(--e-a-border-bold);
  transition: var(--e-a-transition-hover);
  cursor: pointer;
}
.elementor-control-type-tab:first-child {
  border-inline-start: var(--e-a-border-bold);
  border-start-start-radius: var(--e-a-border-radius);
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-type-tab:last-child {
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-tab:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-tab.e-tab-active {
  background-color: var(--e-a-bg-active-bold);
  color: var(--e-a-color-txt-accent);
}

.e-tab-close {
  display: none !important;
}

.elementor-control-type-textarea .elementor-control-dynamic-switcher,
.elementor-control-type-code .elementor-control-dynamic-switcher {
  border-inline-start-width: 1px;
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--e-a-border-radius);
}
.elementor-control-type-textarea:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher,
.elementor-control-type-code:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher {
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: 0;
  z-index: 1;
}
.elementor-control-type-textarea .elementor-control-input-wrapper,
.elementor-control-type-code .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-textarea textarea,
.elementor-control-type-code textarea {
  display: block;
  font-family: inherit;
}
.elementor-control-type-textarea textarea:focus + .elementor-control-dynamic-switcher,
.elementor-control-type-code textarea:focus + .elementor-control-dynamic-switcher {
  display: none;
}
.elementor-control-type-textarea pre:focus-within + .elementor-control-dynamic-switcher,
.elementor-control-type-code pre:focus-within + .elementor-control-dynamic-switcher {
  display: none;
}

.elementor-control-type-url .elementor-control-field {
  position: relative;
}
.elementor-control-type-url .elementor-control-input-wrapper {
  display: flex;
}
.elementor-control-type-url.elementor-control-dynamic .elementor-control-url-more-options input {
  border-radius: var(--e-a-border-radius);
}
.elementor-control-type-url.elementor-control-dynamic-value .e-input-style {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}
.elementor-control-type-url.elementor-control-dynamic-value .elementor-control-url-more {
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: var(--e-a-border-radius);
}
.elementor-control-type-url .elementor-control-url-more {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: var(--e-a-border-bold);
  color: currentColor;
  background-color: transparent;
  border-inline-start-width: 0;
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-more i {
  font-size: 12px;
}
.elementor-control-type-url .elementor-control-url-more-options {
  display: none;
  padding-block-start: 10px;
}
.elementor-control-type-url .elementor-control-url-more-options .elementor-control-field-description {
  margin-block-start: 10px;
}
.elementor-control-type-url .elementor-control-url-more:hover {
  color: var(--e-a-color-txt-hover);
}
.elementor-control-type-url .elementor-control-url-more:focus {
  color: var(--e-a-color-txt-active);
  border-color: var(--e-a-border-color-focus);
}
.elementor-control-type-url .elementor-control-url-option {
  display: flex;
  align-items: center;
}
.elementor-control-type-url .elementor-control-url-option:not(:last-child) {
  padding-block-end: 10px;
}
.elementor-control-type-url .elementor-control-url-option input,
.elementor-control-type-url .elementor-control-url-option label {
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-autocomplete-spinner {
  display: none;
  position: absolute;
  top: 5px;
  right: 0;
  width: 10px;
  height: 10px;
  font-size: 10px;
  color: var(--e-a-color-txt-disabled);
}
.elementor-control-type-url .elementor-control-url__custom-attributes label {
  font-size: var(--control-title-size);
}
.elementor-control-type-url .elementor-control-url__custom-attributes input {
  width: 100%;
  margin-block-start: 10px;
}
.elementor-control-type-url .elementor-input:focus ~ div {
  border-color: var(--e-a-border-color-focus);
}

.elementor-autocomplete-menu {
  position: absolute;
  background: var(--e-a-bg-default);
  border: var(--e-a-border);
  margin: 0;
  list-style: none;
  padding: 4px 0;
  height: auto;
  width: 100%;
  min-width: 260px;
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 3px;
  transition: var(--e-a-transition-hover);
  cursor: default;
  z-index: 1;
}
.elementor-autocomplete-menu .ui-menu-item {
  display: flex;
  justify-content: space-between;
  align-self: baseline;
  padding: 5px 8px;
  font-size: 12px;
  width: 100%;
  line-height: 1.2;
  cursor: pointer;
}
.elementor-autocomplete-menu .ui-menu-item.ui-state-hover, .elementor-autocomplete-menu .ui-menu-item.ui-state-active, .elementor-autocomplete-menu .ui-menu-item.ui-state-focus {
  background: var(--e-a-bg-hover);
}
.elementor-autocomplete-menu .elementor-autocomplete-item-info {
  font-size: 10px;
  padding-block-start: 2px;
}

.elementor-control-type-visual_choice {
  width: 100%;
}

.elementor-visual-choice-choices {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(var(--elementor-visual-choice-columns), 1fr);
  text-align: center;
  border-radius: var(--e-a-border-radius);
  overflow: hidden;
}

.elementor-visual-choice-element {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: auto;
}
.elementor-visual-choice-element-image label {
  border: var(--e-a-border-bold);
  border-radius: var(--e-a-border-radius);
  font-size: 12px;
  transition: var(--e-a-transition-hover);
  cursor: pointer;
  overflow: hidden;
  width: 100%;
  padding: 8px;
}
.elementor-visual-choice-element-image input.e-visual-choice-placeholder + label, .elementor-visual-choice-element-image input:checked + label, .elementor-visual-choice-element-image input:hover + label {
  background-color: var(--e-a-bg-active-bold);
  opacity: 1;
}
.elementor-visual-choice-element-image input:checked + label {
  border-color: var(--e-a-color-primary-bold);
}
.elementor-visual-choice-element-image input:not(:checked) + label {
  background-color: var(--e-a-color-white);
  opacity: 0.5;
}
.elementor-visual-choice-element-image img {
  width: 100%;
  height: auto;
}
.elementor-visual-choice-element-button {
  grid-column: span var(--elementor-visual-choice-columns);
}
.elementor-visual-choice-element-button label {
  width: -moz-fit-content;
  width: fit-content;
}
.elementor-visual-choice-element-button input:checked + .elementor-button, .elementor-visual-choice-element-button input:hover + .elementor-button {
  background-color: var(--e-a-bg-active-bold);
}
.elementor-visual-choice-element-button input:checked + .elementor-button {
  border-color: var(--e-a-color-primary-bold);
}

.elementor-label-inline .elementor-visual-choice-choices {
  justify-content: flex-end;
}

.tipsy-inner {
  white-space: pre-line;
}

.elementor-control-type-wp_widget .widget-inside {
  line-height: 2;
  background-color: inherit;
  display: block;
}
.elementor-control-type-wp_widget .quicktags-toolbar input {
  width: auto;
}

.elementor-control-type-wysiwyg * {
  box-sizing: content-box;
}
.elementor-control-type-wysiwyg .wp-editor-container {
  border: var(--e-a-border);
}
.elementor-control-type-wysiwyg .wp-editor-tabs {
  border: var(--e-a-border-bold);
  border-block-end: none;
  border-start-start-radius: var(--e-a-border-radius);
  border-start-end-radius: var(--e-a-border-radius);
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}
.elementor-control-type-wysiwyg .wp-editor-tabs button:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-control-type-wysiwyg #insert-media-button {
  height: initial;
  line-height: 24px;
  font-size: 10px;
  color: var(--e-a-color-txt);
  border-color: var(--e-a-border-color-bold);
  background-color: var(--e-a-bg-default);
  min-height: initial;
}
.elementor-control-type-wysiwyg .ed_button {
  height: 22px;
  width: initial;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon {
  height: 14px;
  margin: 0;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon:before {
  font-size: 14px;
}
.elementor-control-type-wysiwyg .wp-switch-editor {
  position: static;
  border: none;
  margin: 0;
  color: var(--e-a-color-txt);
  font-size: 10px;
  padding: 3px 9px 4px;
}
.elementor-control-type-wysiwyg .switch-html {
  border: solid var(--e-a-border-color-bold);
  border-width: 0 1px;
}
.elementor-control-type-wysiwyg .html-active .switch-tmce {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .html-active .switch-html {
  background-color: var(--e-a-bg-active);
}
.elementor-control-type-wysiwyg .tmce-active .switch-tmce {
  background-color: var(--e-a-bg-active);
}
.elementor-control-type-wysiwyg .tmce-active .switch-html {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .mce-toolbar-grp,
.elementor-control-type-wysiwyg .quicktags-toolbar {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-wysiwyg .mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
  background-color: var(--e-a-bg-default);
}
.elementor-control-type-wysiwyg .mce-toolbar .mce-btn-group .mce-btn.mce-listbox button {
  color: var(--e-a-color-txt);
}
.elementor-control-type-wysiwyg .mce-toolbar-grp > div {
  padding: 0 3px;
}
.elementor-control-type-wysiwyg .elementor-wp-editor {
  box-sizing: border-box;
}
.elementor-control-type-wysiwyg .mce-ico {
  color: var(--e-a-color-txt);
  font-size: 16px;
}
.elementor-control-type-wysiwyg .mce-btn-group .mce-btn:hover, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn:active, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn.mce-active {
  color: var(--e-a-color-txt-active);
  background: var(--e-a-bg-hover);
  border-color: var(--e-a-border-color);
  box-shadow: none;
}
.elementor-control-type-wysiwyg .mce-btn-group .mce-btn:hover i, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn:active i, .elementor-control-type-wysiwyg .mce-btn-group .mce-btn.mce-active i {
  color: var(--e-a-color-txt-active);
}
.elementor-control-type-wysiwyg .mce-path {
  padding: 5px 10px;
}
.elementor-control-type-wysiwyg .mce-path-item {
  font-size: 12px;
  color: var(--e-a-color-txt);
}
.elementor-control-type-wysiwyg .mce-top-part:before {
  box-shadow: none;
}
.elementor-control-type-wysiwyg .elementor-control-dynamic-switcher {
  border: none;
}

@media screen and (prefers-color-scheme: dark) {
  #wp-link-wrap {
    color-scheme: light;
    --e-a-color-txt: #515962;
    --e-a-bg-default: #ffffff;
    --e-a-border-color-bold: #D5D8DC;
    --e-a-color-primary-bold: #D004D4;
  }
  #wp-link {
    color: var(--e-a-color-txt);
  }
  #wp-link input {
    background-color: var(--e-a-bg-default);
    border-color: var(--e-a-border-color-bold);
  }
  #wp-link input[type=checkbox] {
    border-color: var(--e-a-border-color-bold);
  }
  #wp-link input[type=checkbox]:checked {
    background: var(--e-a-color-primary-bold);
  }
  #wp-link input.button-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
  }
}
.ui-resizable-n {
  top: -5px;
}
.ui-resizable-e {
  right: -5px;
}
.ui-resizable-s {
  bottom: -5px;
}
.ui-resizable-w {
  left: -5px;
}
.ui-resizable-ne {
  top: -5px;
  right: -5px;
}
.ui-resizable-nw {
  top: -5px;
  left: -5px;
}
.ui-resizable-se {
  bottom: -5px;
  right: -5px;
}
.ui-resizable-sw {
  bottom: -5px;
  left: -5px;
}
.ui-resizable-n, .ui-resizable-s {
  left: 0;
  height: 10px;
  width: 100%;
  cursor: ns-resize;
}
.ui-resizable-e, .ui-resizable-w {
  top: 0;
  height: 100%;
  width: 10px;
  cursor: ew-resize;
}
.ui-resizable-ne, .ui-resizable-nw, .ui-resizable-se, .ui-resizable-sw {
  height: 15px;
  width: 15px;
}
.ui-resizable-nw, .ui-resizable-se {
  cursor: nwse-resize;
}
.ui-resizable-ne, .ui-resizable-sw {
  cursor: nesw-resize;
}
.ui-resizable-handle {
  position: absolute;
}
.ui-resizable-resizing {
  pointer-events: none;
}

:root {
  --e-p-draggable-color:	#F0ABFC;
  --e-p-border-section:	#F3BAFD;
  --e-p-border-section-hover:	#F0ABFC;
  --e-p-border-section-invert:	#0C0D0E;
  --e-p-border-con:	#F3BAFD;
  --e-p-border-con-hover:	#F0ABFC;
  --e-p-border-con-invert:	#0C0D0E;
  --e-p-border-column:	#9DA5AE;
  --e-p-border-column-hover:	#818A96;
  --e-p-border-column-invert:	#0C0D0E;
  --e-p-border-con-in:	#9DA5AE;
  --e-p-border-con-in-hover:	#818A96;
  --e-p-border-con-in-invert:	#0C0D0E;
  --e-p-border-widget:	#F3BAFD;
  --e-p-border-widget-hover:	#F0ABFC;
  --e-p-border-widget-invert:	#0C0D0E;
  --e-p-border-global:	#5EEAD4;
  --e-p-border-global-hover:	#1DDDBF;
  --e-p-border-global-invert:	#0C0D0E;
}

.elementor-editor-active .elementor-add-section {
  all: initial;
  display: flex;
  max-width: 1160px;
  position: relative;
  margin-inline: auto;
}
.elementor-editor-active .elementor-add-section:not(.elementor-dragging-on-child) .elementor-add-section-inner {
  border: 2px dashed var(--e-a-border-color-bold);
  background-color: rgba(255, 255, 255, 0.5);
}
.elementor-editor-active .elementor-add-section.elementor-dragging-on-child .elementor-add-section-inner {
  border: 2px dashed var(--e-a-color-primary);
}
.elementor-editor-active .elementor-add-section[data-view=choose-action] .e-view:not(.elementor-add-new-section) {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-preset] .e-view:not(.elementor-select-preset) {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-container-preset] .e-view:not(.e-con-select-preset) {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-type] .e-view:not(.e-con-select-type) {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-container-preset-grid] .e-view:not(.e-con-select-preset-grid) {
  display: none;
}
.elementor-editor-active .elementor-add-section-inner {
  text-align: center;
  margin: 20px;
  padding: 40px 0;
  display: flex;
  justify-content: center;
  flex-grow: 1;
}
.elementor-editor-active .elementor-add-new-section {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 5px;
}
.elementor-editor-active .elementor-add-section-area-button {
  padding: 12px;
  height: 40px;
  width: 40px;
  font-size: 16px;
  display: flex;
  outline: unset;
  border: unset;
  border-radius: 50%;
  box-shadow: unset;
  text-shadow: unset;
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-black);
  transition: var(--e-a-transition-hover);
  cursor: pointer;
}
.elementor-editor-active .elementor-add-section-area-button:hover, .elementor-editor-active .elementor-add-section-area-button:focus {
  border: unset;
  border-radius: 50%;
  box-shadow: unset;
  text-shadow: unset;
  color: var(--e-a-color-black);
  background-color: var(--e-a-bg-active-bold);
}
.elementor-editor-active .elementor-add-section-area-button i {
  margin: 0;
}
.elementor-editor-active .elementor-add-section-area-button.e-button-primary {
  background-color: var(--e-a-btn-bg-primary);
  color: var(--e-a-btn-color);
}
.elementor-editor-active .elementor-add-section-area-button.e-button-primary:hover, .elementor-editor-active .elementor-add-section-area-button.e-button-primary:focus {
  background-color: var(--e-a-btn-bg-primary-hover);
  color: var(--e-a-btn-color);
}
.elementor-editor-active .elementor-add-section-drag-title,
.elementor-editor-active .elementor-select-preset-title {
  font-family: var(--e-a-font-family);
  font-size: 13px;
  line-height: 1;
  color: var(--e-a-color-txt);
}
.elementor-editor-active .elementor-add-section-drag-title {
  font-style: italic;
  font-weight: normal;
  margin-block-start: 10px;
  width: 100%;
}
.elementor-editor-active .elementor-select-preset-title {
  font-weight: 500;
  text-transform: uppercase;
}
.elementor-editor-active .elementor-add-section-close,
.elementor-editor-active .elementor-add-section-back,
.elementor-editor-active .flex-preset-button,
.elementor-editor-active .grid-preset-button {
  padding: unset;
  margin: unset;
  border: unset;
  border-radius: unset;
  outline: unset;
  box-shadow: unset;
  text-shadow: unset;
  color: var(--e-a-color-txt);
  background-color: transparent;
}
.elementor-editor-active .elementor-add-section-close {
  inset-inline-end: 40px;
}
.elementor-editor-active .elementor-add-section-back {
  inset-inline-start: 40px;
  scale: calc(1 * var(--direction-multiplier)) 1;
}
.elementor-editor-active .elementor-add-section-close,
.elementor-editor-active .elementor-add-section-back {
  position: absolute;
  inset-block-start: 40px;
  font-size: 20px;
  cursor: pointer;
  line-height: 1;
  color: var(--e-a-color-txt);
  border-radius: 50%;
  box-shadow: unset;
  text-shadow: unset;
}
.elementor-editor-active .elementor-add-section-close:hover, .elementor-editor-active .elementor-add-section-close:focus,
.elementor-editor-active .elementor-add-section-back:hover,
.elementor-editor-active .elementor-add-section-back:focus {
  border: unset;
  border-radius: 50%;
  box-shadow: unset;
  text-shadow: unset;
  color: var(--e-a-color-txt-hover);
  background-color: var(--e-a-bg-hover);
}
.elementor-editor-active .flex-preset-button:hover, .elementor-editor-active .flex-preset-button:focus,
.elementor-editor-active .grid-preset-button:hover,
.elementor-editor-active .grid-preset-button:focus {
  background-color: transparent;
  color: var(--e-a-color-txt-hover);
}
.elementor-editor-active .elementor-select-preset-list {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px auto 0;
  max-width: 700px;
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset {
  cursor: pointer;
  flex-basis: 90px;
  padding: unset;
  margin: unset;
  border: unset;
  border-radius: unset;
  outline: unset;
  box-shadow: unset;
  text-shadow: unset;
  background-color: unset;
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset path,
.elementor-editor-active .elementor-select-preset-list .elementor-preset rect {
  fill: var(--e-a-bg-active-bold);
  transition: 0.3s all;
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset:hover, .elementor-editor-active .elementor-select-preset-list .elementor-preset:focus {
  background-color: transparent;
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset:hover path,
.elementor-editor-active .elementor-select-preset-list .elementor-preset:hover rect, .elementor-editor-active .elementor-select-preset-list .elementor-preset:focus path,
.elementor-editor-active .elementor-select-preset-list .elementor-preset:focus rect {
  fill: var(--e-a-color-txt);
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset .e-preset--container {
  position: relative;
  display: flex;
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset .e-preset--container::before {
  content: var(--text);
  font-family: var(--e-a-font-family);
  font-size: 13px;
  font-weight: 500;
  position: absolute;
  left: 50%;
  top: 50%;
  color: var(--e-a-color-white);
  transform: translate(-50%, -50%);
}
.elementor-editor-active .elementor-select-preset-list .elementor-preset svg {
  height: 50px;
  width: 100%;
  scale: calc(1 * var(--direction-multiplier)) 1;
}
.elementor-editor-active .elementor-add-section[data-view=choose-action] .elementor-add-section-back {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-preset] .elementor-add-section-back {
  display: none;
}
.elementor-editor-active .elementor-add-section[data-view=select-type] .elementor-add-section-back {
  display: none;
}
.elementor-editor-active #elementor-add-new-section {
  margin: 60px auto;
}
.elementor-editor-active #elementor-add-new-section[data-view=choose-action] .elementor-add-section-close {
  display: none;
}
.elementor-editor-active .elementor-add-section-inline {
  margin: 10px auto;
  width: 100%;
}

@keyframes placeholder-section {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 40px;
    opacity: 0.9;
  }
}
@keyframes placeholder-widget {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 10px;
    opacity: 0.9;
  }
}
@keyframes section-outline {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
body.elementor-editor-active .elementor.elementor-edit-mode.layout-section .entry-content {
  overflow: visible;
}
body.elementor-editor-active .elementor.elementor-edit-mode .article {
  overflow: visible;
}
body.elementor-editor-active .elementor.elementor-edit-mode .elementor-element-overlay .elementor-editor-element-settings {
  clear: unset;
}
body.elementor-editor-active .elementor.elementor-edit-mode .elementor-element.elementor-section {
  overflow: visible;
}

.elementor-edit-area .animated {
  animation-fill-mode: none !important;
}
.elementor-edit-area ul.elementor-editor-element-settings {
  word-break: normal;
  padding: 0;
}
.elementor-edit-area .gallery {
  opacity: 1;
}

.pen {
  position: relative;
  outline: none;
}
.pen:not([data-elementor-inline-editing-toolbar=advanced]) {
  white-space: pre-wrap;
}
.pen-menu {
  box-shadow: 1px 2px 3px -2px #222;
  background-color: #3f444b;
  position: fixed;
  overflow: hidden;
  border-radius: 3px;
  z-index: 9999;
}
.pen-menu:after {
  top: 100%;
  content: "";
  position: absolute;
  border: 6px solid transparent;
  border-block-start-color: #3f444b;
  left: 50%;
  transform: translateX(-50%);
}
.pen-menu-below:after {
  top: 0;
  transform: translateX(-50%) translateY(-100%) rotate(180deg);
}
.pen-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  padding: 0 10px;
  font-size: 21px;
  color: #D5D8DC;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.pen-icon:hover {
  background: #1f2124;
}
.pen-icon[data-group], .pen-icon[data-action=closeGroup] {
  display: none;
}
.pen-icon[data-action=close]:before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  height: 60%;
  width: 1px;
  background-color: #1f2124;
}
.pen-icon.active {
  background-color: #1f2124;
  box-shadow: inset 2px 2px 4px #1f2124;
}
.pen-group-icon:after {
  font-family: eicons;
  font-size: 12px;
  content: "\e92a";
  padding-inline-start: 4px;
  color: #2563EB;
}
.pen-input-wrapper {
  align-items: center;
}
.pen-input-wrapper .pen-url-input {
  font-size: 12px;
  line-height: 1.5;
  padding: 0;
  padding-inline-start: 10px;
  padding-inline-end: 10px;
  width: 250px;
  direction: ltr;
}
.pen-input-wrapper .pen-url-input, .pen-input-wrapper .pen-url-input:focus {
  background-color: transparent;
  border: none;
  outline: none;
  box-shadow: none;
  color: #fff;
}
.pen-input-wrapper .pen-url-input::-moz-placeholder {
  color: #D5D8DC;
}
.pen-input-wrapper .pen-url-input::placeholder {
  color: #D5D8DC;
}
.pen-input-label {
  margin: 0;
  margin-inline-end: -1px;
}
.pen-placeholder:before {
  content: attr(data-pen-placeholder);
  position: absolute;
  font-weight: normal;
  color: #757575;
  opacity: 0.6;
}
.pen-external-url-checkbox {
  display: none;
}
.pen-external-url-checkbox:checked + i {
  color: #fff;
}

.elementor-inline-editing i:not([class]) {
  font-style: italic;
}
.elementor-inline-editing b {
  font-weight: bold;
}
.elementor-inline-editing u {
  text-decoration: underline;
}

.e-element-color-picker {
  --primary-color: #2563EB;
  --swatch-size: 25px;
  cursor: default;
  display: flex;
  position: absolute;
  width: calc((var(--count) + 1) * var(--swatch-size));
  height: var(--swatch-size);
  top: var(--top);
  left: var(--left);
  right: var(--right, unset);
  border-radius: 3px;
  opacity: 0;
  pointer-events: none;
  z-index: 9998;
  background-color: var(--primary-color);
  padding: 1px;
  box-sizing: content-box;
  transition: opacity 0.3s, width 0.3s;
}
.e-element-color-picker.e-picker-hidden {
  opacity: 0;
  pointer-events: none;
}
.e-element-color-picker::before {
  content: "";
  flex: 0 1 var(--swatch-size);
  max-width: 100%;
  height: 100%;
  box-sizing: border-box;
  text-align: center;
}
.e-element-color-picker::after {
  content: "\e91e";
  font-family: "eicons";
  color: #FFF;
  font-size: 1rem;
  line-height: var(--swatch-size);
  position: absolute;
  left: 0.3rem;
  z-index: -1;
}
.e-element-color-picker__swatch {
  flex: 1 0 var(--swatch-size);
  max-width: 100%;
  height: 100%;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
  position: relative;
  overflow: hidden;
  border-radius: inherit;
  /* Hack to fix transparent `--color` on hover */
  background: linear-gradient(var(--color), var(--color)), linear-gradient(var(--primary-color), var(--primary-color));
}
.e-element-color-picker__swatch:not(:first-child) {
  border-left: 1px solid var(--primary-color);
}
.e-element-color-picker__swatch::before {
  content: attr(data-text);
  position: absolute;
  left: 50%;
  top: 50%;
  opacity: 0;
  color: var(--color);
  font-size: 10px;
  font-weight: 300;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  transform: translate(-50%, -50%);
  filter: hue-rotate(180deg) grayscale(1) contrast(999) invert(1);
  transition: inherit;
}
.e-element-color-picker__swatch:hover {
  flex-basis: calc(2 * var(--swatch-size));
  flex-shrink: 0;
}
.e-element-color-picker__swatch:hover::before {
  opacity: 1;
}

.e-ui-state--elements-color-picker-color-picking__on *:not(.e-element-color-picker__swatch) {
  cursor: url("../images/eyedropper.svg") 0 20, pointer;
}
.e-ui-state--elements-color-picker-color-picking__on .e-element-color-picker:not(.e-picker-hidden):hover,
.e-ui-state--elements-color-picker-color-picking__on .elementor-element:hover > .e-element-color-picker:not(.e-picker-hidden),
.e-ui-state--elements-color-picker-color-picking__on .elementor-widget-container:hover + .e-element-color-picker:not(.e-picker-hidden) {
  opacity: 1;
  pointer-events: all;
}
.e-ui-state--elements-color-picker-color-picking__on .elementor-section:hover {
  outline: 1px solid #2563EB;
}

/**
* Contact Buttons has position: fixed, therefore the container results as `empty`
* and it's not visible in the preview when the aside is hidden.
*/
.elementor-editor-preview .elementor-element:has(.e-contact-buttons) .elementor-widget-empty,
.elementor-editor-preview .elementor-element:has(.e-floating-bars) .elementor-widget-empty {
  display: block;
}

[class^=eicon-flex], [class*=" eicon-flex"] {
  transition: 0.3s all;
  --is-ltr: 1;
  --is-rtl: 0;
  --rotation-direction: calc(var(--is-ltr) - var(--is-rtl));
}
[class^=eicon-flex].eicon-inline, [class*=" eicon-flex"].eicon-inline {
  max-height: 1em;
  max-width: 1em;
}
[class^=eicon-flex]:is(.eicon-justify-start-h, .eicon-justify-end-h), [class*=" eicon-flex"]:is(.eicon-justify-start-h, .eicon-justify-end-h) {
  --rotation-direction: calc(var(--is-ltr) + var(--is-rtl));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow) {
  transform: rotate(calc(var(--rotation-direction) * 90deg));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__column-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__column-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__row [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-rtl) * 180deg));
}
.e-ui-state--document-direction-mode__row-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-ltr) * 180deg));
}

html.elementor-html {
  margin-block-start: 0 !important;
}

.elementor-edit-area {
  position: relative;
}

.elementor.loading {
  opacity: 0.5;
}

.elementor-edit-area-active {
  --primary-color: var(--e-p-border-con);
  --secondary-color: var(--e-p-border-con-hover);
  --outline-color: var(--e-p-border-con-active);
}
.elementor-edit-area-active .e-con {
  --primary-color: var(--e-p-border-con);
  --secondary-color: var(--e-p-border-con-hover);
  --outline-color: var( --outline-color );
}
.elementor-edit-area-active .e-con.e-dragging-over, .elementor-edit-area-active .e-con.elementor-dragging-on-child, .elementor-edit-area-active .e-con.e-con-boxed.elementor-html5dnd-current-element {
  outline: 1px solid var(--outline-color);
}
.elementor-edit-area-active .e-grid-outline {
  display: none;
}
.elementor-edit-area-active .elementor-element-editable.e-grid.e-con-full > .e-grid-outline {
  padding-block-start: var(--bc-padding-block-start, var(--padding-block-start));
  padding-block-end: var(--bc-padding-block-end, var(--padding-block-end));
  display: grid;
  position: absolute;
  top: 0;
  bottom: 0;
  pointer-events: none;
  grid-gap: var(--gap);
  justify-items: var(--justify-items);
  align-items: var(--align-items);
  grid-auto-flow: var(--grid-auto-flow);
  justify-content: var(--grid-justify-content);
  align-content: var(--grid-align-content);
  min-height: var(--min-height);
  padding-inline-end: var(--bc-padding-inline-end, var(--padding-inline-end));
  padding-inline-start: var(--bc-padding-inline-start, var(--padding-inline-start));
  width: 100%;
  left: 0;
}
.elementor-edit-area-active .elementor-element-editable.e-grid.e-con-boxed > .e-con-inner {
  position: relative;
}
.elementor-edit-area-active .elementor-element-editable.e-grid.e-con-boxed > .e-con-inner > .e-grid-outline {
  padding-block-start: var(--bc-padding-block-start, var(--padding-block-start));
  padding-block-end: var(--bc-padding-block-end, var(--padding-block-end));
  display: grid;
  position: absolute;
  top: 0;
  bottom: 0;
  pointer-events: none;
  grid-gap: var(--gap);
  justify-items: var(--justify-items);
  align-items: var(--align-items);
  grid-auto-flow: var(--grid-auto-flow);
  justify-content: var(--grid-justify-content);
  align-content: var(--grid-align-content);
  min-height: var(--min-height);
  width: var(--width);
  margin: 0 auto;
}
.elementor-edit-area-active .elementor-element-editable.e-grid .e-grid-outline-item {
  border: 1px dashed #9DA5AE;
  pointer-events: none;
  width: 100%;
  height: 100%;
}
.elementor-edit-area-active .elementor-inner-section:first-child {
  margin-block-start: 15px;
}
.elementor-edit-area-active .elementor-widget-wrap.elementor-element-empty {
  min-height: 30px;
}
@media (min-width: 768px) {
  .elementor-edit-area-active .elementor-widget-wrap.elementor-element-empty {
    margin: 10px;
  }
}
.elementor-edit-area-active .elementor-column {
  min-width: 25px;
}
.elementor-edit-area-active .elementor-widget.elementor-loading {
  opacity: 0.3;
}
.elementor-edit-area-active .elementor-widget.elementor-element-edit-mode:hover {
  box-shadow: 0 0 0 1px var(--e-p-border-widget);
}
.elementor-edit-area-active .elementor-widget.elementor-element-editable, .elementor-edit-area-active .elementor-widget.elementor-element-editable:hover {
  box-shadow: 0 0 0 2px var(--e-p-border-widget);
}
.elementor-edit-area-active .elementor-widget:not(:hover) .elementor-editor-element-settings {
  display: none;
}
.elementor-edit-area-active .elementor-widget.ui-draggable-dragging {
  pointer-events: none;
}
.elementor-edit-area-active .elementor-editor-element-setting {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  font-size: 11px;
  color: var(--e-p-border-widget-invert);
  width: 25px;
  transition: var(--e-a-transition-hover);
}
.elementor-edit-area-active .elementor-inline-editing {
  min-height: 15px;
}
.elementor-edit-area-active .elementor-edit-hidden {
  display: none;
}

.elementor-section-wrap:empty {
  min-height: 25px;
}
.elementor-section-wrap > :first-child > .elementor-element-overlay {
  z-index: 9999;
}

.elementor-element > .elementor-element-overlay {
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: 0;
  letter-spacing: 0;
  z-index: 9998;
  pointer-events: none;
}
.elementor-element-empty .elementor-sortable-placeholder {
  display: none;
}
.elementor-element.elementor-widget-empty {
  background-color: rgba(213, 216, 220, 0.8);
}
.elementor-element.elementor-widget-empty .elementor-widget-empty-icon {
  color: #BABFC5;
  font-size: 22px;
  display: block;
  text-align: center;
  padding: 10px 0;
}
.elementor-element.elementor-widget-empty[data-atomic] .elementor-widget-empty-icon {
  background-color: rgb(243, 243, 244);
  width: 100%;
}
.elementor-element:not(:hover):not(.elementor-element-editable) > .elementor-element-overlay .elementor-editor-element-settings {
  display: none;
}
.elementor-element--toggle-edit-tools > .elementor-element-overlay .elementor-editor-element-edit:not(.elementor-active) ~ * {
  width: 0;
  font-size: 0;
}
.elementor-element[data-side=top]:before, .elementor-element[data-side=bottom] + .elementor-element:before {
  content: "";
  background-color: var(--e-p-draggable-color);
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  display: block;
}
.elementor-element[data-side=bottom]:last-child:after {
  content: "";
  background-color: var(--e-p-draggable-color);
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  display: block;
}
.elementor-element.elementor-absolute, .elementor-element.elementor-fixed {
  cursor: grab;
}
.elementor-element.elementor-absolute:active, .elementor-element.elementor-fixed:active {
  cursor: grabbing;
}
.elementor-element.elementor-absolute .eicon-edit:before, .elementor-element.elementor-fixed .eicon-edit:before {
  content: "\e902";
}

.elementor-editor-element-settings {
  position: absolute;
  display: flex;
  height: 26px;
  list-style: none;
  margin: 0;
  padding: 0;
  font-family: var(--e-a-font-family);
  font-size: 13px;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  z-index: 1;
  pointer-events: all;
  transition: var(--e-a-transition-hover);
}

@media (min-width: 1025px) {
  .elementor-editor-element-edit {
    cursor: move;
  }
}

.elementor-empty-view {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.elementor-first-add {
  text-align: center;
  border: 1px dashed var(--e-a-border-color-bold);
  display: flex;
  height: 100%;
  width: 100%;
  position: absolute;
  align-items: center;
  justify-content: center;
}
.elementor-first-add .elementor-icon {
  font-size: 19px;
  color: #9DA5AE;
  cursor: pointer;
}

.elementor-sortable-helper {
  position: absolute;
  cursor: move;
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-bg-active);
  border-color: var(--e-a-border-color-accent);
  color: var(--e-a-color-active);
  text-align: center;
}
.elementor-sortable-helper .icon {
  font-size: 28px;
  padding-block-start: 15px;
  line-height: 1;
}
.elementor-sortable-helper .title-wrapper {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.elementor-sortable-helper .title {
  font-size: 11px;
}

.elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  background-color: var(--e-p-draggable-color);
  animation-duration: 250ms;
  opacity: 0.9;
  width: 100%;
  align-self: stretch;
}
.e-con .elementor-first-add .elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  align-self: center;
}
.e-swappable--active > .elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  display: none;
}

.elementor-section-placeholder {
  height: 40px;
  animation-name: placeholder-section;
}

.elementor-widget-placeholder {
  height: 10px;
  animation-name: placeholder-widget;
}
.elementor-widget-placeholder.is-logical {
  margin-top: var(--e-placeholder-margin-top);
  margin-bottom: var(--e-placeholder-margin-bottom);
  margin-inline-start: var(--e-placeholder-margin-inline-start);
  width: var(--e-placeholder-width);
  min-width: 200px;
}

.elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add:after,
.elementor-first-add.elementor-html5dnd-current-element:after {
  content: "";
  background-color: var(--e-p-draggable-color);
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  width: 100%;
}
.e-con .elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add:after,
.e-con .elementor-first-add.elementor-html5dnd-current-element:after {
  display: none;
}
.elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add .elementor-icon,
.elementor-first-add.elementor-html5dnd-current-element .elementor-icon {
  display: none;
}

.elementor-draggable-over[data-dragged-element=section][data-dragged-is-inner=true] .elementor-inner-column .elementor-sortable-placeholder {
  display: none;
}

.elementor-editor-preview .elementor-element-overlay,
.elementor-editor-preview .elementor-empty,
.elementor-editor-preview .elementor-add-section,
.elementor-editor-preview .elementor-add-section-inline,
.elementor-editor-preview .elementor-empty-view,
.elementor-editor-preview .elementor-widget-empty {
  display: none;
}

.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile {
  display: inherit;
  background: repeating-linear-gradient(125deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 1px, transparent 2px, transparent 9px);
  border: 1px solid rgba(0, 0, 0, 0.02);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-section > .elementor-element-overlay, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con > .elementor-element-overlay {
  background-color: var(--e-a-bg-hover);
  mix-blend-mode: color;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-section:before, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con:before {
  content: "";
  display: block;
  position: absolute;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 9997;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con {
  display: var(--display);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-inner-section .elementor-container {
  width: 100%;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen > .elementor-widget-container, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen > .elementor-widget-wrap, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra:not(:has(> .elementor-widget-container)),
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile:not(:has(> .elementor-widget-container)) {
  filter: opacity(0.4) saturate(0);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-edit-hidden {
  display: none;
}

.e-youtube-base iframe {
  pointer-events: none;
}

.elementor-editor-section-settings,
.elementor-editor-container-settings,
.elementor-editor-e-flexbox-settings,
.elementor-editor-e-div-block-settings {
  height: 24px;
  top: 1px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background-color: var(--e-p-border-section);
  border-start-start-radius: 5px;
  border-start-end-radius: 5px;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
  stroke: transparent;
  stroke-width: 0;
  -webkit-text-stroke: 0 transparent;
}
.elementor-editor-section-settings i.eicon-handle,
.elementor-editor-container-settings i.eicon-handle,
.elementor-editor-e-flexbox-settings i.eicon-handle,
.elementor-editor-e-div-block-settings i.eicon-handle {
  font-size: 16px;
}
.elementor-editor-section-settings .elementor-editor-element-setting:hover,
.elementor-editor-container-settings .elementor-editor-element-setting:hover,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:hover,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:hover {
  background-color: var(--e-p-border-section-hover);
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:first-child,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:first-child {
  border-start-start-radius: 5px;
  border-start-end-radius: 0;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child:before,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child:before,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:first-child:before,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:first-child:before {
  content: "";
  position: absolute;
  inset-block-start: 2px;
  inset-inline-end: calc(100% - 1px);
  border-block-start: 22px solid transparent;
  border-block-end: 0 none transparent;
  border-inline-start: 0 none transparent;
  border-inline-end: 12px solid var(--e-p-border-section);
  transition: var(--e-a-transition-hover);
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child:hover:before,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child:hover:before,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:first-child:hover:before,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:first-child:hover:before {
  border-inline-end-color: var(--e-p-border-section-hover);
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:last-child,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:last-child {
  border-start-start-radius: 0;
  border-start-end-radius: 5px;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child:after,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child:after,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:last-child:after,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:last-child:after {
  content: "";
  position: absolute;
  inset-block-start: 2px;
  inset-inline-start: calc(100% - 1px);
  border-block-start: 22px solid transparent;
  border-block-end: 0 none transparent;
  border-inline-start: 12px solid var(--e-p-border-section);
  border-inline-end: 0 none transparent;
  transition: var(--e-a-transition-hover);
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child:hover:after,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child:hover:after,
.elementor-editor-e-flexbox-settings .elementor-editor-element-setting:last-child:hover:after,
.elementor-editor-e-div-block-settings .elementor-editor-element-setting:last-child:hover:after {
  border-inline-start-color: var(--e-p-border-section-hover);
}

.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings {
  transform: translateX(-50%);
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 5px;
  border-end-end-radius: 5px;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child {
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 5px;
  border-end-end-radius: 0;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child:before {
  top: 0;
  border-block-start: 0 none transparent;
  border-block-end: 22px solid transparent;
  border-inline-start: 0 none transparent;
  border-inline-end: 12px solid var(--e-p-border-section);
  transition: var(--e-a-transition-hover);
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child {
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 0;
  border-end-end-radius: 5px;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child:after {
  top: 0;
  border-block-start: 0 none transparent;
  border-block-end: 22px solid transparent;
  border-inline-start: 12px solid var(--e-p-border-section);
  border-inline-end: 0 none transparent;
  transition: var(--e-a-transition-hover);
}

.elementor-column > .elementor-element-overlay {
  inset-block-end: 0;
  inset-inline-start: 0;
}
.elementor-column > .elementor-element-overlay:after {
  position: absolute;
  inset: 0;
  outline: 1px dashed var(--e-p-border-column);
}
.elementor-column:hover > .elementor-element-overlay:after {
  content: "";
}
.elementor-column.elementor-element-editable > .elementor-element-overlay:after {
  content: "";
  outline: 2px solid var(--e-p-border-column);
}
.elementor-column.elementor-dragging-on-child > .elementor-element-overlay {
  border: 1px solid var(--e-p-draggable-color);
}
.elementor-column.elementor-dragging-on-child > .elementor-element-overlay:after {
  display: none;
}
.elementor-column > .ui-resizable-e, .elementor-column > .ui-resizable-w {
  cursor: col-resize;
  width: 7px;
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: -5px;
  height: 100%;
}
.elementor-column:last-of-type > .ui-resizable-e, .elementor-column:last-of-type > .ui-resizable-w {
  display: none !important;
}
@media (max-width: 1024px) {
  .elementor-column > .ui-resizable-e, .elementor-column > .ui-resizable-w {
    display: none !important;
  }
}

.elementor-editor-column-settings {
  inset-block-start: -1px;
  inset-inline-start: -1px;
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 0;
  border-end-end-radius: 3px;
  overflow: hidden;
}
.elementor-editor-column-settings .elementor-editor-element-setting {
  background-color: var(--e-p-border-column);
}
.elementor-editor-column-settings .elementor-editor-element-setting:hover {
  background-color: var(--e-p-border-column-hover);
}

.elementor-column-placeholder {
  position: relative;
}
.elementor-column-placeholder:before, .elementor-column-placeholder:after {
  content: "";
  position: absolute;
  top: 10px;
  bottom: 10px;
  right: 10px;
  left: 10px;
}
.elementor-column-placeholder:before {
  border: 1px solid var(--e-p-border-column);
}
.elementor-column-placeholder:after {
  border: 1px dashed var(--e-a-color-white);
}

.elementor-column-percents-tooltip {
  position: absolute;
  display: none;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-size: 10px;
  background-color: var(--e-p-border-column);
  width: 40px;
  padding: 3.5px 0;
  text-align: center;
  z-index: 1;
  line-height: 1;
}
.elementor-column-percents-tooltip:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 8.5px solid transparent;
  top: 0;
}
.elementor-column-percents-tooltip[data-side=left] {
  border-radius: 3px 0 0 3px;
  right: 15px;
}
.elementor-column-percents-tooltip[data-side=left]:after {
  left: 100%;
  border-left-color: var(--e-p-border-column);
  border-right-width: 0;
}
.elementor-column-percents-tooltip[data-side=right] {
  border-radius: 0 3px 3px 0;
  left: 15px;
}
.elementor-column-percents-tooltip[data-side=right]:after {
  right: 100%;
  border-right-color: var(--e-p-border-column);
  border-left-width: 0;
}

.elementor-editor-widget-settings {
  z-index: 2;
  inset-block-start: -1px;
  inset-inline-end: -1px;
  flex-direction: row-reverse;
  border-start-start-radius: 0;
  border-start-end-radius: 0;
  border-end-start-radius: 3px;
  border-end-end-radius: 0;
  overflow: hidden;
}
.elementor-editor-widget-settings .elementor-editor-element-setting {
  background-color: var(--e-p-border-widget);
}
.elementor-editor-widget-settings .elementor-editor-element-setting:hover {
  background-color: var(--e-p-border-widget-hover);
}

.e-widget-pro-promotion {
  border: 2px dashed var(--e-a-border-color-bold);
  background-color: var(--e-a-color-white);
  padding: 20px;
}
.e-widget-pro-promotion .e-container {
  max-width: 600px;
  margin: auto;
}
.e-widget-pro-promotion .e-badge {
  border: 1px solid var(--e-a-color-accent);
  color: var(--e-a-color-accent);
  background: var(--e-a-color-white);
  border-radius: 100px;
  padding: 4px 12px;
  font-size: 0.8em;
}
.e-widget-pro-promotion p {
  margin-block-start: 1em;
  margin-block-end: 1em;
  text-align: center;
}
.e-widget-pro-promotion p img {
  display: block;
  margin: 0 auto 20px;
}
.e-widget-pro-promotion .e-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}
.e-widget-pro-promotion .e-btn {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  padding: 8px 16px;
  border-radius: var(--e-a-border-radius);
  background-color: var(--e-a-btn-bg-accent);
  color: var(--e-a-btn-color-invert);
  transition: var(--e-a-transition-hover);
}
.e-widget-pro-promotion .e-btn:hover, .e-widget-pro-promotion .e-btn:focus {
  background-color: var(--e-a-btn-bg-accent-hover);
  color: var(--e-a-btn-color-invert);
}
.e-widget-pro-promotion .e-btn:active {
  background-color: var(--e-a-btn-bg-accent-active);
}
.e-widget-pro-promotion .e-btn.e-btn-txt {
  background: transparent;
  color: var(--e-a-color-txt);
}
.e-widget-pro-promotion .e-btn.e-btn-txt:hover, .e-widget-pro-promotion .e-btn.e-btn-txt:focus {
  background: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}

.elementor-section > .elementor-element-overlay,
.e-con > .elementor-element-overlay {
  inset-block-end: 0;
  inset-inline-start: 0;
}
.elementor-section > .elementor-element-overlay:after,
.e-con > .elementor-element-overlay:after {
  position: absolute;
  left: 1px;
  right: 1px;
  top: 1px;
  bottom: 1px;
  outline: 1px solid var(--e-p-border-con);
  animation: section-outline 0.75s;
}
.elementor-section:hover > .elementor-element-overlay:after,
.e-con:hover > .elementor-element-overlay:after {
  content: "";
}
.elementor-section.elementor-element-editable > .elementor-element-overlay:after,
.e-con.elementor-element-editable > .elementor-element-overlay:after {
  content: "";
  outline-width: 2px;
}

.e-con .e-con-inner .e-con > .elementor-element-overlay:after {
  outline: 1px dashed var(--e-p-border-con-in);
}
.e-con .e-con-inner .e-con.elementor-element-editable > .elementor-element-overlay:after {
  outline: 2px solid var(--e-p-border-con-in);
}
.e-con .elementor-empty-view {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100px;
  stroke: transparent;
  stroke-width: 0;
  -webkit-text-stroke: 0 transparent;
}
.e-con .elementor-empty-view .elementor-first-add {
  width: auto;
  height: auto;
  inset: 0;
}
.e-con:not(.e-grid) .elementor-empty-view {
  min-height: var(--min-height, 100px);
}
.e-con.e-grid .elementor-empty-view {
  min-width: 100px;
}
.e-con .elementor-widget-placeholder {
  --size: 10px;
  --margin-start: calc( -1 * var( --size ) );
  --margin-end: calc( -2 * var( --size ) );
  flex-shrink: 0;
  align-self: stretch;
  z-index: 1;
  pointer-events: none;
}
.e-con.e-con--row > .elementor-widget-placeholder, .e-con.e-con--row > .e-con-inner > .elementor-widget-placeholder {
  position: relative;
  z-index: 9999;
  width: var(--size);
  margin-inline-start: var(--margin-start);
  height: auto;
  min-height: 100%;
  animation-name: dnd-placeholder-widget-vertical;
  animation-fill-mode: both;
}
.e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .elementor-widget-placeholder, .e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .e-con-inner > .elementor-widget-placeholder {
  margin-inline-end: 0;
  margin-inline-start: calc(var(--margin-start) + var(--margin-end));
}
.e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > :not(.elementor-element) + .elementor-widget-placeholder, .e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .e-con-inner > :not(.elementor-element) + .elementor-widget-placeholder {
  margin-inline-end: var(--margin-end);
  margin-inline-start: var(--margin-start);
}
.e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-right, .e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-left, .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-right, .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-left {
  position: absolute;
  width: var(--size);
  height: 100%;
  top: 0;
  animation: grow-in-width 0.15s forwards;
}
.e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-right, .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-right {
  right: 0;
  left: auto;
}
.e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-left, .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-left {
  left: 0;
  right: auto;
}
.e-con.e-con--column > .elementor-widget-placeholder, .e-con.e-con--column > .e-con-inner > .elementor-widget-placeholder {
  height: var(--size);
  margin-block-start: var(--margin-start);
  margin-block-end: var(--margin-end);
  animation-name: dnd-placeholder-widget-horizontal;
}
.e-con.e-con--column > .elementor-widget-placeholder:nth-last-child(2) {
  margin-block-start: calc(2 * var(--margin-start));
  --margin-end: 0;
}
.e-con.e-con--column > .e-con-inner > .elementor-widget-placeholder:last-child {
  --margin-end: 0;
}
.e-con .elementor-sortable-helper {
  height: 84px;
  width: 125px;
  z-index: -1;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting {
  position: relative;
  background-color: var(--e-p-border-con-in);
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover {
  background-color: var(--e-p-border-con-in-hover);
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after {
  display: none;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit), .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit), .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit) {
  margin-inline-start: -25px;
  z-index: -1;
  transition: 0.3s all;
  will-change: margin-inline-start;
}
.e-con > .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove), .e-con-inner > .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove), .elementor-widget .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove) {
  margin-inline-start: 0;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-settings, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-settings, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-settings {
  inset: initial;
  transform: none;
  inset-inline-start: 0;
  top: 0;
  border-radius: 0;
  border-end-end-radius: 3px;
  height: auto;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before {
  content: "\e9b4";
  font-size: 20px;
  display: block;
  padding: 2px;
}

.e-div-block-base:has(.elementor-empty-view), .e-flexbox-base:has(.elementor-empty-view) {
  min-height: 120px;
}
.e-div-block-base > .elementor-empty-view.elementor-empty-view, .e-flexbox-base > .elementor-empty-view.elementor-empty-view {
  min-height: inherit;
}

body.rtl .e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-right, body.rtl .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-right {
  left: 0;
  right: auto;
}
body.rtl .e-con.e-grid.e-con--row .elementor-widget-placeholder.e-dragging-left, body.rtl .e-con.e-grid.e-con--row > .e-con-inner .elementor-widget-placeholder.e-dragging-left {
  right: 0;
  left: auto;
}

@keyframes dnd-placeholder-widget-vertical {
  0% {
    transform-origin: 0 50%;
    transform: translateX(50%) scaleX(0);
    opacity: 0;
  }
  100% {
    transform-origin: 0 50%;
    transform: translateX(50%) scaleX(1);
    opacity: 0.9;
  }
}
@keyframes dnd-placeholder-widget-horizontal {
  0% {
    transform-origin: 50% 0;
    transform: scaleY(0);
    opacity: 0;
  }
  100% {
    transform-origin: 50% 0;
    transform: scaleY(1);
    opacity: 0.9;
  }
}
@keyframes grow-in-width {
  from {
    width: 0;
  }
  50% {
    width: calc(var(--size) / 2);
  }
  to {
    width: var(--size);
  }
}
.e-con-select-preset {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 25px;
}
.e-con-select-preset[data-view=choose-preset] > *:not(.e-con-select-preset__inner) {
  display: none;
}
.e-con-select-preset[data-view=drop-area] > *:not(.elementor-first-add) {
  display: none;
}
.e-con-select-preset-grid {
  flex-wrap: wrap;
  gap: 20px;
}
.e-con-select-preset-grid .e-con-select-preset__list .e-con-preset rect {
  fill: transparent;
}
.e-con-select-preset__title {
  font-family: var(--e-a-font-family);
  font-size: 15px;
  font-weight: 400;
  color: var(--e-a-color-txt);
}
.e-con-select-preset__list {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 650px;
}
.e-con-select-preset__list .e-con-preset {
  cursor: pointer;
  flex-basis: 90px;
  padding: unset;
  margin: unset;
  border: unset;
  border-radius: unset;
  outline: unset;
  box-shadow: unset;
  text-shadow: unset;
  background-color: unset;
}
.e-con-select-preset__list .e-con-preset svg {
  width: 100%;
  height: auto;
}
.e-con-select-preset__list .e-con-preset svg:not(.exclude-rtl-scale) {
  scale: calc(1 * var(--direction-multiplier)) 1;
}
.e-con-select-preset__list .e-con-preset path {
  fill: var(--e-a-color-white);
}
.e-con-select-preset__list .e-con-preset rect {
  fill: var(--e-a-bg-active-bold);
  transition: 0.3s all;
}
.e-con-select-preset__list .e-con-preset:hover, .e-con-select-preset__list .e-con-preset:focus {
  border: unset;
  border-radius: unset;
  box-shadow: unset;
  text-shadow: unset;
  background-color: transparent;
}
.e-con-select-preset__list .e-con-preset:hover path, .e-con-select-preset__list .e-con-preset:focus path {
  stroke: var(--e-a-color-white);
}
.e-con-select-preset__list .e-con-preset:hover rect, .e-con-select-preset__list .e-con-preset:focus rect {
  fill: var(--e-a-color-txt);
}
.e-con-select-preset__list .e-con-preset[data-preset=r100] {
  scale: calc(1 * var(--direction-multiplier)) 1;
}
.e-con-shared-styles {
  font-family: var(--e-a-font-family);
  line-height: 1;
  color: var(--e-a-color-txt);
  min-height: 159px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 25px;
}
.e-con-select-type__title {
  font-size: 15px;
  font-weight: 400;
}
.e-con-select-type__icons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 40px;
}
.e-con-select-type__icons__icon {
  cursor: pointer;
}
.e-con-select-type__icons__icon svg {
  margin-block-end: 10px;
  width: 85px;
}
.e-con-select-type__icons__icon:hover svg rect, .e-con-select-type__icons__icon:focus svg rect {
  fill: var(--e-a-color-txt);
}
.e-con-select-type__icons__icon:hover svg path, .e-con-select-type__icons__icon:focus svg path {
  stroke: var(--e-a-color-white);
}
.e-con-select-type__icons__icon__subtitle {
  font-size: 14px;
  font-weight: 500;
}
.e-con .ui-resizable-e {
  right: 0;
}
.e-con .ui-resizable-w {
  left: 0;
}
/*# sourceMappingURL=editor-preview.css.map */