<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Article Generator - Asset Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .asset-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .asset-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        canvas {
            border: 1px solid #ddd;
            display: block;
            margin: 10px 0;
        }
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI SEO Article Generator - WordPress.org Assets</h1>
        
        <!-- Icon 256x256 -->
        <div class="asset-preview">
            <div class="asset-title">Icon 256x256</div>
            <canvas id="icon256" width="256" height="256"></canvas>
            <button onclick="downloadCanvas('icon256', 'icon-256x256.png')">Download icon-256x256.png</button>
        </div>

        <!-- Icon 128x128 -->
        <div class="asset-preview">
            <div class="asset-title">Icon 128x128</div>
            <canvas id="icon128" width="128" height="128"></canvas>
            <button onclick="downloadCanvas('icon128', 'icon-128x128.png')">Download icon-128x128.png</button>
        </div>

        <!-- Banner 772x250 -->
        <div class="asset-preview">
            <div class="asset-title">Banner 772x250</div>
            <canvas id="banner" width="772" height="250"></canvas>
            <button onclick="downloadCanvas('banner', 'banner-772x250.png')">Download banner-772x250.png</button>
        </div>

        <!-- Banner 1544x500 (Retina) -->
        <div class="asset-preview">
            <div class="asset-title">Banner 1544x500 (Retina)</div>
            <canvas id="bannerRetina" width="1544" height="500"></canvas>
            <button onclick="downloadCanvas('bannerRetina', 'banner-1544x500.png')">Download banner-1544x500.png</button>
        </div>
    </div>

    <script>
        // Helper function to download canvas as image
        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Draw Icon
        function drawIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Rounded rectangle background
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.125);
            ctx.fill();
            
            // AI Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.28}px -apple-system, Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('AI', size / 2, size / 2 - size * 0.05);
            
            // SEO Arrow
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.02;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // Arrow pointing up
            ctx.beginPath();
            ctx.moveTo(size / 2 - size * 0.08, size * 0.7);
            ctx.lineTo(size / 2, size * 0.62);
            ctx.lineTo(size / 2 + size * 0.08, size * 0.7);
            ctx.stroke();
            
            // Arrow stem
            ctx.beginPath();
            ctx.moveTo(size / 2, size * 0.62);
            ctx.lineTo(size / 2, size * 0.75);
            ctx.stroke();
            
            // Circuit dots (subtle)
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            const positions = [
                [0.2, 0.2], [0.8, 0.2], [0.2, 0.8], [0.8, 0.8]
            ];
            positions.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(size * x, size * y, size * 0.015, 0, Math.PI * 2);
                ctx.fill();
            });
        }

        // Draw Banner
        function drawBanner(canvasId, width, height) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            const scale = width / 772; // Base scale on standard banner width
            
            // Gradient background
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#667eea');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Add subtle pattern overlay
            ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
            for (let i = 0; i < width; i += 100 * scale) {
                for (let j = 0; j < height; j += 100 * scale) {
                    ctx.beginPath();
                    ctx.arc(i, j, 2 * scale, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // Main title
            ctx.fillStyle = 'white';
            ctx.font = `bold ${42 * scale}px -apple-system, Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText('AI SEO Article Generator', width / 2, height * 0.3);
            
            // Subtitle
            ctx.font = `${20 * scale}px -apple-system, Arial, sans-serif`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillText('Powered by Claude & OpenAI', width / 2, height * 0.45);
            
            // Feature badges
            const features = [
                '⚡ Generate in Seconds',
                '🌐 Hebrew & English',
                '🔄 Background Processing'
            ];
            
            ctx.font = `${16 * scale}px -apple-system, Arial, sans-serif`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            
            const startX = width / 2 - 200 * scale;
            features.forEach((feature, index) => {
                ctx.fillText(feature, startX + (index * 140 * scale), height * 0.7);
            });
            
            // AI circuit decoration
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 2 * scale;
            
            // Left decoration
            ctx.beginPath();
            ctx.moveTo(50 * scale, height / 2);
            ctx.lineTo(100 * scale, height / 2);
            ctx.moveTo(75 * scale, height / 2 - 25 * scale);
            ctx.lineTo(75 * scale, height / 2 + 25 * scale);
            ctx.stroke();
            
            // Right decoration
            ctx.beginPath();
            ctx.moveTo(width - 50 * scale, height / 2);
            ctx.lineTo(width - 100 * scale, height / 2);
            ctx.moveTo(width - 75 * scale, height / 2 - 25 * scale);
            ctx.lineTo(width - 75 * scale, height / 2 + 25 * scale);
            ctx.stroke();
        }

        // Generate all assets
        drawIcon('icon256', 256);
        drawIcon('icon128', 128);
        drawBanner('banner', 772, 250);
        drawBanner('bannerRetina', 1544, 500);
    </script>
</body>
</html>