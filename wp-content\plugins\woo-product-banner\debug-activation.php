<?php
/**
 * Debug script per l'attivazione del plugin
 * Questo file aiuta a identificare problemi durante l'attivazione
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Funzione per loggare errori di attivazione
 */
function wpb_log_activation_error($message) {
    error_log('WPB Activation Error: ' . $message);
    
    // Salva anche in un file specifico per debug
    $log_file = WPB_PLUGIN_PATH . 'activation-debug.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

/**
 * Test di attivazione sicuro
 */
function wpb_safe_activation_test() {
    try {
        // Test 1: Verifica che WordPress sia caricato
        if (!function_exists('wp_get_current_user')) {
            wpb_log_activation_error('WordPress non completamente caricato');
            return false;
        }
        
        // Test 2: Verifica che WooCommerce sia disponibile
        if (!class_exists('WooCommerce')) {
            wpb_log_activation_error('WooCommerce non disponibile durante attivazione');
            // Non è un errore fatale, il plugin mostrerà un avviso
        }
        
        // Test 3: Verifica che le costanti siano definite correttamente
        if (!defined('WPB_PLUGIN_PATH') || empty(WPB_PLUGIN_PATH)) {
            wpb_log_activation_error('WPB_PLUGIN_PATH non definita correttamente');
            return false;
        }
        
        if (!defined('WPB_PLUGIN_URL') || empty(WPB_PLUGIN_URL)) {
            wpb_log_activation_error('WPB_PLUGIN_URL non definita correttamente: ' . (defined('WPB_PLUGIN_URL') ? WPB_PLUGIN_URL : 'undefined'));
            return false;
        }
        
        // Test 4: Verifica che i file delle classi esistano
        $required_files = [
            'includes/class-database.php',
            'includes/class-admin.php',
            'includes/class-frontend.php',
            'includes/class-security.php'
        ];
        
        foreach ($required_files as $file) {
            $file_path = WPB_PLUGIN_PATH . $file;
            if (!file_exists($file_path)) {
                wpb_log_activation_error("File richiesto mancante: $file_path");
                return false;
            }
        }
        
        // Test 5: Verifica che le classi siano caricate
        $required_classes = [
            'WPB_Database',
            'WPB_Admin', 
            'WPB_Frontend',
            'WPB_Security'
        ];
        
        foreach ($required_classes as $class) {
            if (!class_exists($class)) {
                wpb_log_activation_error("Classe richiesta non caricata: $class");
                return false;
            }
        }
        
        // Test 6: Test di istanziazione delle classi
        try {
            $database = new WPB_Database();
            if (!$database) {
                wpb_log_activation_error('Impossibile istanziare WPB_Database');
                return false;
            }
        } catch (Exception $e) {
            wpb_log_activation_error('Errore istanziazione WPB_Database: ' . $e->getMessage());
            return false;
        }
        
        try {
            $admin = new WPB_Admin($database);
            if (!$admin) {
                wpb_log_activation_error('Impossibile istanziare WPB_Admin');
                return false;
            }
        } catch (Exception $e) {
            wpb_log_activation_error('Errore istanziazione WPB_Admin: ' . $e->getMessage());
            return false;
        }
        
        try {
            $frontend = new WPB_Frontend($database);
            if (!$frontend) {
                wpb_log_activation_error('Impossibile istanziare WPB_Frontend');
                return false;
            }
        } catch (Exception $e) {
            wpb_log_activation_error('Errore istanziazione WPB_Frontend: ' . $e->getMessage());
            return false;
        }
        
        try {
            $security = new WPB_Security();
            if (!$security) {
                wpb_log_activation_error('Impossibile istanziare WPB_Security');
                return false;
            }
        } catch (Exception $e) {
            wpb_log_activation_error('Errore istanziazione WPB_Security: ' . $e->getMessage());
            return false;
        }
        
        wpb_log_activation_error('Tutti i test di attivazione superati con successo');
        return true;
        
    } catch (Exception $e) {
        wpb_log_activation_error('Errore generale durante test attivazione: ' . $e->getMessage());
        return false;
    } catch (Error $e) {
        wpb_log_activation_error('Errore fatale durante test attivazione: ' . $e->getMessage());
        return false;
    }
}

/**
 * Hook per eseguire il test di attivazione
 */
add_action('init', function() {
    // Esegui il test solo se siamo in admin e il plugin è appena stato attivato
    if (is_admin() && get_transient('wpb_activation_test')) {
        delete_transient('wpb_activation_test');
        
        if (!wpb_safe_activation_test()) {
            // Se il test fallisce, disattiva il plugin
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>WooCommerce Product Banner:</strong> Il plugin è stato disattivato a causa di errori. ';
                echo 'Controlla il file activation-debug.log nella cartella del plugin per dettagli.';
                echo '</p></div>';
            });
            
            // Disattiva il plugin
            deactivate_plugins(WPB_PLUGIN_BASENAME);
        }
    }
}, 1);

/**
 * Imposta il flag per il test di attivazione
 */
register_activation_hook(WPB_PLUGIN_BASENAME, function() {
    set_transient('wpb_activation_test', true, 60); // Test per 1 minuto
    wpb_log_activation_error('Plugin attivato, test di attivazione programmato');
});

/**
 * Pulizia durante disattivazione
 */
register_deactivation_hook(WPB_PLUGIN_BASENAME, function() {
    delete_transient('wpb_activation_test');
    wpb_log_activation_error('Plugin disattivato');
});
?>
