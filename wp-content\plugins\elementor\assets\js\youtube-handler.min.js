/*! elementor - v3.31.0 - 11-08-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[383],{4087:e=>{e.exports=elementorV2.frontendHandlers},6310:(e,t,o)=>{var n=o(4087);const loadYouTubeAPI=()=>new Promise(e=>{if(window.YT&&window.YT.loaded)return void e(window.YT);const t="https://www.youtube.com/iframe_api";if(!document.querySelector(`script[src="${t}"]`)){const e=document.createElement("script");e.src=t;const o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(e,o)}const checkYT=()=>{window.YT&&window.YT.loaded?e(window.YT):setTimeout(checkYT,350)};checkYT()});(0,n.register)({elementType:"e-youtube",uniqueId:"e-youtube-handler",callback:e=>{let{element:t}=e;const o=document.createElement("div");o.style.height="100%",t.appendChild(o);const n=t.getAttribute("data-settings"),r=n?JSON.parse(n):{},s=(e=>{const t=e.match(/^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/(?:(?:watch)?\?(?:.*&)?vi?=|(?:embed|v|vi|user|shorts)\/))([^?&"'>]+)/);return t?t[1]:null})(r.source);if(!s)return;let c,a;const prepareYTVideo=e=>{const t={videoId:s,events:{onReady:()=>{r.mute&&c.mute(),r.autoplay&&c.playVideo()},onStateChange:t=>{t.data===e.PlayerState.ENDED&&r.loop&&c.seekTo(r.start||0)}},playerVars:{controls:r.controls?1:0,rel:r.rel?0:1,cc_load_policy:r.cc_load_policy?1:0,autoplay:r.autoplay?1:0,start:r.start,end:r.end}};return r.privacy&&(t.host="https://www.youtube-nocookie.com",t.origin=window.location.hostname),c=new e.Player(o,t),c};return r.lazyload?(a=new IntersectionObserver(e=>{e[0].isIntersecting&&(loadYouTubeAPI().then(e=>prepareYTVideo(e)),a.unobserve(t))}),a.observe(t)):loadYouTubeAPI().then(e=>prepareYTVideo(e)),()=>{c&&"function"==typeof c.destroy&&(c.destroy(),c=null),t.contains(o)&&t.removeChild(o),a&&"function"==typeof a.disconnect&&(a.disconnect(),a=null)}}})}},e=>{var t;t=6310,e(e.s=t)}]);