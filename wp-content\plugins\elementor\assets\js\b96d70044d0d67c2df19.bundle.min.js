/*! elementor - v3.31.0 - 11-08-2025 */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[6214],{2348:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedRepeaterRemoveContainer=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(41621)),c=r(n(87861)),l=r(n(20890)),d=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var f=t.NestedRepeaterRemoveContainer=function(e){function NestedRepeaterRemoveContainer(){return(0,o.default)(this,NestedRepeaterRemoveContainer),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedRepeaterRemoveContainer,arguments)}return(0,c.default)(NestedRepeaterRemoveContainer,e),(0,a.default)(NestedRepeaterRemoveContainer,[{key:"getId",value:function getId(){return"document/repeater/remove--nested-elements-remove-container"}},{key:"getCommand",value:function getCommand(){return"document/repeater/remove"}},{key:"getConditions",value:function getConditions(e){var t=$e.commands.isCurrentFirstTrace(this.getCommand());return function _superPropGet(e,t,n,r){var o=(0,s.default)((0,u.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(NestedRepeaterRemoveContainer,"getConditions",this,3)([e])&&t}},{key:"apply",value:function apply(e){var t=e.container,n=e.index;$e.run("document/elements/delete",{container:(0,d.findChildContainerOrFail)(t,n),force:!0});var r=t.settings.get("widgetType");(0,d.shouldUseAtomicRepeaters)(r)&&elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/nested-container/atomic-repeater",{detail:{container:t,action:{type:"remove"}}}))}}])}(l.default);t.default=f},12087:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(41621)),c=r(n(87861)),l=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function NestedModelBase(){return(0,o.default)(this,NestedModelBase),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedModelBase,arguments)}return(0,c.default)(NestedModelBase,e),(0,a.default)(NestedModelBase,[{key:"initialize",value:function initialize(e){this.config=elementor.widgetsCache[e.widgetType],this.set("supportRepeaterChildren",!0),0===this.get("elements").length&&$e.commands.currentTrace.includes("document/elements/create")&&this.onElementCreate(),function _superPropGet(e,t,n,r){var o=(0,s.default)((0,u.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(NestedModelBase,"initialize",this,3)([e])}},{key:"isValidChild",value:function isValidChild(e){var t=this.get("elType");return"container"===e.get("elType")&&"widget"===t&&(0,l.isWidgetSupportNesting)(this.get("widgetType"))&&e.get("isLocked")}},{key:"getDefaultChildren",value:function getDefaultChildren(){var e=this.config.defaults,t=[];return e.elements.forEach(function(e){e.id=elementorCommon.helpers.getUniqueId(),e.settings=e.settings||{},e.elements=e.elements||[],e.isLocked=!0,t.push(e)}),t}},{key:"onElementCreate",value:function onElementCreate(){this.set("elements",this.getDefaultChildren())}}])}(elementor.modules.elements.models.Element)},14718:(e,t,n)=>{var r=n(29402);e.exports=function _superPropBase(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=r(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,n)=>{var r=n(10564).default,o=n(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},20890:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(87861)),c=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Base(){return(0,o.default)(this,Base),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,Base,arguments)}return(0,s.default)(Base,e),(0,a.default)(Base,[{key:"getContainerType",value:function getContainerType(){return"widget"}},{key:"getConditions",value:function getConditions(e){return(0,c.isWidgetSupportNesting)(e.container.model.get("widgetType"))}}])}($e.modules.hookData.After)},23574:(e,t,n)=>{"use strict";var r=n(96784),o=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(39805)),i=r(n(40989)),u=r(n(15118)),s=r(n(29402)),c=r(n(41621)),l=r(n(87861)),d=r(n(85707)),f=r(n(12087)),p=r(n(24305)),v=r(n(71206)),C=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var n=new WeakMap,r=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,u={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return u;if(a=t?r:n){if(a.has(e))return a.get(e);a.set(e,u)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(i.get||i.set)?a(u,s,i):u[s]=e[s]);return u}(e,t)}(n(35874));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Component(){var e;(0,a.default)(this,Component);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e=function _callSuper(e,t,n){return t=(0,s.default)(t),(0,u.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,s.default)(e).constructor):t.apply(e,n))}(this,Component,[].concat(n)),(0,d.default)(e,"exports",{NestedModelBase:f.default,NestedViewBase:p.default}),e}return(0,l.default)(Component,e),(0,i.default)(Component,[{key:"registerAPI",value:function registerAPI(){!function _superPropGet(e,t,n,r){var o=(0,c.default)((0,s.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(Component,"registerAPI",this,3)([]),elementor.addControlView("nested-elements-repeater",v.default)}},{key:"getNamespace",value:function getNamespace(){return"nested-elements/nested-repeater"}},{key:"defaultHooks",value:function defaultHooks(){return this.importHooks(C)}}])}($e.modules.ComponentBase)},24305:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(41621)),c=r(n(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}function _superPropGet(e,t,n,r){var o=(0,s.default)((0,u.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}t.default=function(e){function NestedViewBase(){return(0,o.default)(this,NestedViewBase),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedViewBase,arguments)}return(0,c.default)(NestedViewBase,e),(0,a.default)(NestedViewBase,[{key:"getChildViewContainer",value:function getChildViewContainer(e,t){var n=this.model.config.defaults,r=n.elements_placeholder_selector,o=n.child_container_placeholder_selector;return void 0!==t&&void 0!==t._index&&o?e.$el.find("".concat(o,":nth-child(").concat(t._index+1,")")):r?e.$el.find(this.model.config.defaults.elements_placeholder_selector):_superPropGet(NestedViewBase,"getChildViewContainer",this,3)([e,t])}},{key:"getChildType",value:function getChildType(){return["container"]}},{key:"onRender",value:function onRender(){_superPropGet(NestedViewBase,"onRender",this,3)([]),this.normalizeAttributes()}}])}(elementor.modules.elements.views.BaseWidget)},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},35874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NestedRepeaterCreateContainer",{enumerable:!0,get:function get(){return r.NestedRepeaterCreateContainer}}),Object.defineProperty(t,"NestedRepeaterDuplicateContainer",{enumerable:!0,get:function get(){return i.NestedRepeaterDuplicateContainer}}),Object.defineProperty(t,"NestedRepeaterFocusCurrentEditedContainer",{enumerable:!0,get:function get(){return u.NestedRepeaterFocusCurrentEditedContainer}}),Object.defineProperty(t,"NestedRepeaterMoveContainer",{enumerable:!0,get:function get(){return a.NestedRepeaterMoveContainer}}),Object.defineProperty(t,"NestedRepeaterRemoveContainer",{enumerable:!0,get:function get(){return o.NestedRepeaterRemoveContainer}});var r=n(96607),o=n(2348),a=n(69666),i=n(90768),u=n(99948)},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},41621:(e,t,n)=>{var r=n(14718);function _get(){return e.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=r(e,t);if(o){var a=Object.getOwnPropertyDescriptor(o,t);return a.get?a.get.call(arguments.length<3?e:n):a.value}},e.exports.__esModule=!0,e.exports.default=e.exports,_get.apply(null,arguments)}e.exports=_get,e.exports.__esModule=!0,e.exports.default=e.exports},62161:(e,t,n)=>{"use strict";var r=n(12470).sprintf;function isWidgetSupportNesting(e){var t=elementor.widgetsCache[e];return!!t&&t.support_nesting}function isWidgetSupportAtomicRepeaters(e){var t=elementor.widgetsCache[e];return!!t&&t.support_improved_repeaters}Object.defineProperty(t,"__esModule",{value:!0}),t.extractNestedItemTitle=function extractNestedItemTitle(e,t){var n=e.view.model.config.defaults.elements_title;return r(n,t)},t.findChildContainerOrFail=function findChildContainerOrFail(e,t){var n=e.view.children.findByIndex(t);if(!n)throw new Error("Child container was not found for the current repeater item.");return n.getContainer()},t.isWidgetSupportAtomicRepeaters=isWidgetSupportAtomicRepeaters,t.isWidgetSupportNesting=isWidgetSupportNesting,t.shouldUseAtomicRepeaters=function shouldUseAtomicRepeaters(e){return isWidgetSupportNesting(e)&&isWidgetSupportAtomicRepeaters(e)},t.sortViewsByModels=function sortViewsByModels(e){var t=e.model.get("elements").models,n=e.view.children,r={};return t.forEach(function(e,t){var o=n.findByModel(e);o._index=t,r[o.cid]=o}),r},t.widgetNodes=function widgetNodes(e){var t=elementor.widgetsCache[e];if(!t)return!1;return{targetContainer:t.target_container,node:t.node}}},69666:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedRepeaterMoveContainer=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(87861)),c=r(n(20890)),l=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var d=t.NestedRepeaterMoveContainer=function(e){function NestedRepeaterMoveContainer(){return(0,o.default)(this,NestedRepeaterMoveContainer),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedRepeaterMoveContainer,arguments)}return(0,s.default)(NestedRepeaterMoveContainer,e),(0,a.default)(NestedRepeaterMoveContainer,[{key:"getId",value:function getId(){return"document/repeater/move--nested-repeater-move-container"}},{key:"getCommand",value:function getCommand(){return"document/repeater/move"}},{key:"apply",value:function apply(e){var t=e.container,n=e.sourceIndex,r=e.targetIndex,o=$e.run("document/elements/move",{container:(0,l.findChildContainerOrFail)(t,n),target:t,options:{at:r,edit:!1}}),a=t.settings.get("widgetType");(0,l.shouldUseAtomicRepeaters)(a)&&(t.view.children._views=(0,l.sortViewsByModels)(t),elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/nested-container/atomic-repeater",{detail:{container:t,targetContainer:o,index:r,action:{type:"move"}}})))}}])}(c.default);t.default=d},71206:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(85707)),a=r(n(39805)),i=r(n(40989)),u=r(n(15118)),s=r(n(29402)),c=r(n(41621)),l=r(n(87861)),d=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Repeater(){return(0,a.default)(this,Repeater),function _callSuper(e,t,n){return t=(0,s.default)(t),(0,u.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,s.default)(e).constructor):t.apply(e,n))}(this,Repeater,arguments)}return(0,l.default)(Repeater,e),(0,i.default)(Repeater,[{key:"className",value:function className(){return function _superPropGet(e,t,n,r){var o=(0,c.default)((0,s.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(Repeater,"className",this,3)([]).replace("nested-elements-repeater","repeater")}},{key:"getDefaults",value:function getDefaults(){var e=this.options.container,t=e.model.config.defaults,n=e.children.length+1;return(0,o.default)({_id:""},t.repeater_title_setting,(0,d.extractNestedItemTitle)(e,n))}},{key:"onChildviewClickDuplicate",value:function onChildviewClickDuplicate(e){$e.run("document/repeater/duplicate",{container:this.options.container,name:this.model.get("name"),index:e._index}),this.toggleMinRowsClass()}},{key:"updateActiveRow",value:function updateActiveRow(){this.currentEditableChild&&$e.run("document/repeater/select",{container:this.container,index:this.currentEditableChild.itemIndex,options:{useHistory:!1}})}}])}(elementor.modules.controls.Repeater)},76214:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(40989)),a=r(n(39805)),i=r(n(96311));t.default=(0,o.default)(function NestedElementsModule(){(0,a.default)(this,NestedElementsModule),this.component=$e.components.register(new i.default)})},85707:(e,t,n)=>{var r=n(45498);e.exports=function _defineProperty(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,n)=>{var r=n(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},90768:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedRepeaterDuplicateContainer=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(87861)),c=r(n(20890)),l=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var d=t.NestedRepeaterDuplicateContainer=function(e){function NestedRepeaterDuplicateContainer(){return(0,o.default)(this,NestedRepeaterDuplicateContainer),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedRepeaterDuplicateContainer,arguments)}return(0,s.default)(NestedRepeaterDuplicateContainer,e),(0,a.default)(NestedRepeaterDuplicateContainer,[{key:"getId",value:function getId(){return"document/repeater/duplicate--nested-repeater-duplicate-container"}},{key:"getCommand",value:function getCommand(){return"document/repeater/duplicate"}},{key:"apply",value:function apply(e){var t=e.container,n=e.index,r=$e.run("document/elements/duplicate",{container:(0,l.findChildContainerOrFail)(t,n),options:{edit:!1}}),o=t.settings.get("widgetType");(0,l.shouldUseAtomicRepeaters)(o)?(t.view.children._views=(0,l.sortViewsByModels)(t),elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/nested-container/atomic-repeater",{detail:{container:t,targetContainer:r,index:n,action:{type:"duplicate"}}}))):t.render()}}])}(c.default);t.default=d},91270:e=>{function _setPrototypeOf(t,n){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,n)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},96311:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(41621)),c=r(n(87861)),l=r(n(23574));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Component(){return(0,o.default)(this,Component),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,Component,arguments)}return(0,c.default)(Component,e),(0,a.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"nested-elements"}},{key:"registerAPI",value:function registerAPI(){$e.components.register(new l.default),function _superPropGet(e,t,n,r){var o=(0,s.default)((0,u.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(Component,"registerAPI",this,3)([])}}])}($e.modules.ComponentBase)},96607:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedRepeaterCreateContainer=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(41621)),c=r(n(87861)),l=r(n(20890)),d=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var f=t.NestedRepeaterCreateContainer=function(e){function NestedRepeaterCreateContainer(){return(0,o.default)(this,NestedRepeaterCreateContainer),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedRepeaterCreateContainer,arguments)}return(0,c.default)(NestedRepeaterCreateContainer,e),(0,a.default)(NestedRepeaterCreateContainer,[{key:"getId",value:function getId(){return"document/repeater/insert--nested-repeater-create-container"}},{key:"getCommand",value:function getCommand(){return"document/repeater/insert"}},{key:"getConditions",value:function getConditions(e){var t=$e.commands.isCurrentFirstTrace(this.getCommand());return function _superPropGet(e,t,n,r){var o=(0,s.default)((0,u.default)(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}(NestedRepeaterCreateContainer,"getConditions",this,3)([e])&&t}},{key:"apply",value:function apply(e){var t=e.container,n=e.name,r=t.repeaters[n].children.length;$e.run("document/elements/create",{container:t,model:{elType:"container",isLocked:!0,_title:(0,d.extractNestedItemTitle)(t,r)},options:{edit:!1}});var o=t.settings.get("widgetType");(0,d.shouldUseAtomicRepeaters)(o)&&elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/nested-container/atomic-repeater",{detail:{container:t,action:{type:"create"}}}))}}])}(l.default);t.default=f},99948:(e,t,n)=>{"use strict";var r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedRepeaterFocusCurrentEditedContainer=void 0;var o=r(n(39805)),a=r(n(40989)),i=r(n(15118)),u=r(n(29402)),s=r(n(87861)),c=n(62161);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var l=t.NestedRepeaterFocusCurrentEditedContainer=function(e){function NestedRepeaterFocusCurrentEditedContainer(){return(0,o.default)(this,NestedRepeaterFocusCurrentEditedContainer),function _callSuper(e,t,n){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,NestedRepeaterFocusCurrentEditedContainer,arguments)}return(0,s.default)(NestedRepeaterFocusCurrentEditedContainer,e),(0,a.default)(NestedRepeaterFocusCurrentEditedContainer,[{key:"getCommand",value:function getCommand(){return"panel/editor/open"}},{key:"getId",value:function getId(){return"nested-repeater-focus-current-edited-container"}},{key:"getConditions",value:function getConditions(e){var t;if($e.commands.isCurrentFirstTrace("document/elements/create"))return!1;var n=e.view.container.getParentAncestry();return n.some(function(e){return(0,c.isWidgetSupportNesting)(e.model.get("widgetType"))})&&(this.navigationMap=this.getNavigationMapForContainers(n.filter(function(e){return"container"===e.type&&"widget"===e.parent.type})).filter(function(e){return e.index!==e.current})),null===(t=this.navigationMap)||void 0===t?void 0:t.length}},{key:"apply",value:function apply(){var e=1;this.navigationMap.forEach(function(t){var n=t.container,r=t.index;setTimeout(function(){$e.run("document/repeater/select",{container:n,index:r++,options:{useHistory:!1}})},250*e),++e})}},{key:"getNavigationMapForContainers",value:function getNavigationMapForContainers(e){return e.map(function(e){return{current:e.parent.model.get("editSettings").get("activeItemIndex"),container:e.parent,index:e.parent.children.indexOf(e)+1}}).reverse()}}])}($e.modules.hookUI.After);t.default=l}}]);