/* AI SEO Article Generator Admin Styles */

/* Base styles - direction and alignment set dynamically via PHP */
.ai-seo-article-generator-wrap {
    max-width: 1200px;
    margin: 20px 0;
    /* Direction and text-align are set dynamically via PHP based on WordPress locale */
}

/* Language-specific body classes for additional styling if needed */
body.ai-seo-ltr .ai-seo-article-generator-wrap {
    /* LTR-specific styles can be added here */
}

body.ai-seo-rtl .ai-seo-article-generator-wrap {
    /* RTL-specific styles can be added here */
}

.ai-seo-article-generator-wrap h1 {
    font-size: 2.2em;
    margin-bottom: 20px;
    color: #1e73be;
}

/* Dashboard Cards */
.ai-seo-article-generator-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ai-seo-article-generator-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.ai-seo-article-generator-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.ai-seo-article-generator-card h2 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.4em;
}

.ai-seo-article-generator-card p {
    color: #666;
    line-height: 1.6;
}

/* Features List */
.ai-seo-article-generator-features {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 25px;
    margin-top: 20px;
}

.ai-seo-article-generator-features ul {
    list-style: none;
    padding: 0;
}

.ai-seo-article-generator-features li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.ai-seo-article-generator-features li:last-child {
    border-bottom: none;
}

/* Connection Status */
.ai-seo-article-generator-status-success {
    color: #46b450;
    font-weight: bold;
}

.ai-seo-article-generator-status-error {
    color: #dc3232;
    font-weight: bold;
}

/* Wizard Steps */
.ai-seo-article-generator-step {
    display: none;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.ai-seo-article-generator-step.active {
    display: block;
}

.ai-seo-article-generator-step h2 {
    color: #1e73be;
    margin-bottom: 20px;
    font-size: 1.5em;
}

/* Form Elements */
/* Form alignment handled dynamically based on WordPress locale */
.ai-seo-article-generator-wrap .form-table th {
    padding: 11px 20px;
}

.ai-seo-article-generator-wrap .form-table td {
    padding: 11px 10px;
}

/* Input direction handled by body classes */
.ai-seo-article-generator-wrap input[type="text"],
.ai-seo-article-generator-wrap input[type="password"],
.ai-seo-article-generator-wrap textarea,
.ai-seo-article-generator-wrap select {
    width: 100%;
    max-width: 400px;
}

/* RTL specific styles */
body.ai-seo-rtl .ai-seo-article-generator-wrap .form-table th {
    text-align: right;
}

body.ai-seo-rtl .ai-seo-article-generator-wrap .form-table td {
    text-align: right;
}

body.ai-seo-rtl .ai-seo-article-generator-wrap input[type="text"],
body.ai-seo-rtl .ai-seo-article-generator-wrap input[type="password"],
body.ai-seo-rtl .ai-seo-article-generator-wrap textarea,
body.ai-seo-rtl .ai-seo-article-generator-wrap select {
    direction: rtl;
    text-align: right;
}

/* LTR specific styles */
body.ai-seo-ltr .ai-seo-article-generator-wrap .form-table th {
    text-align: left;
}

body.ai-seo-ltr .ai-seo-article-generator-wrap .form-table td {
    text-align: left;
}

body.ai-seo-ltr .ai-seo-article-generator-wrap input[type="text"],
body.ai-seo-ltr .ai-seo-article-generator-wrap input[type="password"],
body.ai-seo-ltr .ai-seo-article-generator-wrap textarea,
body.ai-seo-ltr .ai-seo-article-generator-wrap select {
    direction: ltr;
    text-align: left;
}

/* Progress Bar */
.ai-seo-article-generator-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.ai-seo-article-generator-progress {
    height: 100%;
    background: linear-gradient(90deg, #1e73be, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Structure Preview */
#structure-preview {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    /* Direction set dynamically by JS based on language choice */
}

#structure-preview.ltr {
    direction: ltr;
    text-align: left;
}

#structure-preview.rtl {
    direction: rtl;
    text-align: right;
}

.structure-section {
    margin-bottom: 15px;
    padding: 10px;
    background: #fff;
    border-left: 4px solid #1e73be;
    border-radius: 3px;
}

.structure-section h3 {
    margin: 0 0 10px 0;
    color: #1e73be;
}

.structure-section ul {
    margin: 5px 0;
}

/* RTL specific padding */
body.ai-seo-rtl .structure-section ul,
#structure-preview.rtl .structure-section ul {
    padding-right: 20px;
    padding-left: 0;
}

/* LTR specific padding */
body.ai-seo-ltr .structure-section ul,
#structure-preview.ltr .structure-section ul {
    padding-left: 20px;
    padding-right: 0;
}

/* Article Preview */
.ai-seo-article-generator-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 25px;
    max-height: 600px;
    overflow-y: auto;
    line-height: 1.8;
    /* Direction set dynamically based on content language */
}

/* RTL preview styles */
body.ai-seo-rtl .ai-seo-article-generator-preview,
.ai-seo-article-generator-preview.rtl {
    direction: rtl;
    text-align: right;
}

/* LTR preview styles */
body.ai-seo-ltr .ai-seo-article-generator-preview,
.ai-seo-article-generator-preview.ltr {
    direction: ltr;
    text-align: left;
}

.ai-seo-article-generator-preview h1,
.ai-seo-article-generator-preview h2,
.ai-seo-article-generator-preview h3,
.ai-seo-article-generator-preview h4,
.ai-seo-article-generator-preview h5,
.ai-seo-article-generator-preview h6 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
}

.ai-seo-article-generator-preview h1 {
    font-size: 2em;
    border-bottom: 2px solid #1e73be;
    padding-bottom: 10px;
}

.ai-seo-article-generator-preview h2 {
    font-size: 1.5em;
    color: #1e73be;
}

.ai-seo-article-generator-preview p {
    margin-bottom: 15px;
    text-align: justify;
}

.ai-seo-article-generator-preview ul,
.ai-seo-article-generator-preview ol {
    margin: 15px 0;
}

/* RTL list padding */
body.ai-seo-rtl .ai-seo-article-generator-preview ul,
body.ai-seo-rtl .ai-seo-article-generator-preview ol,
.ai-seo-article-generator-preview.rtl ul,
.ai-seo-article-generator-preview.rtl ol {
    padding-right: 25px;
    padding-left: 0;
}

/* LTR list padding */
body.ai-seo-ltr .ai-seo-article-generator-preview ul,
body.ai-seo-ltr .ai-seo-article-generator-preview ol,
.ai-seo-article-generator-preview.ltr ul,
.ai-seo-article-generator-preview.ltr ol {
    padding-left: 25px;
    padding-right: 0;
}

.ai-seo-article-generator-preview li {
    margin-bottom: 8px;
}

/* Actions */
.ai-seo-article-generator-actions {
    margin-top: 20px;
    text-align: center;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 5px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.ai-seo-article-generator-actions .button {
    margin: 0 5px;
}

/* Copy Article Button */
#copy-article {
    background: #28a745;
    border-color: #28a745;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

#copy-article:hover {
    background: #218838;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
}

#copy-article.copied {
    background: #17a2b8;
    border-color: #138496;
    animation: pulse-success 0.6s ease-in-out;
}

@keyframes pulse-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Modal */
.ai-seo-article-generator-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.ai-seo-article-generator-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 30px;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    /* Direction handled by body classes */
}

/* RTL modal styles */
body.ai-seo-rtl .ai-seo-article-generator-modal-content {
    direction: rtl;
    text-align: right;
}

/* LTR modal styles */
body.ai-seo-ltr .ai-seo-article-generator-modal-content {
    direction: ltr;
    text-align: left;
}

.ai-seo-article-generator-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

/* RTL close button */
body.ai-seo-rtl .ai-seo-article-generator-close {
    float: left;
}

/* LTR close button */
body.ai-seo-ltr .ai-seo-article-generator-close {
    float: right;
}

.ai-seo-article-generator-close:hover,
.ai-seo-article-generator-close:focus {
    color: #000;
}

.ai-seo-article-generator-modal-actions {
    margin-top: 20px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

/* Structure Editor */
#structure-editor .structure-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

#structure-editor .structure-item input {
    width: 100%;
    margin-bottom: 10px;
}

#structure-editor .structure-item .remove-item {
    position: absolute;
    top: 10px;
    color: #dc3232;
    cursor: pointer;
    font-size: 18px;
}

/* RTL remove button */
body.ai-seo-rtl #structure-editor .structure-item .remove-item {
    left: 10px;
    right: auto;
}

/* LTR remove button */
body.ai-seo-ltr #structure-editor .structure-item .remove-item {
    right: 10px;
    left: auto;
}

.add-structure-item {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 15px;
    background: #1e73be;
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    cursor: pointer;
}

.add-structure-item:hover {
    background: #155a8a;
    color: #fff;
}

/* Info Section */
.ai-seo-article-generator-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 5px;
    padding: 20px;
    margin-top: 20px;
}

.ai-seo-article-generator-info h2 {
    margin-top: 0;
    color: #1e73be;
}

.ai-seo-article-generator-info ol {
    margin: 0;
}

/* RTL list padding */
body.ai-seo-rtl .ai-seo-article-generator-info ol {
    padding-right: 20px;
    padding-left: 0;
}

/* LTR list padding */
body.ai-seo-ltr .ai-seo-article-generator-info ol {
    padding-left: 20px;
    padding-right: 0;
}

.ai-seo-article-generator-info li {
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-seo-article-generator-dashboard {
        grid-template-columns: 1fr;
    }
    
    .ai-seo-article-generator-modal-content {
        width: 95%;
        margin: 2% auto;
        padding: 20px;
    }
    
    .ai-seo-article-generator-step {
        padding: 20px;
    }
}

/* Loading State */
.ai-seo-article-generator-loading {
    opacity: 0.6;
    pointer-events: none;
}

.ai-seo-article-generator-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1e73be;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Enhancements */
.button-large {
    padding: 10px 20px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
}

.ai-seo-article-generator-card .button-large {
    display: inline-block;
    margin-top: 15px;
}

/* Table Styles handled by body classes */
body.ai-seo-rtl .ai-seo-article-generator-wrap .wp-list-table {
    direction: rtl;
}

body.ai-seo-rtl .ai-seo-article-generator-wrap .wp-list-table th,
body.ai-seo-rtl .ai-seo-article-generator-wrap .wp-list-table td {
    text-align: right;
}

body.ai-seo-ltr .ai-seo-article-generator-wrap .wp-list-table {
    direction: ltr;
}

body.ai-seo-ltr .ai-seo-article-generator-wrap .wp-list-table th,
body.ai-seo-ltr .ai-seo-article-generator-wrap .wp-list-table td {
    text-align: left;
}

/* Enhancement Buttons */
.section-enhance {
    position: relative;
    border: 2px dashed transparent;
    transition: border-color 0.3s ease;
}

.section-enhance:hover {
    border-color: #1e73be;
    cursor: pointer;
}

.section-enhance-controls {
    position: absolute;
    top: -35px;
    background: #1e73be;
    border-radius: 3px;
    padding: 5px;
    display: none;
}

/* RTL enhance controls */
body.ai-seo-rtl .section-enhance-controls {
    right: 0;
    left: auto;
}

/* LTR enhance controls */
body.ai-seo-ltr .section-enhance-controls {
    left: 0;
    right: auto;
}

.section-enhance:hover .section-enhance-controls {
    display: block;
}

.section-enhance-controls button {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 3px 8px;
    margin: 0 2px;
    border-radius: 2px;
    font-size: 12px;
}

.section-enhance-controls button:hover {
    background: rgba(255,255,255,0.2);
}

/* Keyword Analysis */
.keyword-analysis {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.keyword-analysis h4 {
    margin-top: 0;
    color: #1e73be;
}

.keyword-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.keyword-item:last-child {
    border-bottom: none;
}

.keyword-density {
    font-weight: bold;
}

.keyword-density.good { color: #46b450; }
.keyword-density.warning { color: #ffb900; }
.keyword-density.error { color: #dc3232; }

/* Table of Contents Styles */
.ai-seo-article-generator-toc {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    margin: 20px 0;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    /* Direction handled dynamically */
}

/* RTL TOC styles */
body.ai-seo-rtl .ai-seo-article-generator-toc,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc {
    direction: rtl;
    text-align: right;
}

/* LTR TOC styles */
body.ai-seo-ltr .ai-seo-article-generator-toc,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc {
    direction: ltr;
    text-align: left;
}

.ai-seo-article-generator-toc .toc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.ai-seo-article-generator-toc .toc-title {
    margin: 0;
    color: #1e73be;
    font-size: 1.3em;
    font-weight: bold;
}

.ai-seo-article-generator-toc .toc-toggle {
    background: #1e73be;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.ai-seo-article-generator-toc .toc-toggle:hover {
    background: #155a8a;
}

.ai-seo-article-generator-toc .toc-content {
    transition: all 0.3s ease;
}

.ai-seo-article-generator-toc.collapsed .toc-content {
    display: none;
}

.ai-seo-article-generator-toc .toc-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.ai-seo-article-generator-toc-numbered .toc-list {
    counter-reset: toc-counter;
}

.ai-seo-article-generator-toc .toc-list li {
    margin: 8px 0;
    line-height: 1.5;
}

.ai-seo-article-generator-toc .toc-link {
    color: #333;
    text-decoration: none;
    display: block;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.ai-seo-article-generator-toc .toc-link:hover {
    background: #1e73be;
    color: white;
    text-decoration: none;
}

.ai-seo-article-generator-toc .toc-number {
    font-weight: bold;
    color: #1e73be;
}

/* RTL TOC number spacing */
body.ai-seo-rtl .ai-seo-article-generator-toc .toc-number,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc .toc-number {
    margin-left: 8px;
    margin-right: 0;
}

/* LTR TOC number spacing */
body.ai-seo-ltr .ai-seo-article-generator-toc .toc-number,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc .toc-number {
    margin-right: 8px;
    margin-left: 0;
}

.ai-seo-article-generator-toc .toc-level-2 {
    font-weight: 600;
}

.ai-seo-article-generator-toc .toc-level-3 {
    font-size: 0.95em;
}

.ai-seo-article-generator-toc .toc-level-4 {
    font-size: 0.9em;
    color: #666;
}

.ai-seo-article-generator-toc .toc-level-5,
.ai-seo-article-generator-toc .toc-level-6 {
    font-size: 0.85em;
    color: #777;
}

/* RTL TOC indentation */
body.ai-seo-rtl .ai-seo-article-generator-toc .toc-level-3,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc .toc-level-3 {
    margin-right: 20px;
    margin-left: 0;
}

body.ai-seo-rtl .ai-seo-article-generator-toc .toc-level-4,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc .toc-level-4 {
    margin-right: 40px;
    margin-left: 0;
}

body.ai-seo-rtl .ai-seo-article-generator-toc .toc-level-5,
body.ai-seo-rtl .ai-seo-article-generator-toc .toc-level-6,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc .toc-level-5,
.ai-seo-article-generator-preview.rtl .ai-seo-article-generator-toc .toc-level-6 {
    margin-right: 60px;
    margin-left: 0;
}

/* LTR TOC indentation */
body.ai-seo-ltr .ai-seo-article-generator-toc .toc-level-3,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc .toc-level-3 {
    margin-left: 20px;
    margin-right: 0;
}

body.ai-seo-ltr .ai-seo-article-generator-toc .toc-level-4,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc .toc-level-4 {
    margin-left: 40px;
    margin-right: 0;
}

body.ai-seo-ltr .ai-seo-article-generator-toc .toc-level-5,
body.ai-seo-ltr .ai-seo-article-generator-toc .toc-level-6,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc .toc-level-5,
.ai-seo-article-generator-preview.ltr .ai-seo-article-generator-toc .toc-level-6 {
    margin-left: 60px;
    margin-right: 0;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* TOC in preview */
.ai-seo-article-generator-preview .ai-seo-article-generator-toc {
    border-color: #1e73be;
}

/* Content Analysis Dashboard */
.ai-seo-article-generator-content-analysis {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    /* Direction set dynamically by JS based on language */
}

.ai-seo-article-generator-content-analysis.ltr {
    direction: ltr;
    text-align: left;
}

.ai-seo-article-generator-content-analysis.rtl {
    direction: rtl;
    text-align: right;
}

.ai-seo-article-generator-content-analysis h3 {
    margin: 0 0 15px 0;
    color: #1e73be;
    font-size: 1.2em;
}

.content-analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.analysis-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: transform 0.2s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.analysis-card h4 {
    margin: 0 0 10px 0;
    font-size: 0.9em;
    color: #666;
    font-weight: 600;
}

.analysis-card .metric-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.analysis-card .metric-label {
    font-size: 0.8em;
    color: #888;
}

/* SEO Score Card */
.analysis-card.seo-score .score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 1.5em;
    font-weight: bold;
    color: white;
}

.analysis-card.seo-score.excellent .score-circle {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.analysis-card.seo-score.good .score-circle {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.analysis-card.seo-score.needs-improvement .score-circle {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.analysis-card.seo-score .score-label {
    font-size: 0.9em;
    font-weight: 600;
    color: #333;
}

/* Keyword Density Card */
.analysis-card.keyword-density.good {
    border-color: #28a745;
    background: linear-gradient(to bottom, #d4edda, #ffffff);
}

.analysis-card.keyword-density.warning {
    border-color: #ffc107;
    background: linear-gradient(to bottom, #fff3cd, #ffffff);
}

/* Headings Card */
.analysis-card.headings .headings-breakdown {
    font-size: 0.9em;
    color: #555;
    font-weight: 500;
}

/* SEO Issues */
.seo-issues {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.seo-issues h4 {
    margin: 0 0 10px 0;
    color: #856404;
}

.seo-issues ul {
    margin: 0;
}

/* RTL issues list */
body.ai-seo-rtl .seo-issues ul {
    padding-right: 20px;
    padding-left: 0;
}

/* LTR issues list */
body.ai-seo-ltr .seo-issues ul {
    padding-left: 20px;
    padding-right: 0;
}

.seo-issues li {
    margin-bottom: 5px;
    color: #856404;
}

/* Detected Features */
.detected-features {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
}

.detected-features h4 {
    margin: 0 0 10px 0;
    color: #1e73be;
}

.features-list {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.feature-badge {
    background: #1e73be;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.feature-badge.inactive {
    background: #6c757d;
}

/* Analysis Loading */
.analysis-loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* Enhanced generation feedback */
.generation-method-info {
    background: #e7f3ff;
    border: 1px solid #1e73be;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #1e73be;
    text-align: center;
    font-weight: 500;
}

.long-article-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #856404;
    text-align: center;
}

.word-count-progress {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.word-count-progress .progress-bar {
    background: #e9ecef;
    border-radius: 4px;
    height: 10px;
    margin: 10px 0;
    overflow: hidden;
}

.word-count-progress .progress-fill {
    background: #28a745;
    height: 100%;
    transition: width 0.3s ease;
}

.target-words-info {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 10px;
}

/* Enhanced loading states and feedback */
.ai-seo-article-generator-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    min-height: 200px;
}

.estimated-time {
    color: #666;
    font-style: italic;
    margin-top: 10px;
}

/* Section by section generation indicator */
.section-generation-progress {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.section-generation-progress h4 {
    color: #1e73be;
    margin-bottom: 15px;
}

.section-progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.section-progress-item:last-child {
    border-bottom: none;
}

.section-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.section-status.pending {
    background: #f8f9fa;
    color: #6c757d;
}

.section-status.in-progress {
    background: #fff3cd;
    color: #856404;
}

.section-status.completed {
    background: #d4edda;
    color: #155724;
}

/* === LINKS MANAGER STYLES === */

/* Links button with selection state */
#manage-links.has-selection {
    background: #0073aa;
    color: white;
    font-weight: bold;
}

/* Links modal specific styles */
#links-manager-modal .ai-seo-article-generator-modal-content {
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
}

.links-manager-content {
    padding: 0;
}

.links-instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.links-instructions p {
    margin: 0;
    color: #0073aa;
}

/* Add link section */
#add-link-section {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 25px;
}

#add-link-section h3 {
    margin-top: 0;
    color: #1e3a8a;
    font-size: 1.1em;
}

.selected-text-preview {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
    /* Direction handled by body classes */
}

/* RTL selected text preview */
body.ai-seo-rtl .selected-text-preview {
    direction: rtl;
}

/* LTR selected text preview */
body.ai-seo-ltr .selected-text-preview {
    direction: ltr;
}

.selected-text-preview strong {
    color: #856404;
}

#google-search-btn {
    background: #4285f4 !important;
    color: white !important;
    border: none !important;
    font-size: 13px !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    transition: background-color 0.3s ease !important;
}

#google-search-btn:hover {
    background: #3367d6 !important;
    color: white !important;
}

#selected-text-display {
    background: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    border: 1px solid #ddd;
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Existing links section */
#existing-links-section h3 {
    border-bottom: 2px solid #0073aa;
    padding-bottom: 8px;
    margin-bottom: 15px;
    color: #0073aa;
}

.no-links {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 30px;
}

/* Links grid */
.links-grid {
    display: grid;
    gap: 15px;
}

.link-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    transition: box-shadow 0.2s ease;
}

.link-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.link-text {
    font-size: 14px;
    margin-bottom: 8px;
    /* Direction handled by body classes */
}

/* RTL link text */
body.ai-seo-rtl .link-text {
    direction: rtl;
}

/* LTR link text */
body.ai-seo-ltr .link-text {
    direction: ltr;
}

.link-text strong {
    color: #1e3a8a;
    background: #f0f8ff;
    padding: 2px 6px;
    border-radius: 3px;
}

.link-url {
    font-size: 13px;
    margin-bottom: 5px;
    direction: ltr;
    text-align: left;
}

.link-url a {
    color: #0073aa;
    text-decoration: none;
    word-break: break-all;
}

.link-url a:hover {
    text-decoration: underline;
}

.link-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
    /* Direction handled by body classes */
}

/* RTL link title */
body.ai-seo-rtl .link-title {
    direction: rtl;
}

/* LTR link title */
body.ai-seo-ltr .link-title {
    direction: ltr;
}

.link-actions {
    text-align: left;
    direction: ltr;
}

.remove-link {
    background: #dc3545 !important;
    color: white !important;
    border: none !important;
    padding: 4px 10px !important;
    font-size: 12px !important;
    border-radius: 3px !important;
}

.remove-link:hover {
    background: #c82333 !important;
}

/* Article preview link styling */
#article-preview a[data-link-id] {
    color: #0073aa;
    text-decoration: underline;
    background: rgba(0, 115, 170, 0.1);
    padding: 1px 2px;
    border-radius: 2px;
    transition: background 0.2s ease;
}

#article-preview a[data-link-id]:hover {
    background: rgba(0, 115, 170, 0.2);
    text-decoration: none;
}

/* Outbound links analysis card */
.analysis-card.outbound-links {
    border-left: 4px solid #0073aa;
}

.analysis-card.outbound-links.good {
    border-left-color: #46b450;
    background: #f7fcf0;
}

.analysis-card.outbound-links.warning {
    border-left-color: #ffb900;
    background: #fffbf0;
}

.analysis-card.outbound-links.needs-improvement {
    border-left-color: #dc3232;
    background: #fef7f1;
}

.links-preview {
    margin-top: 10px;
    font-size: 12px;
}

.link-preview-item {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 3px;
    padding: 4px 8px;
    margin: 2px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 150px;
    /* Direction handled by body classes */
}

/* RTL link preview item */
body.ai-seo-rtl .link-preview-item {
    direction: rtl;
}

/* LTR link preview item */
body.ai-seo-ltr .link-preview-item {
    direction: ltr;
}

.link-preview-more {
    color: #666;
    font-style: italic;
    margin-top: 5px;
    /* Direction handled by body classes */
}

/* RTL link preview more */
body.ai-seo-rtl .link-preview-more {
    direction: rtl;
}

/* LTR link preview more */
body.ai-seo-ltr .link-preview-more {
    direction: ltr;
}

/* Selection highlighting in article preview */
#article-preview {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

#article-preview::selection {
    background: #b3d9ff;
    color: #0073aa;
}

#article-preview ::-moz-selection {
    background: #b3d9ff;
    color: #0073aa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content-analysis-grid {
        grid-template-columns: 1fr;
    }
    
    .features-list {
        justify-content: center;
    }
    
    .section-progress-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    /* Links modal mobile adjustments */
    #links-manager-modal .ai-seo-article-generator-modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
    }
    
    .links-grid {
        grid-template-columns: 1fr;
    }
    
    .selected-text-preview {
        font-size: 14px;
    }
    
    #selected-text-display {
        max-width: 200px;
    }
}