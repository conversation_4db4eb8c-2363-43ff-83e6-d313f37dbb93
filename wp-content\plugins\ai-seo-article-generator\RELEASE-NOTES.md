# AI SEO Article Generator - Release Notes

## Version 1.0.3 - WordPress.org Compliance Release

**Release Date:** July 2025

### Summary

This release focuses on complete WordPress.org compliance, resolving all Plugin Check issues, and preparing the plugin for official distribution.

### What's New in 1.0.3

#### Compliance Updates
- **Plugin Check Fixes**
  - Fixed all InterpolatedNotPrepared warnings
  - Added phpcs:ignore comments for acceptable database queries
  - Resolved translators comment placement issues
  - Fixed escaping and sanitization warnings
  - Addressed all critical errors from Plugin Check tool

- **Code Quality Improvements**
  - Complete WordPress Coding Standards (PHPCS) compliance
  - Proper internationalization with translator comments
  - Conditional debug logging (no production debug output)
  - Improved error handling and user feedback

- **Documentation Overhaul**
  - Added comprehensive README.md for GitHub
  - Created DEVELOPER.md with technical documentation
  - Added WORDPRESS-COMPLIANCE.md compliance checklist
  - Reorganized documentation structure
  - Updated readme.txt with complete feature list

- **File Structure Cleanup**
  - Organized documentation in /docs/ directory
  - Removed development and debug files
  - Added proper .gitignore for clean distribution
  - Cleaned up root directory structure

---

## Version 1.0.2 - Major Feature Release

**Release Date:** July 2025

### Summary

Major feature update introducing OpenAI support, background processing, and the saved structures library.

### What's New in 1.0.2

#### Major Features
- **OpenAI Integration**
  - Added support for GPT-4, GPT-4 Turbo, and GPT-3.5 Turbo
  - AI provider selection in settings
  - Seamless switching between Claude and OpenAI

- **Background Processing**
  - Asynchronous generation for long articles (2000+ words)
  - Email notifications on completion
  - Retry mechanism for failed jobs
  - Progress tracking in admin dashboard

- **Saved Structures Library**
  - Save article structures as reusable templates
  - Manage saved structures with usage tracking
  - Load structures for consistent content creation
  - Import/export capabilities

#### Technical Improvements
- Fixed API key validation bug
- Improved table of contents generation
- Added readability analysis (Flesch score)
- Enhanced security with better nonce handling
- Fixed MySQL strict mode compatibility
- Added comprehensive word count tracking
- Better handling of Hebrew/English content detection

#### Bug Fixes
- Resolved timeout issues for large articles
- Fixed content truncation problems
- Improved error messages and user feedback
- Better handling of API response errors

---

## Version 1.0.1 - Language Support Update

**Release Date:** June 2025

### Summary

Added English language support and initial WordPress compliance improvements.

### What's New in 1.0.1

- **Multi-Language Support**
  - Added full English content generation
  - Maintained Hebrew (RTL) support
  - Automatic language detection

- **Plugin Improvements**
  - Renamed to "AI SEO Article Generator"
  - Initial WordPress.org compliance fixes
  - Basic timeout handling improvements
  - Initial security enhancements

---

## Version 1.0.0 - Initial Release

**Release Date:** May 2025

### Summary

Initial release of AI SEO Article Generator (formerly Postinor).

### Features

- **Core Functionality**
  - Claude API integration
  - Hebrew content generation with RTL support
  - SEO optimization features
  - Article structure templates
  - Draft management system

- **Content Features**
  - Structured article generation
  - Keyword integration
  - Meta description generation
  - Table of contents
  - Heading hierarchy

- **User Interface**
  - WordPress admin integration
  - AJAX-powered interface
  - Real-time generation
  - Draft preview

---

## Version History Summary

| Version | Release Date | Type | Key Changes |
|---------|--------------|------|-------------|
| 1.0.3 | July 2025 | Compliance | WordPress.org compliance, documentation |
| 1.0.2 | July 2025 | Features | OpenAI support, background processing |
| 1.0.1 | June 2025 | Enhancement | English support, plugin rename |
| 1.0.0 | May 2025 | Initial | First release with Claude API |

---

## Upgrade Notes

### From 1.0.2 to 1.0.3
- No database changes required
- No settings migration needed
- Simply update the plugin files

### From 1.0.1 to 1.0.2
- Database tables will be automatically updated
- New settings for AI provider selection
- Existing API keys remain compatible

### From 1.0.0 to 1.0.1
- Plugin will be renamed in admin menu
- All settings and data preserved
- No manual intervention required

---

## Contributors

- **Yotam Rozin (ytrofr)** - Lead Developer
- **Sigma** - Co-Developer

---

*For detailed technical changes, see the [DEVELOPER.md](DEVELOPER.md) file.*