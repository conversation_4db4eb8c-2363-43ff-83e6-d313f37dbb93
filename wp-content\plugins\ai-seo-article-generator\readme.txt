=== AI SEO Article Generator ===
Contributors: ytrofr
Donate link: https://advertiser.co.il
Tags: seo, content-generator, claude, chatgpt, ai-writing
Requires at least: 5.0
Tested up to: 6.8
Stable tag: 1.1.1
Requires PHP: 7.2
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Generate SEO-optimized articles using Claude 4 or OpenAI AI. Features feedback system, structured content creation and full Hebrew/English support.

== Description ==

Transform your WordPress content creation with AI SEO Article Generator - the ultimate AI-powered content generation plugin. Create professional, SEO-optimized articles instantly using Claude 4 or GPT-4. Perfect for bloggers, marketers, and content creators who need high-quality, search-engine-friendly content that ranks on Google.

Generate complete articles with proper SEO structure, meta descriptions, keyword optimization, and schema markup. Features dual-language support (Hebrew RTL + English), making it the only WordPress AI content plugin built for international markets. Save hours of writing time while producing content that actually converts and ranks.

= Why Choose AI SEO Article Generator? =

🤖 **Advanced AI Content Creation**: Powered by Claude 4 & GPT-4 - the most advanced AI models for content generation
📈 **Complete SEO Optimization**: Auto-generates meta descriptions, optimizes keyword density, adds schema markup for Google ranking
⚡ **Bulk Content Generation**: Create up to 10,000-word articles with background processing - no timeouts or limitations  
🌍 **Multi-Language Content**: Only WordPress AI plugin with native Hebrew (RTL) + English support for global markets
📚 **Smart Article Templates**: Save and reuse winning article structures that convert and rank
🎯 **Professional Content Structure**: AI creates proper H2/H3 hierarchy, table of contents, and readability optimization
📊 **Content Analytics**: Built-in Flesch reading score and SEO analysis for content that ranks
🔒 **Enterprise Security**: Bank-level security with WordPress coding standards compliance
📞 **Premium Support**: Direct WhatsApp support and comprehensive feedback system
⚙️ **Easy Setup**: Works with your existing WordPress theme - no coding required

= Perfect For Content Creators Who Need =

✍️ **Blog Content**: Generate SEO blog posts that rank on Google and drive organic traffic
🛍️ **E-commerce Content**: Create product descriptions and category pages that convert visitors to buyers  
🏢 **Business Content**: Professional service pages, about pages, and landing pages that build trust
📚 **Educational Content**: How-to guides, tutorials, and knowledge base articles with proper structure
📰 **News & Updates**: Fresh content for news sites, company updates, and industry insights
🎯 **Marketing Content**: Landing pages, sales pages, and lead magnets optimized for conversions
🌐 **Multi-Language Sites**: Hebrew and English content for international WordPress websites
📈 **SEO Content**: Articles specifically designed to rank higher in search engine results

= Requirements =

* WordPress 5.0 or higher
* PHP 7.2 or higher
* MySQL 5.6 or higher
* Claude API key (Anthropic) OR OpenAI API key

== Installation ==

1. Upload the `ai-seo-article-generator` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to the AI SEO Article Generator settings page
4. Enter your Claude API key
5. Start creating content!

= Configuration =

After activation:
1. Go to **AI SEO Generator > Settings** in your WordPress admin
2. Choose your AI provider (Claude or OpenAI)
3. Enter your API key
4. Configure your preferences (language, debug mode, etc.)
5. Create database tables if prompted
6. You're ready to generate content

== Frequently Asked Questions ==

= Which AI providers are supported? =

The plugin supports:
- Claude 4 by Anthropic (recommended)
- OpenAI GPT-4 and GPT-3.5 Turbo

Both provide excellent content generation capabilities.

= Do I need an API key? =

Yes, you need either:
- Claude API key from [Anthropic Console](https://console.anthropic.com/)
- OpenAI API key from [OpenAI Platform](https://platform.openai.com/)

Choose based on your preference and pricing needs.

= What languages are supported? =

The plugin fully supports:
- Hebrew content with RTL layout and Hebrew-specific SEO
- English content with standard LTR layout
- Automatic language detection based on content

= Can I customize the generated content? =

Yes! You can edit all generated content before publishing, create custom structures, and define specific keywords and topics.

= Is the content unique? =

Yes, all content is generated uniquely based on your inputs and requirements. Each generation creates original content.

= How does the SEO optimization work? =

The plugin integrates keywords naturally, creates proper heading structures, generates meta descriptions, and follows SEO best practices.

== Screenshots ==

1. Main dashboard showing content generation interface
2. Article structure builder
3. Generated content preview with SEO metrics
4. Settings page with API configuration
5. Saved drafts management
6. Custom structure templates

== Changelog ==

= 1.1.1 =
* Fixed Hebrew/RTL display issues for English content
* Fixed hardcoded Hebrew text in Table of Contents
* Improved language detection for content generation
* Updated TOC generation to be language-aware
* Made RTL styles conditional based on content language
* Enhanced multilingual support throughout the plugin
* Improved JavaScript translation handling
* Fixed CSS direction properties for better LTR/RTL support
* Updated documentation with multilingual best practices
* Cleaned up test files

= 1.0.4 =
* Added complete Feedback & Support system
* Contact form with categorized feedback types (Bug, Feature, Support, Review)
* Direct WhatsApp integration for instant support (972546330446)
* Floating WhatsApp button on all plugin pages
* Email notifications to admin for all feedback
* Quick action buttons for common tasks
* Full Hebrew language support for feedback system
* Updated AI model references from Claude 3.5 to Claude 4
* Fixed JavaScript errors in feedback system
* Replaced hardcoded Hebrew progress messages with English
* Mobile-responsive feedback interface
* Enhanced user experience with better support options

= 1.0.3 =
* Complete WordPress.org compliance update
* Fixed all Plugin Check errors and warnings
* Added comprehensive documentation (README.md, DEVELOPER.md)
* Reorganized file structure for production
* Improved debug logging system (conditional only)
* Enhanced security with proper escaping and sanitization
* Updated plugin author display to Sigma
* Fixed translators comments placement
* Added phpcs:ignore comments for acceptable warnings
* Cleaned up codebase for submission
* Updated all documentation and version numbers

= 1.0.2 =
* Added OpenAI integration (GPT-4 and GPT-3.5 Turbo support)
* Implemented background processing for long articles
* Added saved structures library feature
* Fixed API key validation bug
* Improved table of contents generation
* Added readability analysis
* Enhanced security with better nonce handling
* Fixed MySQL strict mode compatibility
* Added word count tracking
* Better handling of Hebrew/English content detection

= 1.0.1 =
* Plugin renamed to AI SEO Article Generator
* Added English language support
* Initial timeout fixes
* Basic WordPress standards compliance

= 1.0.0 =
* Initial release
* Claude API integration
* Hebrew content generation
* SEO optimization features
* Article structure templates
* Draft management system

= 1.0.0 =
* Initial release
* Claude Sonnet 4 integration
* Hebrew content generation
* SEO, AEO, and GEO optimization
* Article structure templates
* Draft management system
* Table of contents generation
* RTL support for Hebrew content

== Upgrade Notice ==

= 1.0.3 =
Production-ready release with complete WordPress.org compliance. All Plugin Check issues resolved. Required update before WordPress.org submission.

= 1.0.2 =
Major feature update adding OpenAI support, background processing, and saved structures library. Recommended for all users.

= 1.0.1 =
Important update adding English language support and initial compliance fixes.

= 1.0.0 =
First release of AI SEO Article Generator plugin. Requires Claude API key for operation.

== Additional Information ==

= Support =

For support, feature requests, or bug reports, please use the WordPress.org support forum.

= Database Tables =

The plugin creates three custom tables:
- `{prefix}_ai_seo_article_generator_drafts` - Article drafts and content
- `{prefix}_ai_seo_article_generator_structures` - Article structures  
- `{prefix}_ai_seo_article_generator_saved_structures` - Saved templates

= Privacy =

This plugin sends data to third-party AI services for content generation:
- Claude API (Anthropic) - [Privacy Policy](https://www.anthropic.com/privacy)
- OpenAI API - [Privacy Policy](https://openai.com/privacy/)

No personal data is collected by the plugin itself.

== Credits ==

* Developed by Yotam Rozin (ytrofr) and Sigma
* Powered by Claude 4 (Anthropic) and GPT-4 (OpenAI)
* Icons from WordPress Dashicons
* RTL support for Hebrew content