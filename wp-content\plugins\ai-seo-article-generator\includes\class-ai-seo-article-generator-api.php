<?php

if (!defined('ABSPATH')) {
    exit;
}

class AI_SEO_Article_Generator_API {
    
    private $claude_api_key;
    private $openai_api_key;
    private $ai_provider;
    private $claude_api_url = 'https://api.anthropic.com/v1/messages';
    private $openai_api_url = 'https://api.openai.com/v1/chat/completions';
    
    public function __construct() {
        $this->claude_api_key = get_option('ai_seo_article_generator_api_key', '');
        $this->openai_api_key = get_option('ai_seo_article_generator_openai_api_key', '');
        $this->ai_provider = get_option('ai_seo_article_generator_ai_provider', 'claude');
    }
    
    /**
     * Determine content language based on user selection
     */
    public function determine_content_language($article_language) {
        switch ($article_language) {
            case 'hebrew':
                return true;
            case 'english':
                return false;
            case 'auto':
            default:
                // Use WordPress locale detection
                $locale = get_locale();
                $hebrew_locales = array('he_IL', 'he', 'hebrew', 'Hebrew', 'HE_IL', 'HE');
                return in_array($locale, $hebrew_locales) || strpos($locale, 'he') === 0;
        }
    }
    
    /**
     * Get English length category for article type
     */
    private function get_length_category_english($target_words) {
        if ($target_words >= 3000) return 'very comprehensive';
        if ($target_words >= 2500) return 'comprehensive';
        if ($target_words >= 2000) return 'detailed';
        if ($target_words >= 1500) return 'long';
        if ($target_words >= 1000) return 'medium';
        return 'short';
    }
    
    /**
     * Debug logging helper function
     */
    private function debug_log($message, $data = null) {
        if (get_option('ai_seo_article_generator_debug_logging', 0)) {
            $log_message = 'AI SEO Article Generator API: ' . $message;
            if ($data !== null && is_array($data)) {
                $log_message .= ' - ' . wp_json_encode($data);
            } elseif ($data !== null) {
                $log_message .= ' - ' . $data;
            }
            if (function_exists('error_log')) {
                // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log -- Conditional debug logging
                error_log($log_message);
            }
        }
    }
    
    public function test_connection() {
        if ($this->ai_provider === 'openai') {
            $response = $this->make_openai_request('Test connection', 'Please respond with "Connection successful"', 50, 30);
        } else {
            $response = $this->make_claude_request('Test connection', 'Please respond with "Connection successful"', 50, 30);
        }
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        if (strpos(strtolower($response), 'connection successful') !== false) {
            update_option('ai_seo_article_generator_api_connected', true);
            $provider_name = ($this->ai_provider === 'openai') ? 'OpenAI' : 'Claude';
            return array('success' => true, 'message' => "Connection successful with {$provider_name}!");
        }
        
        return array('success' => false, 'message' => 'Unexpected response from API');
    }
    
    public function generate_article_structure($main_keyword, $sub_keywords, $target_words, $article_language = 'auto') {
        $this->debug_log("🎯 AI SEO API: Starting structure generation for keyword: $main_keyword (language: $article_language)");
        
        $prompt = $this->build_structure_prompt($main_keyword, $sub_keywords, $target_words, $article_language);
        $this->debug_log("📝 AI SEO API: Built prompt, length: " . strlen($prompt));
        
        // Check the appropriate API key based on provider
        $api_key = ($this->ai_provider === 'openai') ? $this->openai_api_key : $this->claude_api_key;
        if (empty($api_key)) {
            $this->debug_log("❌ AI SEO API: API key is empty for provider: " . $this->ai_provider);
            return array('success' => false, 'message' => 'API key לא הוגדר');
        }
        
        $this->debug_log("🔑 AI SEO API: API key is set, making request...");
        
        // Calculate dynamic timeout matching frontend calculation
        // Frontend: 30s + 30s per 1000 words (60s-180s range) 
        $structure_timeout = max(60, min(180, 30 + ($target_words / 1000) * 30));
        $this->debug_log("⏰ AI SEO API: Structure timeout calculated: {$structure_timeout}s for {$target_words} words (matching frontend)");
        
        $response = $this->make_request('Generate Article Structure', $prompt, 2500, $structure_timeout);
        
        if (is_wp_error($response)) {
            $this->debug_log("❌ AI SEO API: Request failed with error: " . $response->get_error_message());
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $this->debug_log("✅ AI SEO API: Got response, length: " . strlen($response));
        $this->debug_log("📄 AI SEO API: Response preview: " . substr($response, 0, 200) . "...");
        
        // Check if response appears truncated
        if ($this->is_response_truncated($response)) {
            $this->debug_log("⚠️ AI SEO API: Response appears truncated, retrying with higher max_tokens...");
            // Use extended timeout for retry (add 50% more time)
            $retry_timeout = min(270, ceil($structure_timeout * 1.5)); // Cap at 4.5 minutes
            $this->debug_log("⏰ AI SEO API: Structure retry timeout: {$retry_timeout}s");
            $retry_response = $this->make_request('Generate Article Structure - Retry', $prompt, 4000, $retry_timeout);
            
            if (!is_wp_error($retry_response) && !$this->is_response_truncated($retry_response)) {
                $this->debug_log("✅ AI SEO API: Retry successful, using retry response");
                $response = $retry_response;
            } else {
                $this->debug_log("⚠️ AI SEO API: Retry also truncated or failed, proceeding with original response");
            }
        }
        
        $structure = $this->parse_structure_response($response);
        $this->debug_log("🏗️ AI SEO API: Parsed structure", $structure);
        
        return array('success' => true, 'structure' => $structure);
    }
    
    public function generate_article_content($structure, $main_keyword, $sub_keywords, $target_words, $article_language = 'auto') {
        $this->debug_log("🚀 AI SEO API: Starting content generation for {$target_words} words (language: $article_language)");
        
        $prompt = $this->build_content_prompt($structure, $main_keyword, $sub_keywords, $target_words, $article_language);
        $this->debug_log("📝 AI SEO API: Content prompt built, length: " . strlen($prompt));
        
        // Calculate max_tokens based on target words (approximately 7-8 tokens per Hebrew word)
        // Claude-3.5-Sonnet has a max of 8192 tokens output, but we'll use dynamic scaling
        $tokens_per_word = 7; // More accurate for Hebrew content
        $max_tokens = min(8100, max(4000, $target_words * $tokens_per_word));
        
        // For very long articles, we'll use section-by-section generation
        if ($target_words > 2500) {
            $this->debug_log("🔄 AI SEO API: Long article detected ({$target_words} words), implementing enhanced generation strategy");
            return $this->generate_long_article_content($structure, $main_keyword, $sub_keywords, $target_words);
        }
        
        // Calculate timeout based on target words (more words = longer timeout)
        // Use admin-configurable maximum timeout for Hebrew content generation
        $max_content_timeout = get_option('ai_seo_article_generator_max_content_timeout', 600);
        $base_content_timeout = min(180, $max_content_timeout * 0.3); // 30% of max as base
        $content_timeout = max($base_content_timeout, min($max_content_timeout, $target_words / 4)); // Dynamic based on word count
        
        $this->debug_log("⚙️ AI SEO API: Using max_tokens: {$max_tokens}, timeout: {$content_timeout}s (max: {$max_content_timeout}s)");
        
        $response = $this->make_request('Generate Article Content', $prompt, $max_tokens, $content_timeout);
        
        if (is_wp_error($response)) {
            $this->debug_log("❌ AI SEO API: Content generation failed: " . $response->get_error_message());
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $this->debug_log("✅ AI SEO API: Content generation completed, response length: " . strlen($response));
        
        // Validate content structure and quality
        $structure_validation = $this->validate_content_structure($response, $main_keyword, $target_words);
        
        // If structure validation fails, try to fix the content
        if (!$structure_validation['valid']) {
            $this->debug_log("⚠️ AI SEO API: Content structure validation failed: " . implode(', ', $structure_validation['issues']));
            
            // Try to enhance the content with missing elements
            $enhanced_content = $this->enhance_content_structure($response, $main_keyword, $structure_validation['issues']);
            if ($enhanced_content) {
                $response = $enhanced_content;
                $this->debug_log("🔧 AI SEO API: Enhanced content structure automatically");
                
                // Re-validate after enhancement
                $structure_validation = $this->validate_content_structure($response, $main_keyword, $target_words);
            }
        }
        
        // Validate content length with improved logic
        $validation_result = $this->validate_content_length($response, $target_words);
        
        // Apply keyword density optimization if needed
        if ($structure_validation['valid'] && isset($structure_validation['stats']['keyword_density'])) {
            $keyword_density = $structure_validation['stats']['keyword_density'];
            if ($keyword_density < 1.0) {
                $this->debug_log("⚠️ AI SEO API: Low keyword density ({$keyword_density}%), attempting optimization");
                $optimized_content = $this->optimize_keyword_density($response, $main_keyword, $target_words);
                if ($optimized_content) {
                    $response = $optimized_content;
                    $this->debug_log("🔧 AI SEO API: Applied keyword density optimization");
                }
            }
        }
        
        // Much stricter acceptance criteria - require at least 90% of target words
        $min_acceptable = $target_words * 0.9; // Require 90% of target
        $should_retry = false;
        
        if ($validation_result['word_count'] >= $min_acceptable) {
            $this->debug_log("✅ AI SEO API: Content accepted - {$validation_result['word_count']} words (target: {$target_words})");
            
            // Even more aggressive retry logic - always retry if not 95% of target
            $should_retry = $validation_result['word_count'] < ($target_words * 0.95);
            
            if ($should_retry && $target_words <= 3500) {
                $this->debug_log("⚠️ AI SEO API: Content short ({$validation_result['word_count']} words), attempting retry...");
                
                $percentage_achieved = round(($validation_result['word_count'] / $target_words) * 100);
                $words_needed = $target_words - $validation_result['word_count'];
                
                $enhanced_prompt = $this->build_content_prompt($structure, $main_keyword, $sub_keywords, $target_words) . 
                    "\n\n🚨 ניסיון שני - המאמר הקודם קצר מדי!\n" .
                    "📊 הישג נוכחי: {$validation_result['word_count']} מילים ({$percentage_achieved}% מהיעד)\n" .
                    "🎯 נדרש להוסיף: {$words_needed} מילים נוספות\n" .
                    "📝 יעד סופי: {$target_words} מילים מדויקות\n\n" .
                    "🔥 הוראות מיוחדות למילוי החסר:\n" .
                    "- הוסף פסקאות נוספות לכל פרק (לפחות 6-8 פסקאות לפרק)\n" .
                    "- כלול דוגמאות מעשיות מפורטות ומקרי מבחן\n" .
                    "- הוסף רשימות של 8-12 נקודות עם הסברים\n" .
                    "- כלול שאלות ותשובות נפוצות בכל פרק\n" .
                    "- הרחב עם פרטים טכניים, נתונים וסטטיסטיקות\n" .
                    "- הוסף טיפים מתקדמים וטכניקות מומחה\n" .
                    "- כתוב במלוא ההרחבה - אין גבול עליון!\n" .
                    "- אל תעצור עד שתגיע בדיוק ל-{$target_words} מילים!";
                
                $retry_max_tokens = min(8000, $target_words * 5);
                $retry_response = $this->make_request('Generate Article Content - Retry', $enhanced_prompt, $retry_max_tokens, $content_timeout + 60);
                
                if (!is_wp_error($retry_response)) {
                    $retry_validation = $this->validate_content_length($retry_response, $target_words);
                    
                    // Use retry if it's significantly better, otherwise stick with original
                    if ($retry_validation['word_count'] > $validation_result['word_count'] * 1.3) {
                        $this->debug_log("✅ AI SEO API: Retry successful - using {$retry_validation['word_count']} words instead of {$validation_result['word_count']}");
                        return array('success' => true, 'content' => $retry_response, 'word_count' => $retry_validation['word_count']);
                    } else {
                        $this->debug_log("⚠️ AI SEO API: Retry didn't improve significantly, using original content");
                    }
                } else {
                    $this->debug_log("⚠️ AI SEO API: Retry failed, using original content");
                }
            }
            
            // Return the original content (it's good enough)
            return array('success' => true, 'content' => $response, 'word_count' => $validation_result['word_count']);
        }
        
        // For articles that are still too short, try content extension (now more aggressive)
        if ($validation_result['word_count'] < $target_words * 0.95) {
            $this->debug_log("⚠️ AI SEO API: Article significantly short, attempting content extension");
            $extended_content = $this->extend_article_content($response, $main_keyword, $target_words, $validation_result['word_count']);
            if ($extended_content) {
                $extended_validation = $this->validate_content_length($extended_content, $target_words);
                if ($extended_validation['word_count'] > $validation_result['word_count']) {
                    $this->debug_log("✅ AI SEO API: Content extended successfully to {$extended_validation['word_count']} words");
                    return array('success' => true, 'content' => $extended_content, 'word_count' => $extended_validation['word_count']);
                }
            }
        }
        
        return array('success' => true, 'content' => $response, 'word_count' => $validation_result['word_count']);
    }
    
    public function enhance_section($section_content, $main_keyword, $enhancement_type = 'improve') {
        $prompt = $this->build_enhancement_prompt($section_content, $main_keyword, $enhancement_type);
        
        $response = $this->make_request('Enhance Section', $prompt, 1000, 45);
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        return array('success' => true, 'content' => $response);
    }
    
    private function make_claude_request($purpose, $prompt, $max_tokens = 1000, $timeout = 60) {
        $this->debug_log("🌐 AI SEO API: Making request for: $purpose");
        $this->debug_log("📏 AI SEO API: Max tokens: $max_tokens");
        $this->debug_log("⏰ AI SEO API: Timeout: {$timeout}s");
        
        // Set appropriate execution time limit for long-running operations
        if ($timeout > 300) {
            // For very long operations (5+ minutes), remove execution time limit
            if (function_exists('set_time_limit') && !ini_get('safe_mode')) {
                // phpcs:ignore Squiz.PHP.DiscouragedFunctions.Discouraged -- Required for long API operations
                @set_time_limit(0);
            }
            $this->debug_log("⏰ AI SEO API: Removed execution time limit for long operation ({$timeout}s timeout)");
        } else {
            // Set execution time limit to 50% more than request timeout to allow for processing
            $execution_limit = ceil($timeout * 1.5);
            if (function_exists('set_time_limit') && !ini_get('safe_mode')) {
                // phpcs:ignore Squiz.PHP.DiscouragedFunctions.Discouraged -- Required for API timeout control
                @set_time_limit($execution_limit);
            }
            $this->debug_log("⏰ AI SEO API: Set execution time limit to {$execution_limit}s (request timeout: {$timeout}s)");
        }
        
        // Log start time for debugging timeout issues
        $start_time = microtime(true);
        $this->debug_log("🚀 AI SEO API: Starting request at " . gmdate('Y-m-d H:i:s') . " UTC with {$timeout}s timeout");
        
        if (empty($this->claude_api_key)) {
            $this->debug_log("❌ AI SEO API: Claude API key is empty in make_claude_request");
            return new WP_Error('no_api_key', 'API key not configured');
        }
        
        $this->debug_log("🔑 AI SEO API: Claude API key length: " . strlen($this->claude_api_key));
        
        $headers = array(
            'Content-Type' => 'application/json',
            'x-api-key' => $this->claude_api_key,
            'anthropic-version' => '2023-06-01'
        );
        
        $this->debug_log("📋 AI SEO API: Headers prepared");
        
        // Determine system prompt language based on content
        $is_hebrew_content = strpos($prompt, 'בעברית') !== false || strpos($prompt, 'מאמר') !== false;
        $system_prompt = $is_hebrew_content 
            ? 'אתה כותב תוכן מקצועי בעברית המתמחה ב-SEO, AEO ו-GEO. אתה מייצר תוכן איכותי, מפורט ומקיף בעברית טבעית.'
            : 'You are a professional content writer specializing in SEO, AEO, and GEO. You produce high-quality, detailed, and comprehensive content in natural English.';
        
        $body = array(
            'model' => 'claude-sonnet-4-20250514',
            'max_tokens' => $max_tokens,
            'temperature' => 0.7,
            'system' => $system_prompt,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            )
        );
        
        $this->debug_log("📦 AI SEO API: Request body prepared, JSON size: " . strlen(json_encode($body)));
        $this->debug_log("🎯 AI SEO API: Request URL: " . $this->claude_api_url);
        $this->debug_log("⏰ AI SEO API: Request timeout: {$timeout}s");
        
        $response = wp_remote_post($this->claude_api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => $timeout,
        ));
        
        // Log completion time for debugging
        $end_time = microtime(true);
        $actual_duration = round($end_time - $start_time, 2);
        $this->debug_log("🏁 AI SEO API: Request completed in {$actual_duration}s (timeout was {$timeout}s)");
        
        $this->debug_log("📡 AI SEO API: Request sent, checking response...");
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->debug_log("❌ AI SEO API: WP_Error in response: " . $error_message);
            
            // Handle timeout specifically with improved messaging
            if (strpos($error_message, 'timed out') !== false || strpos($error_message, 'timeout') !== false) {
                $this->debug_log("⏰ AI SEO API: Request timed out after {$timeout}s");
                
                // Provide specific timeout messages based on operation type
                if ($purpose === 'Generate Article Content' || $purpose === 'Generate Article Content - Retry') {
                    $timeout_minutes = ceil($timeout / 60);
                    return new WP_Error('content_timeout', 
                        "יצירת המאמר לוקחת זמן רב מהצפוי ({$timeout_minutes} דקות). זה יכול לקרות עם מאמרים ארוכים או מורכבים. אנא נסה שוב או הקטן את אורך המאמר.");
                } elseif (strpos($purpose, 'Generate Article Structure') !== false) {
                    $timeout_minutes = ceil($timeout / 60);
                    return new WP_Error('structure_timeout', 
                        "יצירת מבנה המאמר לוקחת זמן רב מהצפוי ({$timeout_minutes} דקות). זה יכול לקרות עם מאמרים מורכבים. אנא נסה שוב או הפשט את הדרישות.");
                } else {
                    return new WP_Error('operation_timeout', 
                        "הפעולה לוקחת זמן רב מהצפוי ({$timeout} שניות). אנא נסה שוב מאוחר יותר.");
                }
            }
            
            return $response;
        }
        
        $response_body = wp_remote_retrieve_body($response);
        $response_code = wp_remote_retrieve_response_code($response);
        
        $this->debug_log("📊 AI SEO API: Response code: $response_code");
        $this->debug_log("📄 AI SEO API: Response body length: " . strlen($response_body));
        
        if ($response_code !== 200) {
            $this->debug_log("❌ AI SEO API: Non-200 response code: $response_code");
            $this->debug_log("🔍 AI SEO API: Error response body: " . $response_body);
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown error';
            $error_type = $error_data['error']['type'] ?? 'unknown_error';
            
            $this->debug_log("💥 AI SEO API: Parsed error - Type: $error_type, Message: $error_message");
            
            // Handle specific error types
            if ($error_type === 'invalid_request_error' && strpos($error_message, 'max_tokens') !== false) {
                return new WP_Error('token_limit_exceeded', 'הבקשה ארוכה מדי. נסה להקטין את אורך המאמר המבוקש.');
            } elseif ($response_code === 429) {
                return new WP_Error('rate_limit_exceeded', 'הגעת למגבלת הבקשות. נסה שוב בעוד כמה דקות.');
            } elseif ($response_code >= 500) {
                return new WP_Error('server_error', 'שגיאת שרת ב-Claude API. נסה שוב בעוד כמה דקות.');
            }
            
            return new WP_Error('api_error', 'API Error: ' . $error_message);
        }
        
        $data = json_decode($response_body, true);
        $this->debug_log("🔍 AI SEO API: JSON decode result: " . (is_array($data) ? 'Success' : 'Failed'));
        
        if (!isset($data['content'][0]['text'])) {
            $this->debug_log("❌ AI SEO API: Invalid response structure");
            $this->debug_log("🔍 AI SEO API: Response structure", $data);
            return new WP_Error('invalid_response', 'Invalid API response format');
        }
        
        $response_text = $data['content'][0]['text'];
        $final_duration = round(microtime(true) - $start_time, 2);
        $this->debug_log("✅ AI SEO API: Successfully extracted response text, length: " . strlen($response_text) . " (total time: {$final_duration}s)");
        
        return $response_text;
    }
    
    private function make_openai_request($purpose, $prompt, $max_tokens = 1000, $timeout = 60) {
        $this->debug_log("🌐 AI SEO API (OpenAI): Making request for: $purpose");
        $this->debug_log("📏 AI SEO API (OpenAI): Max tokens: $max_tokens");
        $this->debug_log("⏰ AI SEO API (OpenAI): Timeout: {$timeout}s");
        
        // Set appropriate execution time limit for long-running operations
        if ($timeout > 300) {
            if (function_exists('set_time_limit') && !ini_get('safe_mode')) {
                // phpcs:ignore Squiz.PHP.DiscouragedFunctions.Discouraged -- Required for long API operations
                @set_time_limit(0);
            }
            $this->debug_log("⏰ AI SEO API (OpenAI): Removed execution time limit for long operation ({$timeout}s timeout)");
        } else {
            $execution_limit = ceil($timeout * 1.5);
            if (function_exists('set_time_limit') && !ini_get('safe_mode')) {
                // phpcs:ignore Squiz.PHP.DiscouragedFunctions.Discouraged -- Required for API timeout control
                @set_time_limit($execution_limit);
            }
            $this->debug_log("⏰ AI SEO API (OpenAI): Set execution time limit to {$execution_limit}s (request timeout: {$timeout}s)");
        }
        
        $start_time = microtime(true);
        $this->debug_log("🚀 AI SEO API (OpenAI): Starting request at " . gmdate('Y-m-d H:i:s') . " UTC with {$timeout}s timeout");
        
        if (empty($this->openai_api_key)) {
            $this->debug_log("❌ AI SEO API (OpenAI): OpenAI API key is empty in make_openai_request");
            return new WP_Error('no_api_key', 'OpenAI API key not configured');
        }
        
        $this->debug_log("🔑 AI SEO API (OpenAI): OpenAI API key length: " . strlen($this->openai_api_key));
        
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->openai_api_key,
        );
        
        $this->debug_log("📋 AI SEO API (OpenAI): Headers prepared");
        
        // Get selected OpenAI model
        $openai_model = get_option('ai_seo_article_generator_openai_model', 'gpt-4o');
        
        $body = array(
            'model' => $openai_model,
            'max_tokens' => $max_tokens,
            'temperature' => 0.7,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'אתה כותב תוכן מקצועי בעברית המתמחה ב-SEO, AEO ו-GEO. אתה מייצר תוכן איכותי, מפורט ומקיף בעברית טבעית.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            )
        );
        
        $this->debug_log("📦 AI SEO API (OpenAI): Request body prepared, JSON size: " . strlen(json_encode($body)));
        $this->debug_log("🎯 AI SEO API (OpenAI): Request URL: " . $this->openai_api_url);
        $this->debug_log("🤖 AI SEO API (OpenAI): Using model: " . $openai_model);
        $this->debug_log("⏰ AI SEO API (OpenAI): Request timeout: {$timeout}s");
        
        $response = wp_remote_post($this->openai_api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => $timeout,
        ));
        
        $end_time = microtime(true);
        $actual_duration = round($end_time - $start_time, 2);
        $this->debug_log("🏁 AI SEO API (OpenAI): Request completed in {$actual_duration}s (timeout was {$timeout}s)");
        
        $this->debug_log("📡 AI SEO API (OpenAI): Request sent, checking response...");
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->debug_log("❌ AI SEO API (OpenAI): WP_Error in response: " . $error_message);
            
            if (strpos($error_message, 'timed out') !== false || strpos($error_message, 'timeout') !== false) {
                $this->debug_log("⏰ AI SEO API (OpenAI): Request timed out after {$timeout}s");
                
                if ($purpose === 'Generate Article Content' || $purpose === 'Generate Article Content - Retry') {
                    $timeout_minutes = ceil($timeout / 60);
                    return new WP_Error('content_timeout', 
                        "יצירת המאמר באמצעות OpenAI לוקחת זמן רב מהצפוי ({$timeout_minutes} דקות). זה יכול לקרות עם מאמרים ארוכים או מורכבים. אנא נסה שוב או הקטן את אורך המאמר.");
                } elseif (strpos($purpose, 'Generate Article Structure') !== false) {
                    $timeout_minutes = ceil($timeout / 60);
                    return new WP_Error('structure_timeout', 
                        "יצירת מבנה המאמר באמצעות OpenAI לוקחת זמן רב מהצפוי ({$timeout_minutes} דקות). זה יכול לקרות עם מאמרים מורכבים. אנא נסה שוב או הפשט את הדרישות.");
                } else {
                    return new WP_Error('operation_timeout', 
                        "הפעולה באמצעות OpenAI לוקחת זמן רב מהצפוי ({$timeout} שניות). אנא נסה שוב מאוחר יותר.");
                }
            }
            
            return $response;
        }
        
        $response_body = wp_remote_retrieve_body($response);
        $response_code = wp_remote_retrieve_response_code($response);
        
        $this->debug_log("📊 AI SEO API (OpenAI): Response code: $response_code");
        $this->debug_log("📄 AI SEO API (OpenAI): Response body length: " . strlen($response_body));
        
        if ($response_code !== 200) {
            $this->debug_log("❌ AI SEO API (OpenAI): Non-200 response code: $response_code");
            $this->debug_log("🔍 AI SEO API (OpenAI): Error response body: " . $response_body);
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown error';
            $error_type = $error_data['error']['type'] ?? 'unknown_error';
            
            $this->debug_log("💥 AI SEO API (OpenAI): Parsed error - Type: $error_type, Message: $error_message");
            
            if ($response_code === 429) {
                return new WP_Error('rate_limit_exceeded', 'הגעת למגבלת הבקשות של OpenAI. נסה שוב בעוד כמה דקות.');
            } elseif ($response_code >= 500) {
                return new WP_Error('server_error', 'שגיאת שרת ב-OpenAI API. נסה שוב בעוד כמה דקות.');
            } elseif ($response_code === 401) {
                return new WP_Error('invalid_api_key', 'מפתח ה-API של OpenAI לא תקין. אנא בדוק את ההגדרות.');
            }
            
            return new WP_Error('api_error', 'OpenAI API Error: ' . $error_message);
        }
        
        $data = json_decode($response_body, true);
        $this->debug_log("🔍 AI SEO API (OpenAI): JSON decode result: " . (is_array($data) ? 'Success' : 'Failed'));
        
        if (!isset($data['choices'][0]['message']['content'])) {
            $this->debug_log("❌ AI SEO API (OpenAI): Invalid response structure");
            $this->debug_log("🔍 AI SEO API (OpenAI): Response structure", $data);
            return new WP_Error('invalid_response', 'Invalid OpenAI API response format');
        }
        
        $response_text = $data['choices'][0]['message']['content'];
        $final_duration = round(microtime(true) - $start_time, 2);
        $this->debug_log("✅ AI SEO API (OpenAI): Successfully extracted response text, length: " . strlen($response_text) . " (total time: {$final_duration}s)");
        
        return $response_text;
    }
    
    /**
     * Helper method to route requests to the appropriate AI provider
     */
    private function make_request($purpose, $prompt, $max_tokens = 1000, $timeout = 60) {
        if ($this->ai_provider === 'openai') {
            return $this->make_openai_request($purpose, $prompt, $max_tokens, $timeout);
        } else {
            return $this->make_claude_request($purpose, $prompt, $max_tokens, $timeout);
        }
    }
    
    private function build_structure_prompt($main_keyword, $sub_keywords, $target_words, $article_language = 'auto') {
        $sub_keywords_list = is_array($sub_keywords) ? implode(', ', $sub_keywords) : $sub_keywords;
        
        // Determine if content should be Hebrew
        $is_hebrew = $this->determine_content_language($article_language);
        $data = array('main_keyword' => $main_keyword, 'is_hebrew' => $is_hebrew);
        
        // Apply AEO enhancement filter
        if ($is_hebrew) {
            $base_prompt = "אתה כותב תוכן מקצועי בעברית המתמחה ב-SEO, AEO ו-GEO. צור מבנה מפורט למאמר אופטימלי בעברית.

מילת המפתח הראשית: {$main_keyword}
מילות מפתח משניות: {$sub_keywords_list}
אורך יעד: {$target_words} מילים

צור מבנה מאמר הכולל:
1. כותרת ראשית מושכת וידידותית לעיטור
2. תת-כותרות (H2, H3) עם שילוב טבעי של מילות המפתח
3. פסקאות מבוא וסיכום
4. רשימת נושאים עיקריים לכל פרק
5. הצעות לתוכן מולטימדיה (תמונות, וידאו)

🎯 דרישות AEO (אופטימיזציה למנועי תשובות) 2025:
- כל כותרת פרק חייבת להיות בצורת שאלה (מה זה {$main_keyword}? איך עובד {$main_keyword}? למה חשוב {$main_keyword}?)
- כל פרק יתחיל בתשובה ישירה של 40-60 מילים
- כלול קטעי הגדרה ברורים לכל מונח מרכזי
- תכנן מקום לדוגמאות ועובדות מספריות
- צור מבנה שקל לחלץ ממנו מידע (בלוקי מידע קצרים)
- הוסף קטעי שאלות נפוצות בכל פרק מרכזי

המבנה צריך להיות:
- ידידותי למנועי חיפוש (SEO)
- מותאם למנועי תשובות (AEO) - תשובות ישירות וברורות
- אופטימלי למנועי יצירה (GEO)
- בשפה עברית טבעית ומקצועית
- מאורגן בצורה הגיונית וקריאה

⚠️ חשוב מאוד: החזר רק JSON תקין ללא טקסט נוסף!
אל תשתמש בבלוק קוד markdown (```json)
החזר רק את ה-JSON הזה ושום דבר אחר:

{
    \"title\": \"כותרת ראשית מקצועית עם מילת המפתח\",
    \"introduction\": \"תיאור מפורט של המבוא שיכלול מילות מפתח\",
    \"sections\": [
        {
            \"heading\": \"פרק 1: כותרת H2 עם מילת מפתח\",
            \"subheadings\": [\"תת-נושא 1\", \"תת-נושא 2\", \"תת-נושא 3\"],
            \"topics\": [\"נושא מפורט 1\", \"נושא מפורט 2\", \"נושא מפורט 3\"],
            \"word_count\": 400
        },
        {
            \"heading\": \"פרק 2: כותרת H2 נוספת\",
            \"subheadings\": [\"תת-נושא 1\", \"תת-נושא 2\"],
            \"topics\": [\"נושא מפורט 1\", \"נושא מפורט 2\"],
            \"word_count\": 400
        }
    ],
    \"conclusion\": \"תיאור מפורט של הסיכום\",
    \"multimedia_suggestions\": [\"תמונה המדגימה את הנושא\", \"אינפוגרפיקה עם נתונים\", \"וידאו הסבר\"]
}

שים לב: 
- למאמר של 1000 מילים: צור 4-5 פרקים עיקריים
- למאמר של 1500 מילים: צור 5-6 פרקים עיקריים  
- למאמר של 2000 מילים: צור 6-7 פרקים עיקריים
- למאמר של 2500+ מילים: צור 7-9 פרקים עיקריים מפורטים

עבור מאמר של {$target_words} מילים - הקפד ליצור מספר פרקים מתאים ומפורטים!";
        } else {
            // English prompt
            $base_prompt = "You are a professional content writer specializing in SEO, AEO & GEO. Create a detailed structure for an optimal English article.

Main keyword: {$main_keyword}
Secondary keywords: {$sub_keywords_list}
Target length: {$target_words} words

Create an article structure including:
1. Engaging main title with keyword integration
2. Subheadings (H2, H3) with natural keyword integration
3. Introduction and conclusion paragraphs
4. List of main topics for each chapter
5. Multimedia content suggestions (images, video)

🎯 AEO (Answer Engine Optimization) Requirements 2025:
- Every chapter heading must be in question format (What is {$main_keyword}? How does {$main_keyword} work? Why is {$main_keyword} important?)
- Each chapter starts with a direct answer of 40-60 words
- Include clear definition sections for all key terms
- Plan space for examples and numerical facts
- Create structure that's easy to extract information from (short info blocks)
- Add FAQ sections in each main chapter

The structure should be:
- Search engine friendly (SEO)
- Optimized for answer engines (AEO) - direct and clear answers
- Optimal for generative engines (GEO)
- In natural, professional English
- Logically organized and readable

⚠️ Very important: Return only valid JSON without additional text!
Do not use markdown code blocks (```json)
Return only this JSON and nothing else:

{
    \"title\": \"Professional main title with keyword\",
    \"introduction\": \"Detailed description of introduction including keywords\",
    \"sections\": [
        {
            \"heading\": \"Chapter 1: H2 heading with keyword\",
            \"subheadings\": [\"Sub-topic 1\", \"Sub-topic 2\", \"Sub-topic 3\"],
            \"topics\": [\"Detailed topic 1\", \"Detailed topic 2\", \"Detailed topic 3\"],
            \"word_count\": 400
        },
        {
            \"heading\": \"Chapter 2: Another H2 heading\",
            \"subheadings\": [\"Sub-topic 1\", \"Sub-topic 2\"],
            \"topics\": [\"Detailed topic 1\", \"Detailed topic 2\"],
            \"word_count\": 400
        }
    ],
    \"conclusion\": \"Detailed description of conclusion section\",
    \"faq\": [
        {
            \"question\": \"Common question about {$main_keyword}\",
            \"answer_preview\": \"Brief answer preview\"
        }
    ],
    \"total_word_count\": {$target_words}
}

📊 Article Length Guidelines:
- For 500-1000 word articles: Create 3-4 main chapters
- For 1000-1500 word articles: Create 4-5 main chapters
- For 1500-2000 word articles: Create 5-6 detailed chapters
- For 2000-2500 word articles: Create 6-7 comprehensive chapters
- For 2500+ word articles: Create 7-9 detailed main chapters

For an article of {$target_words} words - ensure appropriate number of detailed chapters!";
        }
        
        // Apply AEO enhancement filter
        return apply_filters('ai_seo_article_generator_structure_prompt', $base_prompt, $data);
    }
    
    private function build_content_prompt($structure, $main_keyword, $sub_keywords, $target_words, $article_language = 'auto') {
        $structure_json = json_encode($structure, JSON_UNESCAPED_UNICODE);
        $sub_keywords_list = is_array($sub_keywords) ? implode(', ', $sub_keywords) : $sub_keywords;
        
        // Determine content language
        $is_hebrew = $this->determine_content_language($article_language);
        
        // Enhanced word distribution for longer articles
        $sections_count = isset($structure['sections']) ? count($structure['sections']) : 3;
        $intro_words = min(300, $target_words * 0.15); // 10-15% for intro
        $conclusion_words = min(250, $target_words * 0.12); // 10-12% for conclusion
        $remaining_words = $target_words - $intro_words - $conclusion_words;
        $words_per_section = floor($remaining_words / $sections_count);
        
        // For longer articles, increase minimum words per section
        if ($target_words >= 2500) {
            $words_per_section = max($words_per_section, 400);
        }
        
        // Calculate optimal keyword density (1.5-2.5%)
        $target_keyword_density = 2.0;
        $target_keyword_occurrences = ceil(($target_words * $target_keyword_density) / 100);
        
        // Build detailed section requirements with expansion instructions
        $section_requirements = "";
        if (isset($structure['sections'])) {
            foreach ($structure['sections'] as $index => $section) {
                $section_words = isset($section['word_count']) ? max($section['word_count'], $words_per_section) : $words_per_section;
                $section_keywords_needed = ceil($section_words * 0.02); // 2% density per section
                
                if ($is_hebrew) {
                    $section_requirements .= "פרק " . ($index + 1) . " ('{$section['heading']}'): לפחות {$section_words} מילים מפורטות\n";
                    $section_requirements .= "  • כלול לפחות 4-5 פסקאות\n";
                    $section_requirements .= "  • הוסף 2-3 דוגמאות מעשיות\n";
                    $section_requirements .= "  • כלול רשימה של 5-7 נקודות\n";
                    $section_requirements .= "  • הסבר מפורט וטכני\n";
                    $section_requirements .= "  • שלב את מילת המפתח '{$main_keyword}' {$section_keywords_needed} פעמים באופן טבעי\n\n";
                } else {
                    $section_requirements .= "Chapter " . ($index + 1) . " ('{$section['heading']}'): at least {$section_words} detailed words\n";
                    $section_requirements .= "  • Include at least 4-5 paragraphs\n";
                    $section_requirements .= "  • Add 2-3 practical examples\n";
                    $section_requirements .= "  • Include a list of 5-7 points\n";
                    $section_requirements .= "  • Provide detailed technical explanation\n";
                    $section_requirements .= "  • Integrate the keyword '{$main_keyword}' {$section_keywords_needed} times naturally\n\n";
                }
            }
        }
        
        // Enhanced instructions for longer articles
        if ($is_hebrew) {
            $length_category = $target_words >= 2500 ? 'ארוך' : ($target_words >= 1500 ? 'בינוני-ארוך' : 'בינוני');
            $expansion_level = $target_words >= 2500 ? 'מקסימלית' : 'גבוהה';
            
            $base_prompt = "🚨 כתוב מאמר מלא ומקיף בעברית - {$target_words} מילים חובה מוחלטת!
📚 סוג מאמר: {$length_category} (דרישת הרחבה: {$expansion_level})

⚠️ חשוב ביותר: המאמר חייב להכיל לפחות {$target_words} מילים! אל תעצור לפני שהגעת ליעד!

🎯 פרטי המאמר:
מילת המפתח הראשית: {$main_keyword}
מילות מפתח משניות: {$sub_keywords_list}
אורך יעד מחייב: {$target_words} מילים בדיוק (לא פחות!)
מבנה המאמר: {$structure_json}

📏 דרישות אורך מדויקות (סה\"כ {$target_words} מילים):
- מבוא מפורט: לפחות {$intro_words} מילים
- סיכום מקיף: לפחות {$conclusion_words} מילים
{$section_requirements}

🎯 דרישות SEO קריטיות:
- שילוב מילת המפתח '{$main_keyword}' בדיוק {$target_keyword_occurrences} פעמים (צפיפות {$target_keyword_density}%)
- חייב להיות H1 אחד בלבד עם מילת המפתח בכותרת הראשית
- לפחות {$sections_count} כותרות H2 עם שילוב מילות מפתח
- לפחות " . ($sections_count * 2) . " כותרות H3 לתת-נושאים
- שילוב טבעי של מילות המפתח המשניות בכל פרק

🔥 דרישות HTML וקידום:
- השתמש בכותרות היררכיות: H1→H2→H3→H4 בלבד
- H1: רק לכותרת הראשית (חייב לכלול מילת מפתח)
- H2: לכל פרק ראשי (5-7 פרקים מומלץ)
- H3: לתת-נושאים בתוך כל פרק (2-4 לכל פרק)
- H4: לפירוטים נוספים אם נדרש
- שימוש ברשימות <ul> ו-<ol> לארגון מידע
- פסקאות <p> עם אורך מיטבי (40-80 מילים)

🔥 דרישות כתיבה מחמירות (מאמר {$length_category}):
- כתוב כל פרק באופן מפורט ומקיף מאוד
- הוסף לפחות 4-6 פסקאות לכל תת-נושא  
- כלול דוגמאות מעשיות ומפורטות (לפחות 2-3 לכל פרק)
- הוסף רשימות מפורטות של 6-10 נקודות
- הרחב כל נושא עם פרטים טכניים ותובנות מעמיקות
- הוסף ציטוטים, נתונים, וסטטיסטיקות רלוונטיות
- כלול טיפים מעשיים וכלים ספציפיים
- אל תקצר - כתוב בהרחבה {$expansion_level}!
- התמקד באיכות ובעומק התוכן - לא רק באורך

🔥 דרישות תוכן:
1. כתוב מאמר מפורט ומקיף שמכסה את כל הנושאים במבנה
2. השתמש בכותרות מובנות: H1 לכותרת ראשית, H2 לפרקים ראשיים, H3 לתת-פרקים
3. כל פרק חייב להכיל:
   - הסבר מפורט ומקיף של הנושא
   - דוגמאות ומקרי שימוש ספציפיים
   - טיפים מעשיים וישימים
   - רשימות נקודות לטקסט יותר קריא

📝 סגנון כתיבה:
- שפה עברית טבעית, מקצועית ואנושית
- פסקאות של 3-5 שורות מקסימום
- שילוב טבעי של מילות המפתח
- טון מקצועי אך נגיש לקורא הרחב

🔍 אופטימיזציה:
- SEO: שילוב מילות מפתח בכותרות ובתוכן
- AEO: כלול שאלות ותשובות נפוצות
- GEO: תוכן שעונה על כוונות חיפוש שונות

🎯 דרישות AEO מתקדמות (אופטימיזציה למנועי תשובות 2025):
- התחל כל פרק עם תשובה ישירה של 40-60 מילים בתחילת הפסקה הראשונה
- השתמש בפורמט שיחתי שעונה על שאלות משתמשים
- כלול הגדרות ברורות למונחים מרכזיים
- הוסף נתונים ועובדות מספריות ספציפיות (שנת 2024-2025)
- צור בלוקי מידע קצרים וברורים שקל לחלץ מהם מידע
- הוסף קטע שאלות נפוצות קצר בכל פרק מרכזי
- השתמש במבנה 'התשובה הקצרה: ... הסבר מפורט: ...'
- כלול דוגמאות קונקרטיות ומעשיות

⚠️ חשוב מאוד: אל תכתוב 'המשך המאמר...' או 'האם תרצה שאמשיך?' - כתוב את המאמר המלא והמקיף מתחילה ועד סוף!

🔴 דרישת אורך קריטית:
- המאמר חייב להכיל בדיוק {$target_words} מילים!
- ספור את המילים בזמן הכתיבה וודא שאתה מגיע ליעד!
- אם המאמר קצר - הוסף עוד פרטים, דוגמאות ותוכן!
- אל תעצור עד שתגיע ל-{$target_words} מילים מדויקות!

⚠️ דרישות HTML קריטיות:
- התחל עם H1 אחד בלבד עם הכותרת הראשית
- כל פרק ראשי יתחיל עם H2
- כל תת-נושא יתחיל עם H3
- השתמש ברשימות מסודרות ולא מסודרות
- הקפד על סגירת תגיות נכונה

🎯 כתוב עכשיו את המאמר המלא של {$target_words} מילים בפורמט HTML נקי עם תגיות מתאימות:";
        } else {
            // English content prompt
            $length_category = $this->get_length_category_english($target_words);
            
            $base_prompt = "Write a comprehensive professional article in English.

🎯 Article Details:
Main keyword: {$main_keyword}
Secondary keywords: {$sub_keywords_list}
Target word count: {$target_words} words exactly (no less!)
Article structure: {$structure_json}

📏 Exact Length Requirements (total {$target_words} words):
- Detailed introduction: at least {$intro_words} words
- Comprehensive conclusion: at least {$conclusion_words} words
{$section_requirements}

🎯 Critical SEO Requirements:
- Include the keyword '{$main_keyword}' exactly {$target_keyword_occurrences} times (density {$target_keyword_density}%)
- Must have only one H1 with the keyword in the main title
- At least {$sections_count} H2 headings with keyword integration
- At least " . ($sections_count * 2) . " H3 headings for sub-topics
- Natural integration of secondary keywords in each section

🔥 HTML and SEO Requirements:
- Use hierarchical headings: H1→H2→H3→H4 only
- H1: Only for main title (must include keyword)
- H2: For each main section (5-7 sections recommended)
- H3: For sub-topics within each section (2-4 per section)
- H4: For additional details if needed
- Use <ul> and <ol> lists for information organization
- Use <p> paragraphs with optimal length (40-80 words)

🔥 Strict Writing Requirements ({$length_category} article):
- Write each section in detailed and comprehensive manner
- Add at least 4-6 paragraphs per sub-topic
- Include practical and detailed examples (at least 2-3 per section)
- Add detailed lists of 6-10 points
- Expand each topic with technical details and deep insights
- Add quotes, data, and relevant statistics
- Include actionable tips and best practices

🎯 AEO (Answer Engine Optimization) Requirements:
- Start each main section with a direct 40-60 word answer
- Use question-based H2 headings (What is {$main_keyword}? How does {$main_keyword} work?)
- Include clear definitions for technical terms
- Add FAQ sections within relevant chapters
- Structure content for easy AI extraction
- Provide numerical data and statistics where relevant

📊 Content Requirements:
- Write in natural, professional English
- Use active voice and clear, concise sentences
- Include transition sentences between sections
- Maintain consistent tone throughout
- Ensure all information is accurate and up-to-date

⚠️ Critical: The article MUST contain at least {$target_words} words! Do not stop before reaching the target!

HTML Structure Requirements:
- Start with H1 for main title
- Each main section starts with H2
- Each sub-topic starts with H3
- Use ordered and unordered lists
- Ensure proper tag closing

🎯 Write the complete {$target_words}-word article now in clean HTML format with appropriate tags:";
        }
        
        // Apply AEO enhancement filter
        $data = array('main_keyword' => $main_keyword, 'target_words' => $target_words, 'is_hebrew' => $is_hebrew);
        return apply_filters('ai_seo_article_generator_content_prompt', $base_prompt, $data);
    }
    
    private function build_enhancement_prompt($section_content, $main_keyword, $enhancement_type) {
        $enhancement_instructions = array(
            'improve' => 'שפר את התוכן, הוסף פרטים נוספים והפוך אותו לאיכותי יותר',
            'seo' => 'אופטימיזציה עבור SEO - שלב מילות מפתח והוסף אלמנטים טכניים',
            'readability' => 'שפר את הקריאות והבהירות של הטקסט',
            'expand' => 'הרחב את התוכן והוסף מידע נוסף ורלוונטי'
        );
        
        $instruction = isset($enhancement_instructions[$enhancement_type]) 
            ? $enhancement_instructions[$enhancement_type] 
            : $enhancement_instructions['improve'];
        
        return "שפר את הקטע הבא במאמר בעברית:

תוכן קיים:
{$section_content}

מילת מפתח ראשית: {$main_keyword}

הוראות שיפור: {$instruction}

דרישות:
1. שמור על השפה העברית הטבעית והמקצועית
2. שלב את מילת המפתח באופן טבעי
3. הוסף ערך אמיתי לקורא
4. שפר את האופטימיזציה עבור מנועי החיפוש
5. החזר רק את התוכן המשופר בפורמט HTML";
    }
    
    private function parse_structure_response($response) {
        $this->debug_log("🔍 Postinor: Starting JSON parsing, response length: " . strlen($response));
        $this->debug_log("📄 Postinor: Response preview (first 500 chars): " . substr($response, 0, 500));
        $this->debug_log("📄 Postinor: Response preview (last 500 chars): " . substr($response, -500));
        
        // Try multiple extraction methods
        $json_string = $this->extract_json_from_response($response);
        
        if ($json_string) {
            $this->debug_log("✅ Postinor: Extracted JSON string: " . $json_string);
            
            $structure = json_decode($json_string, true);
            $json_error = json_last_error();
            
            if ($json_error === JSON_ERROR_NONE && is_array($structure)) {
                $this->debug_log("🎯 Postinor: JSON parsed successfully");
                
                // Validate the structure has required fields
                $this->debug_log("🔍 Postinor: Validating structure with keys: " . implode(', ', array_keys($structure)));
                
                $missing_fields = array();
                if (!isset($structure['title'])) $missing_fields[] = 'title';
                if (!isset($structure['sections'])) $missing_fields[] = 'sections';
                if (!isset($structure['introduction'])) $missing_fields[] = 'introduction';
                if (!isset($structure['conclusion'])) $missing_fields[] = 'conclusion';
                
                if (empty($missing_fields)) {
                    $this->debug_log("✅ Postinor: Structure validation passed");
                    return $structure;
                } else {
                    $this->debug_log("❌ Postinor: Structure validation failed - missing fields: " . implode(', ', $missing_fields));
                }
            } else {
                $this->debug_log("❌ Postinor: JSON parsing failed - Error: " . json_last_error_msg());
            }
        } else {
            $this->debug_log("❌ Postinor: Could not extract JSON from response");
        }
        
        $this->debug_log("🔄 Postinor: Returning fallback structure");
        return array(
            'title' => 'מבנה המאמר לא ניתן לניתוח',
            'introduction' => 'תיאור המבוא',
            'sections' => array(),
            'conclusion' => 'תיאור הסיכום',
            'multimedia_suggestions' => array()
        );
    }
    
    private function extract_json_from_response($response) {
        // Method 1: Extract from markdown code blocks first
        if (preg_match('/```json\s*\n(.*?)\n```/s', $response, $matches)) {
            $candidate = trim($matches[1]);
            if ($this->is_valid_json($candidate)) {
                $this->debug_log("📦 Postinor: Found valid JSON in markdown code block");
                return $candidate;
            }
        }
        
        if (preg_match('/```\s*\n(\{.*?\})\s*\n```/s', $response, $matches)) {
            $candidate = trim($matches[1]);
            if ($this->is_valid_json($candidate)) {
                $this->debug_log("📦 Postinor: Found valid JSON in generic code block");
                return $candidate;
            }
        }
        
        // Method 2: Try balanced extraction from beginning (most reliable for complete structures)
        $balanced_json = $this->extract_balanced_json_from_start($response);
        if ($balanced_json) {
            if (strpos($balanced_json, '"title"') !== false && strpos($balanced_json, '"sections"') !== false) {
                $this->debug_log("🎯 Postinor: Found complete structure using balanced extraction");
                return $balanced_json;
            }
            $this->debug_log("📦 Postinor: Found valid JSON using balanced extraction");
            return $balanced_json;
        }
        
        // Method 3: Simple fallback extraction (first { to last })
        $json_start = strpos($response, '{');
        $json_end = strrpos($response, '}');
        
        if ($json_start !== false && $json_end !== false && $json_end > $json_start) {
            $json_string = substr($response, $json_start, $json_end - $json_start + 1);
            $this->debug_log("🔍 Postinor: Simple extraction - Length: " . strlen($json_string) . ", Valid: " . ($this->is_valid_json($json_string) ? 'Yes' : 'No'));
            $this->debug_log("📄 Postinor: Simple extraction preview: " . substr($json_string, 0, 200) . "..." . substr($json_string, -100));
            
            if ($this->is_valid_json($json_string)) {
                // Check if this is a complete structure
                if (strpos($json_string, '"title"') !== false && strpos($json_string, '"sections"') !== false) {
                    $this->debug_log("🎯 Postinor: Found complete structure using simple extraction");
                    return trim($json_string);
                }
                $this->debug_log("📦 Postinor: Found valid JSON using start/end positions (simple extraction)");
                return trim($json_string);
            } else {
                $this->debug_log("⚠️ Postinor: JSON from start/end positions is invalid, attempting to fix...");
                $fixed_json = $this->attempt_json_fix($json_string);
                if ($fixed_json && $this->is_valid_json($fixed_json)) {
                    // Check if fixed JSON has complete structure
                    if (strpos($fixed_json, '"title"') !== false && strpos($fixed_json, '"sections"') !== false) {
                        $this->debug_log("🎯 Postinor: Fixed JSON contains complete structure");
                        return $fixed_json;
                    }
                    $this->debug_log("🔧 Postinor: Successfully fixed JSON from simple extraction");
                    return $fixed_json;
                }
            }
        }
        
        // Method 4: Use improved bracket counting for complete JSON objects
        $json_candidates = $this->find_complete_json_objects_improved($response);
        
        if (!empty($json_candidates)) {
            $this->debug_log("📦 Postinor: Found " . count($json_candidates) . " JSON candidates");
            
            // Prioritize candidates by completeness and position
            $prioritized_candidates = array();
            foreach ($json_candidates as $candidate) {
                if ($this->is_valid_json($candidate)) {
                    $score = 0;
                    $length = strlen($candidate);
                    $position = strpos($response, $candidate);
                    
                    // Higher score for larger objects (more likely to be complete)
                    $score += $length;
                    
                    // Bonus for having required structure fields
                    if (strpos($candidate, '"title"') !== false) $score += 1000;
                    if (strpos($candidate, '"sections"') !== false) $score += 1000;
                    if (strpos($candidate, '"introduction"') !== false) $score += 500;
                    if (strpos($candidate, '"conclusion"') !== false) $score += 500;
                    
                    // Bonus for appearing early in response (more likely to be main structure)
                    if ($position < 100) $score += 200;
                    
                    $prioritized_candidates[] = array(
                        'json' => $candidate,
                        'score' => $score,
                        'length' => $length,
                        'position' => $position
                    );
                    
                    $this->debug_log("✅ Postinor: Valid JSON candidate - Length: $length, Position: $position, Score: $score");
                } else {
                    $this->debug_log("❌ Postinor: Invalid JSON candidate, length: " . strlen($candidate));
                }
            }
            
            // Sort by score (highest first)
            if (!empty($prioritized_candidates)) {
                usort($prioritized_candidates, function($a, $b) {
                    return $b['score'] - $a['score'];
                });
                
                $best_candidate = $prioritized_candidates[0];
                $this->debug_log("🏆 Postinor: Using best candidate - Score: {$best_candidate['score']}, Length: {$best_candidate['length']}");
                return $best_candidate['json'];
            }
        }
        
        $this->debug_log("❌ Postinor: All JSON extraction methods failed");
        return false;
    }
    
    private function is_response_truncated($response) {
        // Check for common signs of truncation
        $truncation_indicators = array(
            // Response ends mid-word (especially Hebrew/English mix)
            '/[א-ת][a-zA-Z]$/',
            '/[a-zA-Z][א-ת]$/',
            // Response ends mid-sentence
            '/[^.!?}"\]]$/',
            // Response ends with incomplete JSON structure
            '/[,:]$/',
            '/["\']$/',
            // Response ends with incomplete word
            '/\b\w{1,3}$/',
        );
        
        $trimmed = trim($response);
        $last_50 = substr($trimmed, -50);
        
        foreach ($truncation_indicators as $pattern) {
            if (preg_match($pattern, $last_50)) {
                $this->debug_log("🔍 Postinor: Truncation detected with pattern: $pattern on text: " . substr($last_50, -20));
                return true;
            }
        }
        
        // Check if response contains incomplete JSON structure
        $first_brace = strpos($response, '{');
        $last_brace = strrpos($response, '}');
        
        if ($first_brace !== false && $last_brace !== false) {
            $json_section = substr($response, $first_brace, $last_brace - $first_brace + 1);
            
            // Count open vs closed brackets
            $open_braces = substr_count($json_section, '{');
            $close_braces = substr_count($json_section, '}');
            $open_brackets = substr_count($json_section, '[');
            $close_brackets = substr_count($json_section, ']');
            
            if ($open_braces > $close_braces || $open_brackets > $close_brackets) {
                $this->debug_log("🔍 Postinor: Unbalanced JSON detected - Open braces: $open_braces, Close braces: $close_braces");
                return true;
            }
        }
        
        $this->debug_log("✅ Postinor: Response appears complete");
        return false;
    }
    
    private function extract_balanced_json_from_start($text) {
        $start_pos = strpos($text, '{');
        if ($start_pos === false) {
            return false;
        }
        
        $length = strlen($text);
        $bracket_count = 0;
        $in_string = false;
        $escape_next = false;
        
        for ($i = $start_pos; $i < $length; $i++) {
            $char = $text[$i];
            
            if ($escape_next) {
                $escape_next = false;
                continue;
            }
            
            if ($char === '\\') {
                $escape_next = true;
                continue;
            }
            
            if ($char === '"' && !$escape_next) {
                $in_string = !$in_string;
                continue;
            }
            
            if (!$in_string) {
                if ($char === '{') {
                    $bracket_count++;
                } elseif ($char === '}') {
                    $bracket_count--;
                    
                    if ($bracket_count === 0) {
                        // Found complete balanced JSON
                        $json_candidate = substr($text, $start_pos, $i - $start_pos + 1);
                        if ($this->is_valid_json($json_candidate)) {
                            $this->debug_log("✅ Postinor: Balanced extraction found valid JSON, length: " . strlen($json_candidate));
                            return trim($json_candidate);
                        } else {
                            $this->debug_log("❌ Postinor: Balanced extraction found invalid JSON, length: " . strlen($json_candidate));
                            return false;
                        }
                    }
                }
            }
        }
        
        $this->debug_log("❌ Postinor: Balanced extraction could not find complete JSON object");
        return false;
    }
    
    private function is_valid_json($string) {
        if (empty($string)) {
            return false;
        }
        
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
    
    private function find_complete_json_objects_improved($text) {
        $objects = array();
        $length = strlen($text);
        
        for ($i = 0; $i < $length; $i++) {
            if ($text[$i] === '{') {
                $bracket_count = 0;
                $in_string = false;
                $escape_next = false;
                $start = $i;
                
                for ($j = $i; $j < $length; $j++) {
                    $char = $text[$j];
                    
                    if ($escape_next) {
                        $escape_next = false;
                        continue;
                    }
                    
                    if ($char === '\\') {
                        $escape_next = true;
                        continue;
                    }
                    
                    if ($char === '"' && !$escape_next) {
                        $in_string = !$in_string;
                        continue;
                    }
                    
                    if (!$in_string) {
                        if ($char === '{') {
                            $bracket_count++;
                        } elseif ($char === '}') {
                            $bracket_count--;
                            
                            if ($bracket_count === 0) {
                                $json_candidate = substr($text, $start, $j - $start + 1);
                                if (strlen($json_candidate) > 50) { // Only substantial objects
                                    $objects[] = trim($json_candidate);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        $this->debug_log("🔍 Postinor: Found " . count($objects) . " complete JSON objects using improved bracket counting");
        return $objects;
    }
    
    private function attempt_json_fix($json_string) {
        if (empty($json_string)) {
            return false;
        }
        
        $trimmed = trim($json_string);
        
        // Count brackets to see if we need to add closing ones
        $open_braces = substr_count($trimmed, '{');
        $close_braces = substr_count($trimmed, '}');
        $open_brackets = substr_count($trimmed, '[');
        $close_brackets = substr_count($trimmed, ']');
        
        // Add missing closing braces
        $missing_braces = $open_braces - $close_braces;
        $missing_brackets = $open_brackets - $close_brackets;
        
        if ($missing_braces > 0) {
            $trimmed .= str_repeat('}', $missing_braces);
            $this->debug_log("🔧 Postinor: Added $missing_braces closing braces");
        }
        
        if ($missing_brackets > 0) {
            $trimmed .= str_repeat(']', $missing_brackets);
            $this->debug_log("🔧 Postinor: Added $missing_brackets closing brackets");
        }
        
        // Try to fix common JSON issues
        $fixes = array(
            // Remove trailing commas
            '/,\s*([}\]])/s' => '$1',
            // Fix unquoted keys (simple case)
            '/(\{|,)\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:/' => '$1"$2":',
            // Remove comments
            '/\/\*.*?\*\//s' => '',
            '/\/\/.*$/m' => ''
        );
        
        foreach ($fixes as $pattern => $replacement) {
            $before = $trimmed;
            $trimmed = preg_replace($pattern, $replacement, $trimmed);
            if ($before !== $trimmed) {
                $this->debug_log("🔧 Postinor: Applied fix: $pattern");
            }
        }
        
        return trim($trimmed);
    }
    
    private function find_complete_json_objects($text) {
        $objects = array();
        $length = strlen($text);
        
        for ($i = 0; $i < $length; $i++) {
            if ($text[$i] === '{') {
                $bracket_count = 0;
                $start = $i;
                
                for ($j = $i; $j < $length; $j++) {
                    if ($text[$j] === '{') {
                        $bracket_count++;
                    } elseif ($text[$j] === '}') {
                        $bracket_count--;
                        
                        if ($bracket_count === 0) {
                            $json_candidate = substr($text, $start, $j - $start + 1);
                            if (strlen($json_candidate) > 100) { // Only substantial objects
                                $objects[] = trim($json_candidate);
                            }
                            break;
                        }
                    }
                }
            }
        }
        
        $this->debug_log("🔍 Postinor: Found " . count($objects) . " complete JSON objects using bracket counting");
        return $objects;
    }
    
    private function validate_content_length($content, $target_words) {
        // Use improved Hebrew word counting
        $word_count = $this->count_hebrew_words($content);
        
        // More stringent validation - require at least 60% of target for acceptance
        $minimum_words = floor($target_words * 0.6);
        
        return array(
            'valid' => $word_count >= $minimum_words,
            'word_count' => $word_count,
            'target_words' => $target_words,
            'minimum_words' => $minimum_words,
            'percentage' => round(($word_count / $target_words) * 100, 1)
        );
    }
    
    private function validate_content_structure($content, $main_keyword, $target_words) {
        $issues = array();
        $is_valid = true;
        
        // Check for H1 tags (should have exactly 1)
        $h1_count = preg_match_all('/<h1[^>]*>/i', $content);
        if ($h1_count === 0) {
            $issues[] = 'חסר כותרת H1';
            $is_valid = false;
        } elseif ($h1_count > 1) {
            $issues[] = 'יותר מכותרת H1 אחת';
            $is_valid = false;
        }
        
        // Check for H2 tags (should have at least 3)
        $h2_count = preg_match_all('/<h2[^>]*>/i', $content);
        if ($h2_count < 3) {
            $issues[] = 'חסרים כותרות H2 (נמצאו ' . $h2_count . ', נדרשות לפחות 3)';
            $is_valid = false;
        }
        
        // Check for H3 tags (should have at least 2)
        $h3_count = preg_match_all('/<h3[^>]*>/i', $content);
        if ($h3_count < 2) {
            $issues[] = 'חסרים כותרות H3 (נמצאו ' . $h3_count . ', נדרשות לפחות 2)';
            $is_valid = false;
        }
        
        // Check keyword density using improved word counting
        $text = wp_strip_all_tags($content);
        $word_count = $this->count_hebrew_words($content);
        $keyword_count = substr_count(mb_strtolower($text), mb_strtolower($main_keyword));
        $keyword_density = $word_count > 0 ? ($keyword_count / $word_count) * 100 : 0;
        
        if ($keyword_density < 1.0) {
            $issues[] = 'צפיפות מילת המפתח נמוכה מדי (' . round($keyword_density, 2) . '%, נדרש לפחות 1%)';
            $is_valid = false;
        } elseif ($keyword_density > 4.0) {
            $issues[] = 'צפיפות מילת המפתח גבוהה מדי (' . round($keyword_density, 2) . '%, מומלץ מתחת ל-4%)';
        }
        
        // Check for lists (should have at least 2)
        $list_count = preg_match_all('/<(ul|ol)[^>]*>/i', $content);
        if ($list_count < 2) {
            $issues[] = 'חסרים רשימות (נמצאו ' . $list_count . ', מומלץ לפחות 2)';
        }
        
        // Check for proper paragraph structure
        $paragraph_count = preg_match_all('/<p[^>]*>/i', $content);
        $expected_paragraphs = max(8, floor($target_words / 80)); // ~80 words per paragraph
        if ($paragraph_count < $expected_paragraphs / 2) {
            $issues[] = 'מעט מדי פסקאות (נמצאו ' . $paragraph_count . ', מצופה ' . $expected_paragraphs . ')';
        }
        
        return array(
            'valid' => $is_valid,
            'issues' => $issues,
            'stats' => array(
                'h1_count' => $h1_count,
                'h2_count' => $h2_count,
                'h3_count' => $h3_count,
                'keyword_density' => round($keyword_density, 2),
                'keyword_count' => $keyword_count,
                'list_count' => $list_count,
                'paragraph_count' => $paragraph_count
            )
        );
    }
    
    private function enhance_content_structure($content, $main_keyword, $issues) {
        $enhanced_content = $content;
        
        // Auto-fix missing H1 if needed
        if (in_array('חסר כותרת H1', $issues)) {
            // Try to convert the first heading to H1
            $enhanced_content = preg_replace('/<h2([^>]*)>(.*?)<\/h2>/i', '<h1$1>$2</h1>', $enhanced_content, 1);
        }
        
        // Auto-fix multiple H1s if needed
        if (strpos(implode('', $issues), 'יותר מכותרת H1 אחת') !== false) {
            // Convert additional H1s to H2s
            $h1_count = 0;
            $enhanced_content = preg_replace_callback('/<h1([^>]*)>(.*?)<\/h1>/i', function($matches) use (&$h1_count) {
                $h1_count++;
                if ($h1_count > 1) {
                    return '<h2' . $matches[1] . '>' . $matches[2] . '</h2>';
                }
                return $matches[0];
            }, $enhanced_content);
        }
        
        return $enhanced_content;
    }
    
    private function optimize_keyword_density($content, $main_keyword, $target_words) {
        // Calculate current keyword stats using improved word counting
        $text = wp_strip_all_tags($content);
        $word_count = $this->count_hebrew_words($content);
        $current_keyword_count = substr_count(mb_strtolower($text), mb_strtolower($main_keyword));
        $current_density = $word_count > 0 ? ($current_keyword_count / $word_count) * 100 : 0;
        
        // Target density should be 1.5-2.5%
        $target_density = 2.0;
        $target_keyword_count = ceil(($word_count * $target_density) / 100);
        $keywords_to_add = max(0, $target_keyword_count - $current_keyword_count);
        
        if ($keywords_to_add <= 0) {
            return $content; // No optimization needed
        }
        
        $this->debug_log("🎯 Postinor: Optimizing keyword density - need to add {$keywords_to_add} instances of '{$main_keyword}'");
        
        $optimized_content = $content;
        $keywords_added = 0;
        
        // Strategy 1: Add keywords to headings naturally
        $optimized_content = preg_replace_callback(
            '/<(h[2-6])([^>]*)>(.*?)<\/h[2-6]>/i',
            function($matches) use ($main_keyword, &$keywords_added, $keywords_to_add) {
                if ($keywords_added >= $keywords_to_add) return $matches[0];
                
                $heading_text = $matches[3];
                $lower_text = mb_strtolower($heading_text);
                $lower_keyword = mb_strtolower($main_keyword);
                
                // Only add if keyword not already in heading
                if (strpos($lower_text, $lower_keyword) === false) {
                    // Add keyword naturally to the heading
                    $enhanced_heading = $heading_text . ' - ' . $main_keyword;
                    $keywords_added++;
                    $this->debug_log("🔧 Postinor: Added keyword to heading: {$heading_text}");
                    return '<' . $matches[1] . $matches[2] . '>' . $enhanced_heading . '</' . $matches[1] . '>';
                }
                
                return $matches[0];
            },
            $optimized_content
        );
        
        // Strategy 2: Add keywords to paragraph content naturally
        if ($keywords_added < $keywords_to_add) {
            $paragraphs_processed = 0;
            $optimized_content = preg_replace_callback(
                '/<p([^>]*)>(.*?)<\/p>/is',
                function($matches) use ($main_keyword, &$keywords_added, $keywords_to_add, &$paragraphs_processed) {
                    $paragraphs_processed++;
                    
                    // Skip every other paragraph to avoid over-optimization
                    if ($paragraphs_processed % 2 !== 0 || $keywords_added >= $keywords_to_add) {
                        return $matches[0];
                    }
                    
                    $paragraph_text = $matches[2];
                    $lower_text = mb_strtolower($paragraph_text);
                    $lower_keyword = mb_strtolower($main_keyword);
                    
                    // Only add if keyword not already in paragraph
                    if (strpos($lower_text, $lower_keyword) === false && strlen(wp_strip_all_tags($paragraph_text)) > 50) {
                        // Find a natural place to insert the keyword
                        $sentences = preg_split('/[.!?]+/', $paragraph_text);
                        if (count($sentences) > 1) {
                            // Insert keyword in first sentence naturally
                            $first_sentence = trim($sentences[0]);
                            if (!empty($first_sentence)) {
                                // Add keyword at the end of first sentence
                                $enhanced_paragraph = str_replace(
                                    $first_sentence,
                                    $first_sentence . ', כאשר ' . $main_keyword,
                                    $paragraph_text
                                );
                                $keywords_added++;
                                $this->debug_log("🔧 Postinor: Added keyword to paragraph naturally");
                                return '<p' . $matches[1] . '>' . $enhanced_paragraph . '</p>';
                            }
                        }
                    }
                    
                    return $matches[0];
                },
                $optimized_content
            );
        }
        
        // Log final optimization results
        $this->debug_log("✅ Postinor: Keyword optimization complete - added {$keywords_added} instances");
        
        return $optimized_content;
    }
    
    /**
     * Generate long articles (>2500 words) using section-by-section approach
     */
    private function generate_long_article_content($structure, $main_keyword, $sub_keywords, $target_words) {
        $this->debug_log("🔄 AI SEO API: Starting section-by-section generation for {$target_words} words");
        
        if (!isset($structure['sections']) || empty($structure['sections'])) {
            $this->debug_log("❌ AI SEO API: No sections found in structure for long article generation");
            return array('success' => false, 'message' => 'מבנה המאמר לא מכיל פרקים מתאימים ליצירה מתקדמת');
        }
        
        $sections = $structure['sections'];
        $words_per_section = floor($target_words / count($sections));
        $generated_sections = array();
        $total_words = 0;
        
        // Generate introduction
        $intro_words = min(400, $target_words * 0.15);
        $intro_content = $this->generate_section_content('introduction', $structure['introduction'] ?? '', $main_keyword, $sub_keywords, $intro_words);
        if ($intro_content) {
            $generated_sections[] = $intro_content;
            $intro_word_count = $this->count_hebrew_words($intro_content);
            $total_words += $intro_word_count;
            $this->debug_log("✅ AI SEO API: Generated introduction - {$intro_word_count} words");
        }
        
        // Generate each section
        foreach ($sections as $index => $section) {
            $section_target_words = max($words_per_section, 300);
            
            $this->debug_log("🔄 AI SEO API: Generating section " . ($index + 1) . " - target: {$section_target_words} words");
            
            $section_content = $this->generate_section_content(
                'section',
                $section,
                $main_keyword,
                $sub_keywords,
                $section_target_words
            );
            
            if ($section_content) {
                $generated_sections[] = $section_content;
                $section_word_count = $this->count_hebrew_words($section_content);
                $total_words += $section_word_count;
                $this->debug_log("✅ AI SEO API: Generated section " . ($index + 1) . " - {$section_word_count} words");
            } else {
                $this->debug_log("⚠️ AI SEO API: Failed to generate section " . ($index + 1) . ", continuing...");
            }
            
            // Small delay between sections to avoid rate limiting
            sleep(2);
        }
        
        // Generate conclusion
        $conclusion_words = min(300, $target_words * 0.12);
        $conclusion_content = $this->generate_section_content('conclusion', $structure['conclusion'] ?? '', $main_keyword, $sub_keywords, $conclusion_words);
        if ($conclusion_content) {
            $generated_sections[] = $conclusion_content;
            $conclusion_word_count = $this->count_hebrew_words($conclusion_content);
            $total_words += $conclusion_word_count;
            $this->debug_log("✅ AI SEO API: Generated conclusion - {$conclusion_word_count} words");
        }
        
        // Combine all sections
        $full_content = implode("\n\n", $generated_sections);
        
        // If still short, extend the content
        if ($total_words < $target_words * 0.8) {
            $this->debug_log("⚠️ AI SEO API: Long article still short ({$total_words} words), attempting extension");
            $extended_content = $this->extend_article_content($full_content, $main_keyword, $target_words, $total_words);
            if ($extended_content) {
                $full_content = $extended_content;
                $total_words = $this->count_hebrew_words($full_content);
                $this->debug_log("✅ AI SEO API: Extended long article to {$total_words} words");
            }
        }
        
        $this->debug_log("🎉 AI SEO API: Long article generation completed - {$total_words} words total");
        
        return array(
            'success' => true,
            'content' => $full_content,
            'word_count' => $total_words,
            'generation_method' => 'section_by_section'
        );
    }
    
    /**
     * Generate content for a specific section
     */
    private function generate_section_content($type, $section_data, $main_keyword, $sub_keywords, $target_words) {
        $prompt = $this->build_section_prompt($type, $section_data, $main_keyword, $sub_keywords, $target_words);
        
        $max_tokens = min(4000, $target_words * 7); // Conservative for sections
        $timeout = max(120, $target_words / 3); // More time per word for sections
        
        $response = $this->make_request("Generate {$type} Content", $prompt, $max_tokens, $timeout);
        
        if (is_wp_error($response)) {
            $this->debug_log("❌ AI SEO API: Failed to generate {$type}: " . $response->get_error_message());
            return false;
        }
        
        return $response;
    }
    
    /**
     * Build prompt for section generation
     */
    private function build_section_prompt($type, $section_data, $main_keyword, $sub_keywords, $target_words) {
        $sub_keywords_list = is_array($sub_keywords) ? implode(', ', $sub_keywords) : $sub_keywords;
        
        switch ($type) {
            case 'introduction':
                return "כתוב מבוא מפורט ומקיף למאמר בעברית על '{$main_keyword}'.
                
מילות מפתח משניות: {$sub_keywords_list}
אורך יעד: {$target_words} מילים בדיוק
תיאור המבוא: {$section_data}

דרישות:
- כתוב בעברית טבעית ומקצועית
- שלב את מילת המפתח הראשית 2-3 פעמים
- הוסף הקשר ורקע לנושא
- צור מתח ועניין לקורא
- כלול רשימה של הנושאים שיכוסו במאמר
- השתמש בפסקאות קצרות (3-4 שורות)
- הוסף שאלות רטוריות לעניין הקורא

החזר רק את התוכן בפורמט HTML עם תגיות p, ul, ol לפי הצורך.";
                
            case 'conclusion':
                return "כתוב סיכום מפורט ומקיף למאמר בעברית על '{$main_keyword}'.
                
מילות מפתח משניות: {$sub_keywords_list}
אורך יעד: {$target_words} מילים בדיוק
תיאור הסיכום: {$section_data}

דרישות:
- כתוב בעברית טבעית ומקצועית
- סכם את הנקודות העיקריות שנדונו
- שלב את מילת המפתח הראשית 2-3 פעמים
- הוסף המלצות מעשיות לקורא
- כלול call-to-action מתאים
- הוסף שאלות לחשיבה נוספת
- השתמש בפסקאות קצרות (3-4 שורות)

החזר רק את התוכן בפורמט HTML עם תגיות p, ul, ol לפי הצורך.";
                
            default: // section
                $heading = $section_data['heading'] ?? 'פרק במאמר';
                $topics = isset($section_data['topics']) ? implode(', ', $section_data['topics']) : '';
                $subheadings = isset($section_data['subheadings']) ? implode(', ', $section_data['subheadings']) : '';
                
                return "כתוב פרק מפורט ומקיף למאמר בעברית.

כותרת הפרק: {$heading}
מילת מפתח ראשית: {$main_keyword}
מילות מפתח משניות: {$sub_keywords_list}
אורך יעד: {$target_words} מילים בדיוק
נושאים לכיסוי: {$topics}
תת-כותרות: {$subheadings}

דרישות:
- התחל עם H2 לכותרת הפרק
- כתוב בעברית טבעית ומקצועית
- שלב את מילת המפתח הראשית 3-4 פעמים באופן טבעי
- הוסף תת-כותרות H3 לארגון הפרק
- כלול לפחות 4-5 פסקאות מפורטות
- הוסף דוגמאות מעשיות ומקרי שימוש
- כלול רשימות של 5-7 נקודות
- הוסף טיפים מעשיים וישימים
- השתמש בפסקאות קצרות (3-4 שורות)

החזר רק את התוכן בפורמט HTML עם תגיות h2, h3, p, ul, ol לפי הצורך.";
        }
    }
    
    /**
     * Extend article content when it's shorter than target
     */
    private function extend_article_content($content, $main_keyword, $target_words, $current_words) {
        $words_needed = $target_words - $current_words;
        
        if ($words_needed < 200) {
            return false; // Not worth extending for small amounts
        }
        
        $this->debug_log("🔄 AI SEO API: Extending content by {$words_needed} words");
        
        $prompt = "הרחב ושפר את המאמר הבא בעברית על '{$main_keyword}'.

התוכן הנוכחי:
{$content}

דרישות הרחבה:
- הוסף בדיוק {$words_needed} מילים נוספות
- הרחב פרקים קיימים עם פרטים נוספים
- הוסף דוגמאות מעשיות ומקרי שימוש
- כלול טיפים מתקדמים וטכניקות מומחה
- הוסף רשימות מפורטות וסטטיסטיקות
- שלב את מילת המפתח '{$main_keyword}' באופן טבעי
- שמור על עקביות הסגנון והטון
- וודא שההרחבה מתחברת באופן טבעי לתוכן הקיים

החזר את כל המאמר המורחב בפורמט HTML.";
        
        $max_tokens = min(8000, ($target_words + 500) * 7);
        $timeout = max(240, $words_needed / 3);
        
        $extended_response = $this->make_request('Extend Article Content', $prompt, $max_tokens, $timeout);
        
        if (is_wp_error($extended_response)) {
            $this->debug_log("❌ AI SEO API: Failed to extend content: " . $extended_response->get_error_message());
            return false;
        }
        
        return $extended_response;
    }
    
    /**
     * Count Hebrew words more accurately
     */
    private function count_hebrew_words($content) {
        // Use the accurate word counting function
        if (function_exists('ai_seo_accurate_word_count')) {
            return ai_seo_accurate_word_count($content);
        }
        
        // Improved Hebrew word counting
        $text = wp_strip_all_tags($content);
        $text = preg_replace('/\s+/', ' ', trim($text));
        
        // Method 1: Count Hebrew character sequences
        $hebrew_count = preg_match_all('/[\p{Hebrew}\p{N}]+/u', $text);
        
        // Method 2: Count English words
        $english_count = preg_match_all('/[a-zA-Z]+/', $text);
        
        // Method 3: Simple space-based counting
        $space_count = count(array_filter(explode(' ', $text)));
        
        // Method 4: Original str_word_count with Hebrew chars
        $str_count = str_word_count($text, 0, 'אבגדהוזחטיכלמנסעפצקרשתךםןףץ0123456789');
        
        // Use the highest count (most conservative approach)
        $final_count = max($hebrew_count, $english_count, $space_count, $str_count);
        
        $this->debug_log("Word count methods: Hebrew={$hebrew_count}, English={$english_count}, Space={$space_count}, Str={$str_count}, Final={$final_count}");
        
        return $final_count;
    }
}