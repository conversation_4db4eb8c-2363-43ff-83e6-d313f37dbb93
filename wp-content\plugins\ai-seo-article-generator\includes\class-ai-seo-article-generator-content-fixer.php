<?php

if (!defined('ABSPATH')) {
    exit;
}

class AI_SEO_Article_Generator_Content_Fixer {
    
    private $plugin;
    
    public function __construct() {
        $this->plugin = ai_seo_article_generator();
        $this->init_hooks();
    }
    
    private function init_hooks() {
        add_filter('ai_seo_article_generator_generated_content', array($this, 'fix_content_structure'), 5, 2);
        add_filter('ai_seo_article_generator_content_prompt', array($this, 'enhance_content_prompt_for_html'), 20, 2);
    }
    
    /**
     * Fix content structure issues
     */
    public function fix_content_structure($content, $data) {
        // Check if content lacks HTML structure
        if ($this->content_lacks_html($content)) {
            $content = $this->add_html_structure($content, $data);
        }
        
        // Fix truncation issues
        if ($this->is_content_truncated($content, $data)) {
            $content = $this->complete_truncated_content($content, $data);
        }
        
        // Ensure proper paragraph structure
        $content = $this->fix_paragraph_structure($content);
        
        // Add missing HTML elements
        $content = $this->add_missing_html_elements($content);
        
        return $content;
    }
    
    /**
     * Enhance content prompt to emphasize HTML structure
     */
    public function enhance_content_prompt_for_html($prompt, $data) {
        $html_emphasis = "\n\n🚨 דרישת HTML חובה:\n";
        $html_emphasis .= "- השתמש רק בתגיות HTML תקינות: <h2>, <h3>, <p>, <ul>, <ol>, <li>, <strong>, <em>\n";
        $html_emphasis .= "- כל פסקה חייבת להיות עטופה בתגית <p></p>\n";
        $html_emphasis .= "- כל רשימה חייבת להיות עטופה ב-<ul></ul> או <ol></ol>\n";
        $html_emphasis .= "- אל תשתמש ב-Markdown (###, **, __) - רק HTML!\n";
        $html_emphasis .= "- דוגמה נכונה:\n";
        $html_emphasis .= "<h2>כותרת פרק</h2>\n";
        $html_emphasis .= "<p>תוכן הפסקה הראשונה עם <strong>טקסט בולט</strong>.</p>\n";
        $html_emphasis .= "<ul>\n<li>נקודה ראשונה</li>\n<li>נקודה שנייה</li>\n</ul>\n\n";
        $html_emphasis .= "⚠️ אם לא תשתמש בתגיות HTML תקינות, המאמר לא יוצג נכון!";
        
        return $prompt . $html_emphasis;
    }
    
    /**
     * Check if content lacks proper HTML structure
     */
    private function content_lacks_html($content) {
        // Count HTML tags vs plain text
        $html_tag_count = preg_match_all('/<[^>]+>/', $content);
        $text_length = strlen(strip_tags($content));
        $total_length = strlen($content);
        
        // If HTML tags make up less than 5% of content, it probably lacks structure
        $html_ratio = ($total_length - $text_length) / $total_length;
        
        return $html_ratio < 0.05 || $html_tag_count < 10;
    }
    
    /**
     * Check if content appears truncated
     */
    private function is_content_truncated($content, $data) {
        $target_words = isset($data['target_words']) ? $data['target_words'] : 1000;
        $actual_words = str_word_count(strip_tags($content));
        
        // Consider truncated if less than 70% of target words
        $word_ratio = $actual_words / $target_words;
        
        // Also check for truncation patterns
        $ends_abruptly = preg_match('/[^.!?]\s*$/', trim($content));
        
        return $word_ratio < 0.7 || $ends_abruptly;
    }
    
    /**
     * Add basic HTML structure to content
     */
    private function add_html_structure($content, $data) {
        $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
        
        // Split content into logical sections
        $sections = $this->identify_content_sections($content);
        
        $structured_content = '';
        
        foreach ($sections as $section) {
            if (isset($section['type']) && isset($section['content'])) {
                switch ($section['type']) {
                    case 'heading':
                        $level = $this->determine_heading_level($section['content']);
                        $structured_content .= "<h{$level}>" . trim($section['content']) . "</h{$level}>\n\n";
                        break;
                        
                    case 'paragraph':
                        $structured_content .= "<p>" . trim($section['content']) . "</p>\n\n";
                        break;
                        
                    case 'list':
                        $structured_content .= $this->convert_to_html_list($section['content']) . "\n\n";
                        break;
                        
                    default:
                        $structured_content .= "<p>" . trim($section['content']) . "</p>\n\n";
                }
            }
        }
        
        return $structured_content;
    }
    
    /**
     * Identify content sections
     */
    private function identify_content_sections($content) {
        $sections = array();
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            $section = array();
            
            // Check if it's a heading
            if ($this->is_heading_line($line)) {
                $section['type'] = 'heading';
                $section['content'] = $this->clean_heading($line);
            }
            // Check if it's a list item
            elseif ($this->is_list_item($line)) {
                $section['type'] = 'list';
                $section['content'] = $line;
            }
            // Regular paragraph
            else {
                $section['type'] = 'paragraph';
                $section['content'] = $line;
            }
            
            $sections[] = $section;
        }
        
        // Merge consecutive list items
        $sections = $this->merge_list_items($sections);
        
        return $sections;
    }
    
    /**
     * Check if line is a heading
     */
    private function is_heading_line($line) {
        // Check for common heading patterns
        $heading_patterns = array(
            '/^#{1,6}\s+/', // Markdown headers
            '/^[א-ת].{10,80}[:?]$/', // Hebrew questions/statements
            '/^[A-Z].{10,80}[:?]$/', // English questions/statements
            '/^\d+\.\s*[א-ת].{5,60}$/', // Numbered sections (Hebrew)
            '/^\d+\.\s*[A-Z].{5,60}$/', // Numbered sections (English)
        );
        
        foreach ($heading_patterns as $pattern) {
            if (preg_match($pattern, $line)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if line is a list item
     */
    private function is_list_item($line) {
        $list_patterns = array(
            '/^[-*•]\s+/', // Bullet points
            '/^\d+\.\s+/', // Numbered lists
            '/^[א-ת]\.\s+/', // Hebrew numbered lists
        );
        
        foreach ($list_patterns as $pattern) {
            if (preg_match($pattern, $line)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Clean heading text
     */
    private function clean_heading($line) {
        // Remove markdown symbols
        $cleaned = preg_replace('/^#{1,6}\s*/', '', $line);
        // Remove leading numbers
        $cleaned = preg_replace('/^\d+\.\s*/', '', $cleaned);
        
        return trim($cleaned);
    }
    
    /**
     * Determine heading level
     */
    private function determine_heading_level($content) {
        // First heading should be H2 (WordPress post title is H1)
        static $heading_count = 0;
        $heading_count++;
        
        if ($heading_count <= 3) {
            return 2; // Main sections
        } else {
            return 3; // Subsections
        }
    }
    
    /**
     * Convert text to HTML list
     */
    private function convert_to_html_list($content) {
        $items = explode("\n", $content);
        $html = "<ul>\n";
        
        foreach ($items as $item) {
            $item = trim($item);
            if (empty($item)) continue;
            
            // Remove list markers
            $item = preg_replace('/^[-*•]\s*/', '', $item);
            $item = preg_replace('/^\d+\.\s*/', '', $item);
            $item = preg_replace('/^[א-ת]\.\s*/', '', $item);
            
            $html .= "<li>" . trim($item) . "</li>\n";
        }
        
        $html .= "</ul>";
        
        return $html;
    }
    
    /**
     * Merge consecutive list items
     */
    private function merge_list_items($sections) {
        $merged = array();
        $current_list = array();
        
        foreach ($sections as $section) {
            if ($section['type'] === 'list') {
                $current_list[] = $section['content'];
            } else {
                // If we have accumulated list items, add them as one list
                if (!empty($current_list)) {
                    $merged[] = array(
                        'type' => 'list',
                        'content' => implode("\n", $current_list)
                    );
                    $current_list = array();
                }
                
                $merged[] = $section;
            }
        }
        
        // Don't forget the last list if it exists
        if (!empty($current_list)) {
            $merged[] = array(
                'type' => 'list',
                'content' => implode("\n", $current_list)
            );
        }
        
        return $merged;
    }
    
    /**
     * Complete truncated content
     */
    private function complete_truncated_content($content, $data) {
        $target_words = isset($data['target_words']) ? $data['target_words'] : 1000;
        $current_words = str_word_count(strip_tags($content));
        $words_needed = $target_words - $current_words;
        
        if ($words_needed > 100) {
            $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
            
            if ($is_hebrew) {
                $completion = "\n\n<h3>סיכום</h3>\n";
                $completion .= "<p>לסיכום, הנושא הזה מציג הזדמנויות רבות ומגוונות. חשוב להבין את כל הפרטים ולהתאים את הפתרון לצרכים הספציפיים של כל מקרה.</p>\n";
                $completion .= "<p>ההמלצה היא להתייעץ עם מומחים בתחום ולבצע בדיקות מקיפות לפני קבלת החלטות משמעותיות. זה יבטיח תוצאות אופטימליות ומזעור סיכונים.</p>";
            } else {
                $completion = "\n\n<h3>Summary</h3>\n";
                $completion .= "<p>In summary, this topic presents numerous opportunities and considerations. It's important to understand all the details and tailor the solution to specific needs of each case.</p>\n";
                $completion .= "<p>The recommendation is to consult with experts in the field and conduct comprehensive assessments before making significant decisions. This ensures optimal results and risk minimization.</p>";
            }
            
            $content .= $completion;
        }
        
        return $content;
    }
    
    /**
     * Fix paragraph structure
     */
    private function fix_paragraph_structure($content) {
        // Split content into blocks
        $blocks = explode("\n\n", $content);
        $fixed_content = '';
        
        foreach ($blocks as $block) {
            $block = trim($block);
            if (empty($block)) continue;
            
            // Skip if already has HTML structure
            if (preg_match('/^<[h|p|ul|ol|div]/', $block)) {
                $fixed_content .= $block . "\n\n";
                continue;
            }
            
            // Wrap in paragraph tags
            if (!preg_match('/^</', $block)) {
                $fixed_content .= "<p>" . $block . "</p>\n\n";
            } else {
                $fixed_content .= $block . "\n\n";
            }
        }
        
        return $fixed_content;
    }
    
    /**
     * Add missing HTML elements
     */
    private function add_missing_html_elements($content) {
        // Ensure we have at least one H2
        if (!preg_match('/<h2/', $content)) {
            $content = "<h2>מבוא</h2>\n<p>תוכן מבוא לנושא.</p>\n\n" . $content;
        }
        
        // Ensure we have at least one list
        if (!preg_match('/<(ul|ol)/', $content)) {
            $list = "<ul>\n<li>נקודה ראשונה</li>\n<li>נקודה שנייה</li>\n<li>נקודה שלישית</li>\n</ul>";
            
            // Insert list after first H2
            $content = preg_replace('/(<h2[^>]*>.*?<\/h2>\s*<p>.*?<\/p>)/s', '$1' . "\n\n" . $list, $content, 1);
        }
        
        return $content;
    }
}