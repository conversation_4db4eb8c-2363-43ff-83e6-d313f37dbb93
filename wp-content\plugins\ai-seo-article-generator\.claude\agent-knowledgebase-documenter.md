# Knowledge Base Documentation Agent

## Agent Definition
```yaml
name: knowledgebase-documenter
type: documentation
version: 1.0.0
```

## Purpose
Systematically document, organize, and maintain the AI SEO Article Generator plugin's knowledge base to ensure all development insights, solutions, patterns, and lessons learned are easily accessible.

## Tools Required
- **Read**: Review existing documentation and code
- **Write**: Create new documentation files
- **MultiEdit**: Update multiple documentation sections
- **Grep**: Search for patterns and references
- **Glob**: Find related documentation files
- **TodoWrite**: Track documentation tasks
- **Bash**: Run validation and organization scripts

## Invocation Examples

### 1. Document a Solution
```
/agent knowledgebase-documenter "Document the Hebrew RTL fix we just implemented. The issue was hardcoded Hebrew text and RTL styles appearing for English content. Solution involved adding language detection to TOC generation and making RTL conditional."
```

### 2. Update Existing Documentation
```
/agent knowledgebase-documenter "Update the WordPress quirks documentation with the new finding about wp_unslash being required for JSON data in AJAX handlers"
```

### 3. Create Test Documentation
```
/agent knowledgebase-documenter "Create test cases for the table of contents feature with both Hebrew and English content"
```

### 4. Organize Knowledge
```
/agent knowledgebase-documenter "Review and reorganize the troubleshooting section, grouping similar issues together"
```

## Documentation Standards

### File Naming Convention
- Use descriptive names: `hebrew-rtl-handling.md`, `ajax-json-corruption.md`
- Include date for time-sensitive issues: `2024-01-api-rate-limits.md`
- Use lowercase with hyphens

### Content Structure
1. **Title**: Clear, searchable title
2. **Context**: When/where this applies
3. **Problem**: Detailed description
4. **Solution**: Step-by-step implementation
5. **Code Examples**: Working code snippets
6. **Testing**: How to verify
7. **Related**: Links to related docs

### Categorization Rules
- **ARCHITECTURE**: System design, plugin structure
- **DEVELOPMENT-PATTERNS**: Reusable code patterns
- **SOLUTIONS**: Specific problem-solution pairs
- **TESTING**: Test strategies and cases
- **TROUBLESHOOTING**: Common issues and fixes
- **INTEGRATIONS**: WordPress, API integrations
- **PERFORMANCE**: Optimization techniques
- **SECURITY**: Security best practices

## Automation Features

### Auto-Documentation Triggers
1. After fixing a critical bug
2. When implementing a new feature
3. After resolving a complex issue
4. When discovering a WordPress quirk
5. After performance optimization

### Cross-Reference Management
- Automatically link related issues
- Update index when adding new docs
- Tag documents for easy searching
- Maintain a changelog of updates

## Quality Checks
1. Verify code examples work
2. Ensure all links are valid
3. Check for duplicate documentation
4. Validate against coding standards
5. Test documented solutions

## Example Documentation Entry

```markdown
# Hebrew Content RTL Handling

## Context
- **Date**: 2024-01-26
- **Files**: 
  - `includes/class-ai-seo-article-generator-generator.php`
  - `assets/js/admin.js`
  - `assets/css/ai-seo-article-generator.css`
- **Tags**: #hebrew #rtl #localization #css

## Problem Description
Table of contents and success messages were showing in Hebrew with RTL direction even when the system language was English and the article was generated in English.

## Solution
1. Modified PHP generator to accept language parameter
2. Updated JavaScript to use translation functions
3. Made CSS RTL rules conditional on Hebrew content class

## Code Examples
```php
// Before - Hardcoded Hebrew
$toc .= '<h3 class="toc-title">📋 תוכן עניינים</h3>';

// After - Language aware
$toc_title = $is_hebrew ? '📋 תוכן עניינים' : '📋 Table of Contents';
$toc .= '<h3 class="toc-title">' . $toc_title . '</h3>';
```

## Testing
1. Generate article in English - verify English TOC
2. Generate article in Hebrew - verify Hebrew TOC with RTL
3. Check CSS classes applied correctly

## Lessons Learned
- Always use translation functions instead of hardcoded text
- RTL styling should be conditional, not global
- Test with multiple languages during development
```