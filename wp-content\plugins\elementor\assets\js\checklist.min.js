/*! elementor - v3.31.0 - 11-08-2025 */
/*! For license information please see checklist.min.js.LICENSE.txt */
(()=>{var r={7470:(r,u,s)=>{"use strict";var l=s(75206);u.createRoot=l.createRoot,u.hydrateRoot=l.hydrateRoot},8762:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=l(s(41594)),p=l(s(85707)),_=l(s(62688)),m=l(s(54871)),y=s(86956),v=s(44048),g=s(20244),h=s(55041);function ownKeys(r,u){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);u&&(l=l.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),s.push.apply(s,l)}return s}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(r,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(s,u))})}return r}var E=h.STEP.PROMOTION_DATA,x=h.MIXPANEL_CHECKLIST_STEPS.TITLE,b=h.MIXPANEL_CHECKLIST_STEPS.ACCORDION_SECTION;function CheckListItem(r){var u=r.expandedIndex,s=r.setExpandedIndex,l=r.setSteps,p=r.index,_=r.step,h=p===u?{transform:"rotate(180deg)"}:{},P=(0,g.isStepChecked)(_),O=_.config[E];return c.default.createElement(c.default.Fragment,null,c.default.createElement(y.ListItemButton,{onClick:function handleExpandClick(){(0,g.addMixpanelTrackingChecklistSteps)(_.config.id,x,b),s(p===u?-1:p)},"data-step-id":_.config.id,dense:!0},c.default.createElement(y.ListItemIcon,null,c.default.createElement(y.Checkbox,{"data-is-checked":P,icon:c.default.createElement(v.RadioButtonUncheckedIcon,null),checkedIcon:c.default.createElement(v.CircleCheckFilledIcon,{color:"primary"}),edge:"start",checked:P,tabIndex:-1,inputProps:{"aria-labelledby":_.config.title}})),c.default.createElement(y.ListItemText,{primary:_.config.title,primaryTypographyProps:{variant:"body2"}}),O?function getUpgradeIcon(){return"default"===(null==O?void 0:O.icon)?c.default.createElement(v.UpgradeIcon,{color:"promotion",sx:{mr:1}}):c.default.createElement(y.SvgIcon,{color:"promotion",sx:{mr:1}},c.default.createElement("img",{src:null==O?void 0:O.icon,alt:O.iconAlt||""}))}():null,c.default.createElement(v.ChevronDownIcon,{sx:_objectSpread(_objectSpread({},h),{},{transition:"300ms"})})),c.default.createElement(y.Collapse,{in:p===u},c.default.createElement(m.default,{step:_,setSteps:l})))}u.default=CheckListItem;CheckListItem.propTypes={step:_.default.object.isRequired,expandedIndex:_.default.number,setExpandedIndex:_.default.func.isRequired,setSteps:_.default.func.isRequired,index:_.default.number.isRequired}},9535:(r,u,s)=>{var l=s(89736);function _regenerator(){var u,s,c="function"==typeof Symbol?Symbol:{},p=c.iterator||"@@iterator",_=c.toStringTag||"@@toStringTag";function i(r,c,p,_){var y=c&&c.prototype instanceof Generator?c:Generator,v=Object.create(y.prototype);return l(v,"_invoke",function(r,l,c){var p,_,y,v=0,g=c||[],h=!1,E={p:0,n:0,v:u,a:d,f:d.bind(u,4),d:function d(r,s){return p=r,_=0,y=u,E.n=s,m}};function d(r,l){for(_=r,y=l,s=0;!h&&v&&!c&&s<g.length;s++){var c,p=g[s],x=E.p,b=p[2];r>3?(c=b===l)&&(y=p[(_=p[4])?5:(_=3,3)],p[4]=p[5]=u):p[0]<=x&&((c=r<2&&x<p[1])?(_=0,E.v=l,E.n=p[1]):x<b&&(c=r<3||p[0]>l||l>b)&&(p[4]=r,p[5]=l,E.n=b,_=0))}if(c||r>1)return m;throw h=!0,l}return function(c,g,x){if(v>1)throw TypeError("Generator is already running");for(h&&1===g&&d(g,x),_=g,y=x;(s=_<2?u:y)||!h;){p||(_?_<3?(_>1&&(E.n=-1),d(_,y)):E.n=y:E.v=y);try{if(v=2,p){if(_||(c="next"),s=p[c]){if(!(s=s.call(p,y)))throw TypeError("iterator result is not an object");if(!s.done)return s;y=s.value,_<2&&(_=0)}else 1===_&&(s=p.return)&&s.call(p),_<2&&(y=TypeError("The iterator does not provide a '"+c+"' method"),_=1);p=u}else if((s=(h=E.n<0)?y:r.call(l,E))!==m)break}catch(r){p=u,_=1,y=r}finally{v=1}}return{value:s,done:h}}}(r,p,_),!0),v}var m={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}s=Object.getPrototypeOf;var y=[][p]?s(s([][p]())):(l(s={},p,function(){return this}),s),v=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(y);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,l(r,_,"GeneratorFunction")),r.prototype=Object.create(v),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,l(v,"constructor",GeneratorFunctionPrototype),l(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",l(GeneratorFunctionPrototype,_,"GeneratorFunction"),l(v),l(v,_,"Generator"),l(v,p,function(){return this}),l(v,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},9730:r=>{"use strict";r.exports=elementorV2.query},9892:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.UserProgress=void 0;var c=l(s(39805)),p=l(s(40989)),_=l(s(15118)),m=l(s(29402)),y=l(s(87861));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var v=u.UserProgress=function(r){function UserProgress(){return(0,c.default)(this,UserProgress),function _callSuper(r,u,s){return u=(0,m.default)(u),(0,_.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,m.default)(r).constructor):u.apply(r,s))}(this,UserProgress,arguments)}return(0,y.default)(UserProgress,r),(0,p.default)(UserProgress,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist/user-progress"}}])}($e.modules.CommandData);u.default=v},10564:r=>{function _typeof(u){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(u)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10906:(r,u,s)=>{var l=s(91819),c=s(20365),p=s(37744),_=s(78687);r.exports=function _toConsumableArray(r){return l(r)||c(r)||p(r)||_()},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,u,s)=>{var l=s(10564).default;r.exports=function toPrimitive(r,u){if("object"!=l(r)||!r)return r;var s=r[Symbol.toPrimitive];if(void 0!==s){var c=s.call(r,u||"default");if("object"!=l(c))return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},13507:r=>{"use strict";r.exports=elementorV2.icons.RocketIcon},15118:(r,u,s)=>{var l=s(10564).default,c=s(36417);r.exports=function _possibleConstructorReturn(r,u){if(u&&("object"==l(u)||"function"==typeof u))return u;if(void 0!==u)throw new TypeError("Derived constructors may only return object or undefined");return c(r)},r.exports.__esModule=!0,r.exports.default=r.exports},16712:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=l(s(41594)),p=s(86956),_=s(12470),m=l(s(62688)),y=function ReminderModal(r){var u=r.setOpen;return c.default.createElement(p.Card,{elevation:0,sx:{maxWidth:336},className:"e-checklist-infotip-first-time-closed"},c.default.createElement(p.CardContent,null,c.default.createElement(p.Typography,{variant:"subtitle2",sx:{mb:2}},(0,_.__)("Looking for your Launchpad Checklist?","elementor")),c.default.createElement(p.Typography,{variant:"body2"},(0,_.__)("Click the launch icon to continue setting up your site.","elementor"))),c.default.createElement(p.CardActions,null,c.default.createElement(p.Button,{size:"small",variant:"contained",className:"infotip-first-time-closed-button",onClick:function closeChecklist(r){r.stopPropagation(),u(!1)}},(0,_.__)("Got it","elementor"))))};u.default=y;y.propTypes={setOpen:m.default.func.isRequired}},18821:(r,u,s)=>{var l=s(70569),c=s(65474),p=s(37744),_=s(11018);r.exports=function _slicedToArray(r,u){return l(r)||c(r,u)||p(r,u)||_()},r.exports.__esModule=!0,r.exports.default=r.exports},19070:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),_=l(s(18821)),m=s(86956),y=l(s(8762)),v=l(s(62688)),g=l(s(26917)),h=s(20244);var E=function ChecklistWrapper(r){var u=r.steps,s=r.setSteps,l=r.isMinimized,c=(0,p.useState)(-1),v=(0,_.default)(c,2),E=v[0],x=v[1],b=u.filter(h.isStepChecked).length===u.length;return p.default.createElement(m.Box,{sx:{transition:"400ms",maxHeight:l?0:"645px"}},p.default.createElement(m.List,{component:"div",sx:{py:0}},u.map(function(r,u){return p.default.createElement(p.Fragment,{key:u},u?p.default.createElement(m.Divider,null):null,p.default.createElement(y.default,{step:r,setSteps:s,setExpandedIndex:x,expandedIndex:E,index:u}))})),b?p.default.createElement(g.default,null):null)};u.default=E;E.propTypes={steps:v.default.array.isRequired,setSteps:v.default.func.isRequired,isMinimized:v.default.bool.isRequired}},20244:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.addMixpanelTrackingChecklistHeader=function addMixpanelTrackingChecklistHeader(r){var u=getDocumentMetaDataMixpanel();return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.elementorEditor.checklist[r],_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.checklistHeader,trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements.buttonIcon},u))},u.addMixpanelTrackingChecklistSteps=function addMixpanelTrackingChecklistSteps(r,u){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"button",l=getDocumentMetaDataMixpanel();r=r.replace(/_/g,"");var c="checklist_steps_".concat(u,"_").concat(r);return elementor.editorEvents.dispatchEvent(c,_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.checklistSteps,trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements[s]},l))},u.addMixpanelTrackingChecklistTopBar=function addMixpanelTrackingChecklistTopBar(r){var u=getDocumentMetaDataMixpanel(),s=r?"launchpadOff":"launchpadOn";return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.topBar[s],_objectSpread({location:elementor.editorEvents.config.locations.topBar,secondaryLocation:elementor.editorEvents.config.secondaryLocations.launchpad,trigger:elementor.editorEvents.config.triggers.toggleClick,element:elementor.editorEvents.config.elements.buttonIcon},u))},u.dispatchChecklistOpenEvent=function dispatchChecklistOpenEvent(){var r=getDocumentMetaDataMixpanel();return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.elementorEditor.checklist.checklistFirstPopup,_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.launchpad,trigger:elementor.editorEvents.config.triggers.editorLoaded,element:elementor.editorEvents.config.elements.launchpadChecklist},r))},u.fetchSteps=function fetchSteps(){return _fetchSteps.apply(this,arguments)},u.fetchUserProgress=function fetchUserProgress(){return _fetchUserProgress.apply(this,arguments)},u.getAndUpdateStep=function getAndUpdateStep(r,u,s,l){if(u.config.id!==r)return u;return _objectSpread(_objectSpread({},u),{},(0,p.default)({},s,l))},u.getDocumentMetaDataMixpanel=getDocumentMetaDataMixpanel,u.isStepChecked=function isStepChecked(r){return!r[h]&&(r[y]||r[v]||r[g])},u.toggleChecklistPopup=function toggleChecklistPopup(){$e.run("checklist/toggle-popup")},u.updateStep=function updateStep(r,u){return _updateStep.apply(this,arguments)},u.updateUserProgress=function updateUserProgress(r){return _updateUserProgress.apply(this,arguments)};var c=l(s(61790)),p=l(s(85707)),_=l(s(58155)),m=s(55041);function ownKeys(r,u){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);u&&(l=l.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),s.push.apply(s,l)}return s}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(r,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(s,u))})}return r}var y=m.STEP.IS_MARKED_COMPLETED,v=m.STEP.IS_ABSOLUTE_COMPLETED,g=m.STEP.IS_IMMUTABLE_COMPLETED,h=m.STEP.PROMOTION_DATA;function _fetchSteps(){return(_fetchSteps=(0,_.default)(c.default.mark(function _callee(){var r,u;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=1,$e.data.get(m.STEPS_ROUTE,{},{refresh:!0});case 1:return u=s.sent,s.abrupt("return",(null==u||null===(r=u.data)||void 0===r?void 0:r.data)||null);case 2:case"end":return s.stop()}},_callee)}))).apply(this,arguments)}function _fetchUserProgress(){return(_fetchUserProgress=(0,_.default)(c.default.mark(function _callee2(){var r,u;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=1,$e.data.get(m.USER_PROGRESS_ROUTE,{},{refresh:!0});case 1:return u=s.sent,s.abrupt("return",(null==u||null===(r=u.data)||void 0===r?void 0:r.data)||null);case 2:case"end":return s.stop()}},_callee2)}))).apply(this,arguments)}function _updateStep(){return(_updateStep=(0,_.default)(c.default.mark(function _callee3(r,u){return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=1,$e.data.update(m.STEPS_ROUTE,_objectSpread({id:r},u),{id:r});case 1:return s.abrupt("return",s.sent);case 2:case"end":return s.stop()}},_callee3)}))).apply(this,arguments)}function _updateUserProgress(){return(_updateUserProgress=(0,_.default)(c.default.mark(function _callee4(r){return c.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=1,$e.data.update(m.USER_PROGRESS_ROUTE,r);case 1:return u.abrupt("return",u.sent);case 2:case"end":return u.stop()}},_callee4)}))).apply(this,arguments)}function getDocumentMetaDataMixpanel(){return{postId:elementor.getPreviewContainer().document.config.id,postTitle:elementor.getPreviewContainer().model.attributes.settings.attributes.post_title,postTypeTitle:elementor.getPreviewContainer().document.config.post_type_title,documentType:elementor.getPreviewContainer().document.config.type}}},20365:r=>{r.exports=function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)},r.exports.__esModule=!0,r.exports.default=r.exports},21947:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.TogglePopup=void 0;var c=l(s(41594)),p=l(s(39805)),_=l(s(40989)),m=l(s(15118)),y=l(s(29402)),v=l(s(87861)),g=l(s(85707)),h=l(s(50288)),E=s(9730),x=l(s(7470)),b=s(20244),P=s(55041);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var O=new E.QueryClient,S=u.TogglePopup=function(r){function TogglePopup(){return(0,p.default)(this,TogglePopup),function _callSuper(r,u,s){return u=(0,y.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,y.default)(r).constructor):u.apply(r,s))}(this,TogglePopup,arguments)}return(0,v.default)(TogglePopup,r),(0,_.default)(TogglePopup,[{key:"apply",value:function apply(r){TogglePopup.isOpen?this.unmount():this.mount(),TogglePopup.isOpen=!TogglePopup.isOpen,r.isOpen=TogglePopup.isOpen,(0,b.updateUserProgress)((0,g.default)({},P.USER_PROGRESS.LAST_OPENED_TIMESTAMP,TogglePopup.isOpen))}},{key:"mount",value:function mount(){this.setRootElement(),TogglePopup.rootElement.render(c.default.createElement(E.QueryClientProvider,{client:O},c.default.createElement(h.default,null)))}},{key:"unmount",value:function unmount(){TogglePopup.rootElement.unmount(),document.body.removeChild(document.body.querySelector("#e-checklist"))}},{key:"setRootElement",value:function setRootElement(){var r=document.body.querySelector("#e-checklist");r||((r=document.createElement("div")).id="e-checklist",document.body.appendChild(r)),TogglePopup.rootElement=x.default.createRoot(r)}}])}($e.modules.CommandBase);(0,g.default)(S,"rootElement",null),(0,g.default)(S,"isOpen",!1);u.default=S},25233:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=l(s(18821)),_=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),m=_,y=s(9730),v=s(54142),g=l(s(13507)),h=s(86956),E=l(s(16712)),x=s(55041),b=s(20244);var P=x.USER_PROGRESS.CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME;u.default=function TopBarIcon(){var r=(0,_.useState)(!1),u=(0,p.default)(r,2),s=u[0],l=u[1],c=(0,_.useState)(!1),x=(0,p.default)(c,2),O=x[0],S=x[1],T=(0,y.useQuery)({queryKey:["closedForFirstTime"],queryFn:b.fetchUserProgress}),k=T.error,C=T.data,M=null==C?void 0:C[P];return(0,_.useEffect)(function(){return(0,v.__privateListenTo)((0,v.commandEndEvent)("checklist/toggle-popup"),function(r){l(r.args.isOpen)})},[s]),(0,_.useEffect)(function(){var r=function handleFirstClosed(){S(!0)};return window.addEventListener("elementor/checklist/first_close",r),function(){window.removeEventListener("elementor/checklist/first_close",r)}},[]),k?null:s&&!M?m.createElement(g.default,null):m.createElement(h.Infotip,{placement:"bottom-start",content:m.createElement(E.default,{setHasRoot:l,setOpen:S}),open:O,PopperProps:{modifiers:[{name:"offset",options:{offset:[-16,12]}}]}},m.createElement(g.default,null))}},26917:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),_=s(86956),m=s(12470),y=l(s(73921)),v=s(20244),g=s(55041);var h=g.MIXPANEL_CHECKLIST_STEPS.ACTION,E=g.MIXPANEL_CHECKLIST_STEPS.WELL_DONE;u.default=function SuccessMessage(){var r=(0,y.default)(),u=r.ajaxState,s=r.setAjax;return(0,p.useEffect)(function(){if("success"===u.status)setTimeout(function(){$e.commands.run("checklist/toggle-icon",!1)},0)},[u]),p.default.createElement(_.Card,{elevation:0,square:!0,className:"e-checklist-done"},p.default.createElement(_.CardMedia,{image:"https://assets.elementor.com/checklist/v1/images/checklist-step-7.jpg",sx:{height:180}}),p.default.createElement(_.CardContent,{sx:{textAlign:"center"}},p.default.createElement(_.Typography,{variant:"h6",color:"text.primary"},(0,m.__)("You're on your way!","elementor")),p.default.createElement(_.Typography,{variant:"body2",color:"text.secondary",component:"p"},(0,m.__)("With these steps, you've got a great base for a robust website. Enjoy your web creation journey!","elementor"))),p.default.createElement(_.CardActions,{sx:{justifyContent:"center"}},p.default.createElement(_.Button,{color:"primary",size:"small",variant:"contained",onClick:function hideChecklist(){(0,v.addMixpanelTrackingChecklistSteps)(E,h),s({data:{action:"elementor_ajax",actions:JSON.stringify({save_editorPreferences_settings:{action:"save_editorPreferences_settings",data:{data:{show_launchpad_checklist:""}}}})}})}},(0,m.__)("Got it","elementor"))))}},29402:r=>{function _getPrototypeOf(u){return r.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},r.exports.__esModule=!0,r.exports.default=r.exports,_getPrototypeOf(u)}r.exports=_getPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},33318:(r,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"ToggleIcon",{enumerable:!0,get:function get(){return c.ToggleIcon}}),Object.defineProperty(u,"TogglePopup",{enumerable:!0,get:function get(){return l.TogglePopup}});var l=s(21947),c=s(88564)},33929:(r,u,s)=>{var l=s(67114),c=s(89736);r.exports=function AsyncIterator(r,u){function n(s,c,p,_){try{var m=r[s](c),y=m.value;return y instanceof l?u.resolve(y.v).then(function(r){n("next",r,p,_)},function(r){n("throw",r,p,_)}):u.resolve(y).then(function(r){m.value=r,p(m)},function(r){return n("throw",r,p,_)})}catch(r){_(r)}}var s;this.next||(c(AsyncIterator.prototype),c(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),c(this,"_invoke",function(r,l,c){function f(){return new u(function(u,s){n(r,c,u,s)})}return s=s?s.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},35843:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=l(s(61790)),_=l(s(85707)),m=l(s(58155)),y=s(86956),v=s(12470),g=l(s(56741)),h=l(s(62688)),E=s(9730),x=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),b=s(20244),P=s(55041),O=s(44048);var S=P.USER_PROGRESS.CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME,T=P.MIXPANEL_CHECKLIST_STEPS.CHECKLIST_HEADER_CLOSE,k=function Header(r){var u=r.steps,s=r.isMinimized,l=r.toggleIsMinimized,c=(0,E.useQuery)({queryKey:["closedForFirstTime"],queryFn:b.fetchUserProgress}).data,h=(null==c?void 0:c[S])||!1,P=function(){var r=(0,m.default)(p.default.mark(function _callee(){return p.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if((0,b.addMixpanelTrackingChecklistHeader)(T),h){r.next=2;break}return r.next=1,(0,b.updateUserProgress)((0,_.default)({},S,!0));case 1:window.dispatchEvent(new CustomEvent("elementor/checklist/first_close",{detail:{message:"firstClose"}}));case 2:(0,b.toggleChecklistPopup)();case 3:case"end":return r.stop()}},_callee)}));return function closeChecklist(){return r.apply(this,arguments)}}();return x.createElement(x.Fragment,null,x.createElement(y.AppBar,{elevation:0,position:"sticky",sx:{p:2,backgroundColor:"background.default"}},x.createElement(y.Toolbar,{variant:"dense",disableGutters:!0},x.createElement(y.Typography,{variant:"subtitle1",sx:{flexGrow:1}},(0,v.__)("Let's make a productivity boost","elementor")),x.createElement(y.IconButton,{size:"small",onClick:l,"aria-expanded":!s},s?x.createElement(O.ExpandDiagonalIcon,null):x.createElement(O.MinimizeDiagonalIcon,null)),x.createElement(y.CloseButton,{sx:{mr:-.5},size:"small",onClick:P})),x.createElement(g.default,{steps:u})),x.createElement(y.Divider,null))};k.propTypes={steps:h.default.array.isRequired,isMinimized:h.default.bool.isRequired,toggleIsMinimized:h.default.func.isRequired};u.default=k},36417:r=>{r.exports=function _assertThisInitialized(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r},r.exports.__esModule=!0,r.exports.default=r.exports},37744:(r,u,s)=>{var l=s(78113);r.exports=function _unsupportedIterableToArray(r,u){if(r){if("string"==typeof r)return l(r,u);var s={}.toString.call(r).slice(8,-1);return"Object"===s&&r.constructor&&(s=r.constructor.name),"Map"===s||"Set"===s?Array.from(r):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?l(r,u):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},39739:r=>{"use strict";r.exports=elementorV2.editorAppBar},39805:r=>{r.exports=function _classCallCheck(r,u){if(!(r instanceof u))throw new TypeError("Cannot call a class as a function")},r.exports.__esModule=!0,r.exports.default=r.exports},40362:(r,u,s)=>{"use strict";var l=s(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,r.exports=function(){function shim(r,u,s,c,p,_){if(_!==l){var m=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw m.name="Invariant Violation",m}}function getShim(){return shim}shim.isRequired=shim;var r={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return r.PropTypes=r,r}},40989:(r,u,s)=>{var l=s(45498);function _defineProperties(r,u){for(var s=0;s<u.length;s++){var c=u[s];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(r,l(c.key),c)}}r.exports=function _createClass(r,u,s){return u&&_defineProperties(r.prototype,u),s&&_defineProperties(r,s),Object.defineProperty(r,"prototype",{writable:!1}),r},r.exports.__esModule=!0,r.exports.default=r.exports},41594:r=>{"use strict";r.exports=React},44048:r=>{"use strict";r.exports=elementorV2.icons},45498:(r,u,s)=>{var l=s(10564).default,c=s(11327);r.exports=function toPropertyKey(r){var u=c(r,"string");return"symbol"==l(u)?u:u+""},r.exports.__esModule=!0,r.exports.default=r.exports},46313:(r,u,s)=>{var l=s(9535),c=s(33929);r.exports=function _regeneratorAsyncGen(r,u,s,p,_){return new c(l().w(r,u,s,p),_||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},46352:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=l(s(39805)),_=l(s(40989)),m=l(s(15118)),y=l(s(29402)),v=l(s(87861)),g=_interopRequireWildcard(s(33318)),h=_interopRequireWildcard(s(46581));function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m})(r,u)}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}u.default=function(r){function Component(){return(0,p.default)(this,Component),function _callSuper(r,u,s){return u=(0,y.default)(u),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,y.default)(r).constructor):u.apply(r,s))}(this,Component,arguments)}return(0,v.default)(Component,r),(0,_.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"checklist"}},{key:"defaultCommands",value:function defaultCommands(){return this.importCommands(g)}},{key:"defaultData",value:function defaultData(){return this.importCommands(h)}}],[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist"}}])}($e.modules.ComponentBase)},46581:(r,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"Steps",{enumerable:!0,get:function get(){return l.Steps}}),Object.defineProperty(u,"UserProgress",{enumerable:!0,get:function get(){return c.UserProgress}});var l=s(65414),c=s(9892)},50003:(r,u,s)=>{"use strict";var l=s(62688),c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var _=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var c,_,m={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return m;if(c=u?l:s){if(c.has(r))return c.get(r);c.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?c(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),m=c(s(61790)),y=c(s(85707)),v=c(s(58155)),g=c(s(18821)),h=c(s(35843)),E=c(s(19070)),x=s(86956),b=s(55041),P=s(20244);var O=b.USER_PROGRESS.IS_POPUP_MINIMIZED,S=function Checklist(r){var u=(0,_.useState)(r.steps),s=(0,g.default)(u,2),l=s[0],c=s[1],p=(0,_.useState)(!!r.userProgress[O]),b=(0,g.default)(p,2),S=b[0],T=b[1],k=function(){var r=(0,v.default)(m.default.mark(function _callee(){var r;return m.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return r=S,u.prev=1,T(!r),u.next=2,(0,P.updateUserProgress)((0,y.default)({},O,!r));case 2:u.next=4;break;case 3:u.prev=3,u.catch(1),T(r);case 4:case"end":return u.stop()}},_callee,null,[[1,3]])}));return function toggleIsMinimized(){return r.apply(this,arguments)}}();return(0,_.useEffect)(function(){c(r.steps)},[r.steps]),_.default.createElement(x.Paper,{elevation:5,sx:{position:"fixed",width:"360px",bottom:"40px",insetInlineEnd:"40px",zIndex:"99999",hidden:!0,maxHeight:"645px",overflowY:"auto"}},_.default.createElement(h.default,{steps:l,isMinimized:S,toggleIsMinimized:k}),_.default.createElement(E.default,{steps:l,setSteps:c,isMinimized:S}))};S.propTypes={steps:l.array.isRequired,userProgress:l.object.isRequired};u.default=S},50288:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m}(r,u)}(s(41594)),_=l(s(10906)),m=s(86956),y=s(9730),v=s(54142),g=l(s(50003)),h=s(20244);u.default=function App(){var r=elementorCommon.config.isRTL,u=(0,y.useQuery)({queryKey:["steps"],queryFn:h.fetchSteps,gcTime:0,enabled:!1}),s=u.error,l=u.data,c=u.refetch,E=(0,y.useQuery)({queryKey:["statusData"],queryFn:h.fetchUserProgress,gcTime:0,enabled:!1}),x=E.error,b=E.data,P=E.refetch,O=function fetchData(){c(),P()};return(0,p.useEffect)(function(){return O(),(0,v.__privateListenTo)((0,v.commandEndEvent)("document/save/save"),function(r){var u,s=r.args;"kit"===(null==s||null===(u=s.document)||void 0===u||null===(u=u.config)||void 0===u?void 0:u.type)&&O()})},[]),x||!b||s||null==l||!l.length?null:p.default.createElement(m.DirectionProvider,{rtl:r},p.default.createElement(m.ThemeProvider,{colorScheme:"light"},p.default.createElement(g.default,{steps:(0,_.default)(l),userProgress:b})))}},53051:(r,u,s)=>{var l=s(67114),c=s(9535),p=s(62507),_=s(46313),m=s(33929),y=s(95315),v=s(66961);function _regeneratorRuntime(){"use strict";var u=c(),s=u.m(_regeneratorRuntime),g=(Object.getPrototypeOf?Object.getPrototypeOf(s):s.__proto__).constructor;function n(r){var u="function"==typeof r&&r.constructor;return!!u&&(u===g||"GeneratorFunction"===(u.displayName||u.name))}var h={throw:1,return:2,break:3,continue:3};function a(r){var u,s;return function(l){u||(u={stop:function stop(){return s(l.a,2)},catch:function _catch(){return l.v},abrupt:function abrupt(r,u){return s(l.a,h[r],u)},delegateYield:function delegateYield(r,c,p){return u.resultName=c,s(l.d,v(r),p)},finish:function finish(r){return s(l.f,r)}},s=function t(r,s,c){l.p=u.prev,l.n=u.next;try{return r(s,c)}finally{u.next=l.n}}),u.resultName&&(u[u.resultName]=l.v,u.resultName=void 0),u.sent=l.v,u.next=l.n;try{return r.call(this,u)}finally{l.p=u.prev,l.n=u.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,s,l,c){return u.w(a(r),s,l,c&&c.reverse())},isGeneratorFunction:n,mark:u.m,awrap:function awrap(r,u){return new l(r,u)},AsyncIterator:m,async:function async(r,u,s,l,c){return(n(u)?_:p)(a(r),u,s,l,c)},keys:y,values:v}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},54142:r=>{"use strict";r.exports=elementorV2.editorV1Adapters},54871:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=l(s(41594)),p=l(s(61790)),_=l(s(85707)),m=l(s(58155)),y=s(86956),v=s(12470),g=l(s(62688)),h=s(20244),E=s(55041),x=E.STEP.IS_MARKED_COMPLETED,b=E.STEP.IS_ABSOLUTE_COMPLETED,P=E.STEP.IS_IMMUTABLE_COMPLETED,O=E.MIXPANEL_CHECKLIST_STEPS.DONE,S=E.MIXPANEL_CHECKLIST_STEPS.UNDONE,T=E.MIXPANEL_CHECKLIST_STEPS.ACTION,k=E.MIXPANEL_CHECKLIST_STEPS.UPGRADE,C=function ChecklistCardContent(r){var u=r.step,s=r.setSteps,l=u.config,g=l.id,C=l.description,M=l.learn_more_url,R=l.learn_more_text,w=l.image_src,j=l.promotion_data,I=j?(null==j?void 0:j.text)||(0,v.__)("Upgrade Now","elementor"):u.config.cta_text,D=j?j.url:u.config.cta_url,L=u[b],A=u[P],N=u[x],U=!L&&!A&&!j,q=function(){var r=(0,m.default)(p.default.mark(function _callee(){return p.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(j?(0,h.addMixpanelTrackingChecklistSteps)(u.config.id,k):(0,h.addMixpanelTrackingChecklistSteps)(u.config.id,T),elementor&&E.STEP_IDS_TO_COMPLETE_IN_EDITOR.includes(g)&&E.PANEL_ROUTES[g]){r.next=1;break}return r.abrupt("return",window.open(D,"_blank"));case 1:return r.next=2,$e.run("panel/global/open");case 2:$e.route(E.PANEL_ROUTES[g]);case 3:case"end":return r.stop()}},_callee)}));return function redirectHandler(){return r.apply(this,arguments)}}(),W=function(){var r=(0,m.default)(p.default.mark(function _callee2(){var r;return p.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return r=N,N?(0,h.addMixpanelTrackingChecklistSteps)(u.config.id,S):(0,h.addMixpanelTrackingChecklistSteps)(u.config.id,O),s.prev=1,F(x,!r),s.next=2,(0,h.updateStep)(g,(0,_.default)({},x,!r));case 2:s.next=4;break;case 3:s.prev=3,s.catch(1),F(x,r);case 4:case"end":return s.stop()}},_callee2,null,[[1,3]])}));return function toggleMarkAsDone(){return r.apply(this,arguments)}}(),F=function updateStepsState(r,l){s(function(s){return s.map(function(s){return(0,h.getAndUpdateStep)(u.config.id,s,r,l)})})};return c.default.createElement(y.Card,{elevation:0,square:!0,"data-step-id":g},c.default.createElement(y.CardMedia,{image:w,sx:{height:180}}),c.default.createElement(y.CardContent,null,c.default.createElement(y.Typography,{variant:"body2",color:"text.secondary",component:"p"},C+" ",c.default.createElement(y.Link,{href:M,target:"_blank",rel:"noreferrer",underline:"hover",color:"info.main",noWrap:!0},R))),c.default.createElement(y.CardActions,null,U?c.default.createElement(y.Button,{size:"small",color:"secondary",variant:"text",onClick:W},N?(0,v.__)("Unmark as done","elementor"):(0,v.__)("Mark as done","elementor")):null,c.default.createElement(y.Button,{color:j?"promotion":"primary",size:"small",variant:"contained",onClick:q},I)))};u.default=C;C.propTypes={step:g.default.object.isRequired,setSteps:g.default.func.isRequired}},55041:(r,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.USER_PROGRESS_ROUTE=u.USER_PROGRESS=u.STEP_IDS_TO_COMPLETE_IN_EDITOR=u.STEPS_ROUTE=u.STEP=u.PANEL_ROUTES=u.MIXPANEL_CHECKLIST_STEPS=void 0;u.STEPS_ROUTE="checklist/steps",u.USER_PROGRESS_ROUTE="checklist/user-progress",u.STEP={IS_MARKED_COMPLETED:"is_marked_completed",IS_IMMUTABLE_COMPLETED:"is_immutable_completed",IS_ABSOLUTE_COMPLETED:"is_absolute_completed",PROMOTION_DATA:"promotion_data"},u.USER_PROGRESS={LAST_OPENED_TIMESTAMP:"last_opened_timestamp",SHOULD_OPEN_IN_EDITOR:"should_open_in_editor",CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME:"first_closed_checklist_in_editor",IS_POPUP_MINIMIZED:"is_popup_minimized",EDITOR_VISIT_COUNT:"e_editor_counter"},u.STEP_IDS_TO_COMPLETE_IN_EDITOR=["add_logo","set_fonts_and_colors"],u.PANEL_ROUTES={add_logo:"panel/global/settings-site-identity",set_fonts_and_colors:"panel/global/global-typography"},u.MIXPANEL_CHECKLIST_STEPS={UPGRADE:"upgrade",ACTION:"action",DONE:"done",UNDONE:"undone",TITLE:"title",WELL_DONE:"well_done",CHECKLIST_HEADER_CLOSE:"checklistHeaderClose",ACCORDION_SECTION:"accordionSection"}},56441:r=>{"use strict";r.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56741:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=l(s(41594)),p=s(86956),_=l(s(62688)),m=s(20244),y=function Progress(r){var u=r.steps,s=100*u.filter(m.isStepChecked).length/u.length;return c.default.createElement(p.Box,{sx:{display:"flex",alignItems:"center",gap:1}},c.default.createElement(p.Box,{sx:{width:"100%"}},c.default.createElement(p.LinearProgress,{variant:"determinate",value:s})),c.default.createElement(p.Box,{sx:{width:"fit-content"}},c.default.createElement(p.Typography,{variant:"body2",color:"text.secondary"},"".concat(Math.round(s),"%"))))};u.default=y;y.propTypes={steps:_.default.array.isRequired}},58155:r=>{function asyncGeneratorStep(r,u,s,l,c,p,_){try{var m=r[p](_),y=m.value}catch(r){return void s(r)}m.done?u(y):Promise.resolve(y).then(l,c)}r.exports=function _asyncToGenerator(r){return function(){var u=this,s=arguments;return new Promise(function(l,c){var p=r.apply(u,s);function _next(r){asyncGeneratorStep(p,l,c,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(p,l,c,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},61790:(r,u,s)=>{var l=s(53051)();r.exports=l;try{regeneratorRuntime=l}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=l:Function("r","regeneratorRuntime = r")(l)}},62507:(r,u,s)=>{var l=s(46313);r.exports=function _regeneratorAsync(r,u,s,c,p){var _=l(r,u,s,c,p);return _.next().then(function(r){return r.done?r.value:_.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},62688:(r,u,s)=>{r.exports=s(40362)()},65414:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.Steps=void 0;var c=l(s(39805)),p=l(s(40989)),_=l(s(15118)),m=l(s(29402)),y=l(s(87861));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var v=u.Steps=function(r){function Steps(){return(0,c.default)(this,Steps),function _callSuper(r,u,s){return u=(0,m.default)(u),(0,_.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,m.default)(r).constructor):u.apply(r,s))}(this,Steps,arguments)}return(0,y.default)(Steps,r),(0,p.default)(Steps,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist/steps/{id}"}}])}($e.modules.CommandData);u.default=v},65474:r=>{r.exports=function _iterableToArrayLimit(r,u){var s=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=s){var l,c,p,_,m=[],y=!0,v=!1;try{if(p=(s=s.call(r)).next,0===u){if(Object(s)!==s)return;y=!1}else for(;!(y=(l=p.call(s)).done)&&(m.push(l.value),m.length!==u);y=!0);}catch(r){v=!0,c=r}finally{try{if(!y&&null!=s.return&&(_=s.return(),Object(_)!==_))return}finally{if(v)throw c}}return m}},r.exports.__esModule=!0,r.exports.default=r.exports},66961:(r,u,s)=>{var l=s(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var u=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],s=0;if(u)return u.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&s>=r.length&&(r=void 0),{value:r&&r[s++],done:!r}}}}throw new TypeError(l(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,u){this.v=r,this.k=u},r.exports.__esModule=!0,r.exports.default=r.exports},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},73921:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=function useAjax(){var r=(0,y.useState)(null),u=(0,m.default)(r,2),s=u[0],l=u[1],p="initial",v={status:p,isComplete:!1,response:null},g=(0,y.useState)(v),h=(0,m.default)(g,2),E=h[0],x=h[1],b={reset:function reset(){return x(p)}},P=function(){var r=(0,_.default)(c.default.mark(function _callee(r){return c.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",new Promise(function(u,s){var l=new FormData;if(r.data){for(var c in r.data)l.append(c,r.data[c]);r.data.nonce||l.append("_nonce",elementorCommon.config.ajax.nonce)}var p=_objectSpread(_objectSpread({type:"post",url:elementorCommon.config.ajax.url,headers:{},cache:!1,contentType:!1,processData:!1},r),{},{data:l,success:function success(r){u(r)},error:function error(r){s(r)}});jQuery.ajax(p)}));case 1:case"end":return u.stop()}},_callee)}));return function runRequest(u){return r.apply(this,arguments)}}();return(0,y.useEffect)(function(){s&&P(s).then(function(r){var u=r.success?"success":"error";x(function(s){return _objectSpread(_objectSpread({},s),{},{status:u,response:null==r?void 0:r.data})})}).catch(function(r){var u,s=408===r.status?"timeout":null===(u=r.responseJSON)||void 0===u?void 0:u.data;x(function(r){return _objectSpread(_objectSpread({},r),{},{status:"error",response:s})})}).finally(function(){x(function(r){return _objectSpread(_objectSpread({},r),{},{isComplete:!0})})})},[s]),{ajax:s,setAjax:l,ajaxState:E,ajaxActions:b,runRequest:P}};var c=l(s(61790)),p=l(s(85707)),_=l(s(58155)),m=l(s(18821)),y=s(41594);function ownKeys(r,u){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);u&&(l=l.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),s.push.apply(s,l)}return s}function _objectSpread(r){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(r,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(r,u,Object.getOwnPropertyDescriptor(s,u))})}return r}},75206:r=>{"use strict";r.exports=ReactDOM},78113:r=>{r.exports=function _arrayLikeToArray(r,u){(null==u||u>r.length)&&(u=r.length);for(var s=0,l=Array(u);s<u;s++)l[s]=r[s];return l},r.exports.__esModule=!0,r.exports.default=r.exports},78687:r=>{r.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},83725:(r,u,s)=>{"use strict";var l=s(96784),c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.editorAppBarLink=void 0;var p=_interopRequireWildcard(s(39739)),_=s(12470),m=_interopRequireWildcard(s(41594)),y=l(s(25233)),v=s(20244),g=s(9730),h=s(33318);function _interopRequireWildcard(r,u){if("function"==typeof WeakMap)var s=new WeakMap,l=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(r,u){if(!u&&r&&r.__esModule)return r;var p,_,m={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return m;if(p=u?l:s){if(p.has(r))return p.get(r);p.set(r,m)}for(var y in r)"default"!==y&&{}.hasOwnProperty.call(r,y)&&((_=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,y))&&(_.get||_.set)?p(m,y,_):m[y]=r[y]);return m})(r,u)}var E=new g.QueryClient;u.editorAppBarLink=function editorAppBarLink(){p.utilitiesMenu.registerLink({id:"app-bar-menu-item-checklist",priority:5,useProps:function useProps(){return{title:(0,_.__)("Checklist","elementor"),icon:function icon(){return m.createElement(g.QueryClientProvider,{client:E},m.createElement(y.default,null))},onClick:function onClick(){(0,v.addMixpanelTrackingChecklistTopBar)(h.TogglePopup.isOpen),(0,v.toggleChecklistPopup)()}}}})}},85707:(r,u,s)=>{var l=s(45498);r.exports=function _defineProperty(r,u,s){return(u=l(u))in r?Object.defineProperty(r,u,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[u]=s,r},r.exports.__esModule=!0,r.exports.default=r.exports},86956:r=>{"use strict";r.exports=elementorV2.ui},87861:(r,u,s)=>{var l=s(91270);r.exports=function _inherits(r,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(u&&u.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),u&&l(r,u)},r.exports.__esModule=!0,r.exports.default=r.exports},88564:(r,u,s)=>{"use strict";var l=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.ToggleIcon=void 0;var c=l(s(39805)),p=l(s(40989)),_=l(s(15118)),m=l(s(29402)),y=l(s(87861)),v=l(s(85707)),g=l(s(21947)),h=s(20244);function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var E=u.ToggleIcon=function(r){function ToggleIcon(){return(0,c.default)(this,ToggleIcon),function _callSuper(r,u,s){return u=(0,m.default)(u),(0,_.default)(r,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,m.default)(r).constructor):u.apply(r,s))}(this,ToggleIcon,arguments)}return(0,y.default)(ToggleIcon,r),(0,p.default)(ToggleIcon,[{key:"apply",value:function apply(r){document.body.querySelector('[aria-label="Checklist"]').parentElement.style.display=r?"block":"none",!r&&g.default.isOpen&&(0,h.toggleChecklistPopup)()}}])}($e.modules.CommandBase);(0,v.default)(E,"isSettingsOn",!0);u.default=E},89736:r=>{function _regeneratorDefine(u,s,l,c){var p=Object.defineProperty;try{p({},"",{})}catch(u){p=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,u,s,l){if(u)p?p(r,u,{value:s,enumerable:!l,configurable:!l,writable:!l}):r[u]=s;else{var c=function o(u,s){_regeneratorDefine(r,u,function(r){return this._invoke(u,s,r)})};c("next",0),c("throw",1),c("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(u,s,l,c)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},91270:r=>{function _setPrototypeOf(u,s){return r.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,u){return r.__proto__=u,r},r.exports.__esModule=!0,r.exports.default=r.exports,_setPrototypeOf(u,s)}r.exports=_setPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},91819:(r,u,s)=>{var l=s(78113);r.exports=function _arrayWithoutHoles(r){if(Array.isArray(r))return l(r)},r.exports.__esModule=!0,r.exports.default=r.exports},95315:r=>{r.exports=function _regeneratorKeys(r){var u=Object(r),s=[];for(var l in u)s.unshift(l);return function e(){for(;s.length;)if((l=s.pop())in u)return e.value=l,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports}},u={};function __webpack_require__(s){var l=u[s];if(void 0!==l)return l.exports;var c=u[s]={exports:{}};return r[s](c,c.exports,__webpack_require__),c.exports}(()=>{"use strict";var r=__webpack_require__(96784),u=r(__webpack_require__(61790)),s=r(__webpack_require__(58155)),l=__webpack_require__(83725),c=r(__webpack_require__(46352)),p=__webpack_require__(55041),_=__webpack_require__(20244);function checklistStartup(){return _checklistStartup.apply(this,arguments)}function _checklistStartup(){return(_checklistStartup=(0,s.default)(u.default.mark(function _callee(){var r;return u.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!("yes"!==elementor.getPreferences("show_launchpad_checklist"))){u.next=1;break}$e.commands.run("checklist/toggle-icon",!1),u.next=3;break;case 1:return u.next=2,(0,_.fetchUserProgress)();case 2:null!=(r=u.sent)&&r[p.USER_PROGRESS.SHOULD_OPEN_IN_EDITOR]&&((0,_.toggleChecklistPopup)(),(0,_.dispatchChecklistOpenEvent)());case 3:elementor.off("document:loaded",checklistStartup);case 4:case"end":return u.stop()}},_callee)}))).apply(this,arguments)}$e.components.register(new c.default),(0,l.editorAppBarLink)(),elementorCommon.elements.$window.on("elementor:loaded",function elementorLoaded(){elementor.on("document:loaded",checklistStartup),elementorCommon.elements.$window.off("elementor:loaded",elementorLoaded)})})()})();