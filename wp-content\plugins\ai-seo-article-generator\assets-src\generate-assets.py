#!/usr/bin/env python3
"""
Generate WordPress.org plugin assets for AI SEO Article Generator
Requires: pip install pillow
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import os

# Create output directory
output_dir = "../svn-repo/assets"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Color scheme
GRADIENT_START = (102, 126, 234)  # #667eea
GRADIENT_END = (118, 75, 162)     # #764ba2
WHITE = (255, 255, 255)
WHITE_TRANSLUCENT = (255, 255, 255, 230)
WHITE_SUBTLE = (255, 255, 255, 51)  # 20% opacity

def create_gradient(width, height, start_color, end_color, direction='diagonal'):
    """Create a gradient image"""
    base = Image.new('RGB', (width, height), start_color)
    top = Image.new('RGB', (width, height), end_color)
    mask = Image.new('L', (width, height))
    mask_draw = ImageDraw.Draw(mask)
    
    if direction == 'diagonal':
        for i in range(width + height):
            mask_draw.line([(0, i), (i, 0)], fill=int(255 * i / (width + height)), width=2)
    elif direction == 'horizontal':
        for i in range(width):
            mask_draw.line([(i, 0), (i, height)], fill=int(255 * i / width), width=1)
    
    base.paste(top, (0, 0), mask)
    return base

def draw_rounded_rectangle(draw, coords, radius, fill):
    """Draw a rounded rectangle"""
    x1, y1, x2, y2 = coords
    draw.rectangle([x1 + radius, y1, x2 - radius, y2], fill=fill)
    draw.rectangle([x1, y1 + radius, x2, y2 - radius], fill=fill)
    draw.pieslice([x1, y1, x1 + 2*radius, y1 + 2*radius], 180, 270, fill=fill)
    draw.pieslice([x2 - 2*radius, y1, x2, y1 + 2*radius], 270, 360, fill=fill)
    draw.pieslice([x1, y2 - 2*radius, x1 + 2*radius, y2], 90, 180, fill=fill)
    draw.pieslice([x2 - 2*radius, y2 - 2*radius, x2, y2], 0, 90, fill=fill)

def create_icon(size):
    """Create plugin icon"""
    # Create gradient background
    img = create_gradient(size, size, GRADIENT_START, GRADIENT_END)
    draw = ImageDraw.Draw(img)
    
    # Try to use a good font, fallback to default if not available
    try:
        # Adjust font size for different icon sizes
        font_size = int(size * 0.35)
        arrow_size = int(size * 0.02)
        font = ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # Add rounded corners mask
    mask = Image.new('L', (size, size), 0)
    mask_draw = ImageDraw.Draw(mask)
    draw_rounded_rectangle(mask_draw, [0, 0, size, size], int(size * 0.125), 255)
    
    # Apply mask
    output = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    output.paste(img, (0, 0))
    output.putalpha(mask)
    
    # Create new image with background
    final = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    gradient = create_gradient(size, size, GRADIENT_START, GRADIENT_END)
    
    # Draw rounded rectangle background
    draw = ImageDraw.Draw(final)
    draw_rounded_rectangle(draw, [0, 0, size-1, size-1], int(size * 0.125), GRADIENT_START)
    
    # Paste gradient with rounded corners
    rounded_gradient = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    rounded_gradient.paste(gradient, (0, 0), mask)
    final = Image.alpha_composite(final, rounded_gradient)
    
    draw = ImageDraw.Draw(final)
    
    # Draw "AI" text
    text = "AI"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - int(size * 0.05)
    draw.text((x, y), text, fill=WHITE, font=font)
    
    # Draw SEO arrow pointing up
    arrow_x = size // 2
    arrow_y = int(size * 0.68)
    arrow_size = int(size * 0.08)
    arrow_width = max(2, int(size * 0.02))
    
    # Arrow head
    draw.line([arrow_x - arrow_size, arrow_y + arrow_size//2, 
               arrow_x, arrow_y - arrow_size//2], fill=WHITE, width=arrow_width)
    draw.line([arrow_x + arrow_size, arrow_y + arrow_size//2, 
               arrow_x, arrow_y - arrow_size//2], fill=WHITE, width=arrow_width)
    
    # Arrow stem
    draw.line([arrow_x, arrow_y - arrow_size//2, 
               arrow_x, arrow_y + arrow_size], fill=WHITE, width=arrow_width)
    
    # Add subtle circuit dots
    dot_positions = [
        (int(size * 0.15), int(size * 0.15)),
        (int(size * 0.85), int(size * 0.15)),
        (int(size * 0.15), int(size * 0.85)),
        (int(size * 0.85), int(size * 0.85)),
    ]
    
    dot_radius = max(2, int(size * 0.015))
    for x, y in dot_positions:
        draw.ellipse([x - dot_radius, y - dot_radius, 
                      x + dot_radius, y + dot_radius], 
                     fill=WHITE_SUBTLE)
    
    return final

def create_banner(width, height):
    """Create plugin banner"""
    # Create gradient background with variation
    img = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(img)
    
    # Three-color gradient effect
    for i in range(height):
        # Calculate color based on position
        ratio = i / height
        if ratio < 0.5:
            # Blend from start to mid
            r = int(GRADIENT_START[0] + (GRADIENT_END[0] - GRADIENT_START[0]) * ratio * 2)
            g = int(GRADIENT_START[1] + (GRADIENT_END[1] - GRADIENT_START[1]) * ratio * 2)
            b = int(GRADIENT_START[2] + (GRADIENT_END[2] - GRADIENT_START[2]) * ratio * 2)
        else:
            # Blend from mid to start
            r = int(GRADIENT_END[0] + (GRADIENT_START[0] - GRADIENT_END[0]) * (ratio - 0.5) * 2)
            g = int(GRADIENT_END[1] + (GRADIENT_START[1] - GRADIENT_END[1]) * (ratio - 0.5) * 2)
            b = int(GRADIENT_END[2] + (GRADIENT_START[2] - GRADIENT_END[2]) * (ratio - 0.5) * 2)
        
        draw.rectangle([(0, i), (width, i + 1)], fill=(r, g, b))
    
    draw = ImageDraw.Draw(img)
    
    # Add subtle pattern overlay (dots)
    for x in range(0, width, int(100 * (width / 772))):
        for y in range(0, height, int(100 * (width / 772))):
            draw.ellipse([x-2, y-2, x+2, y+2], fill=WHITE_SUBTLE)
    
    # Try to load fonts
    try:
        title_size = int(42 * (width / 772))
        subtitle_size = int(20 * (width / 772))
        feature_size = int(16 * (width / 772))
        
        title_font = ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", title_size)
        subtitle_font = ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf", subtitle_size)
        feature_font = ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf", feature_size)
    except:
        title_font = subtitle_font = feature_font = ImageFont.load_default()
    
    # Main title
    title = "AI SEO Article Generator"
    bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = bbox[2] - bbox[0]
    draw.text(((width - title_width) // 2, int(height * 0.25)), 
              title, fill=WHITE, font=title_font)
    
    # Subtitle
    subtitle = "Powered by Claude & OpenAI"
    bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = bbox[2] - bbox[0]
    draw.text(((width - subtitle_width) // 2, int(height * 0.42)), 
              subtitle, fill=WHITE_TRANSLUCENT, font=subtitle_font)
    
    # Feature badges
    features = [
        "⚡ Generate in Seconds",
        "🌐 Hebrew & English",
        "🔄 Background Processing"
    ]
    
    # Calculate total width of features
    feature_spacing = int(140 * (width / 772))
    start_x = (width - (len(features) - 1) * feature_spacing) // 2
    
    for i, feature in enumerate(features):
        bbox = draw.textbbox((0, 0), feature, font=feature_font)
        feature_width = bbox[2] - bbox[0]
        x = start_x + i * feature_spacing - feature_width // 2
        draw.text((x, int(height * 0.65)), feature, fill=WHITE, font=feature_font)
    
    # Circuit decorations
    line_width = max(2, int(2 * (width / 772)))
    decoration_size = int(50 * (width / 772))
    
    # Left decoration
    draw.line([(decoration_size, height // 2), 
               (decoration_size * 2, height // 2)], 
              fill=WHITE_SUBTLE, width=line_width)
    draw.line([(decoration_size * 1.5, height // 2 - decoration_size // 2), 
               (decoration_size * 1.5, height // 2 + decoration_size // 2)], 
              fill=WHITE_SUBTLE, width=line_width)
    
    # Right decoration
    draw.line([(width - decoration_size, height // 2), 
               (width - decoration_size * 2, height // 2)], 
              fill=WHITE_SUBTLE, width=line_width)
    draw.line([(width - decoration_size * 1.5, height // 2 - decoration_size // 2), 
               (width - decoration_size * 1.5, height // 2 + decoration_size // 2)], 
              fill=WHITE_SUBTLE, width=line_width)
    
    return img

# Generate all assets
print("Generating WordPress.org plugin assets...")

# Icons
icon_256 = create_icon(256)
icon_256.save(os.path.join(output_dir, "icon-256x256.png"), "PNG")
print("✓ Created icon-256x256.png")

icon_128 = create_icon(128)
icon_128.save(os.path.join(output_dir, "icon-128x128.png"), "PNG")
print("✓ Created icon-128x128.png")

# Banners
banner_standard = create_banner(772, 250)
banner_standard.save(os.path.join(output_dir, "banner-772x250.png"), "PNG")
print("✓ Created banner-772x250.png")

banner_retina = create_banner(1544, 500)
banner_retina.save(os.path.join(output_dir, "banner-1544x500.png"), "PNG")
print("✓ Created banner-1544x500.png")

print("\nAll assets generated successfully!")
print(f"Location: {os.path.abspath(output_dir)}")
print("\nNext steps:")
print("1. Review the generated assets")
print("2. Run: cd svn-repo && svn add assets/*")
print("3. Run: svn ci -m 'Add plugin assets' --username ytrofr --password [your-password]")