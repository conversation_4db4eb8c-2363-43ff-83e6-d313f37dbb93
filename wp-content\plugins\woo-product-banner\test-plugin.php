<?php
/**
 * Test file for WooCommerce Product Banner Plugin
 * This file can be used to test plugin functionality
 * Remove this file in production
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Plugin Structure
 */
function wpb_test_plugin_structure() {
    $results = array();
    
    // Test if main plugin file exists
    $main_file = WPB_PLUGIN_PATH . 'woo-product-banner.php';
    $results['main_file'] = file_exists($main_file) ? 'PASS' : 'FAIL';
    
    // Test if required classes exist
    $required_files = array(
        'class-admin.php',
        'class-frontend.php',
        'class-database.php',
        'class-security.php'
    );
    
    foreach ($required_files as $file) {
        $file_path = WPB_PLUGIN_PATH . 'includes/' . $file;
        $results[$file] = file_exists($file_path) ? 'PASS' : 'FAIL';
    }
    
    // Test if assets exist
    $asset_files = array(
        'assets/css/frontend.css',
        'assets/css/admin.css',
        'assets/js/admin.js'
    );
    
    foreach ($asset_files as $file) {
        $file_path = WPB_PLUGIN_PATH . $file;
        $results[$file] = file_exists($file_path) ? 'PASS' : 'FAIL';
    }
    
    return $results;
}

/**
 * Test Plugin Constants
 */
function wpb_test_plugin_constants() {
    $results = array();
    
    $required_constants = array(
        'WPB_PLUGIN_URL',
        'WPB_PLUGIN_PATH',
        'WPB_PLUGIN_VERSION',
        'WPB_PLUGIN_BASENAME'
    );
    
    foreach ($required_constants as $constant) {
        $results[$constant] = defined($constant) ? 'PASS' : 'FAIL';
    }
    
    return $results;
}

/**
 * Test WooCommerce Integration
 */
function wpb_test_woocommerce_integration() {
    $results = array();
    
    // Test if WooCommerce is active
    $results['woocommerce_active'] = class_exists('WooCommerce') ? 'PASS' : 'FAIL';
    
    // Test if WooCommerce hooks are available
    $results['woocommerce_hooks'] = has_action('woocommerce_single_product_summary') ? 'PASS' : 'FAIL';
    
    return $results;
}

/**
 * Test Database Functions
 */
function wpb_test_database_functions() {
    $results = array();

    if (class_exists('WPB_Database')) {
        $db = new WPB_Database();

        // Test basic database operations
        $test_image_id = 123; // Dummy ID for testing

        // Test setting banner image (will fail validation, but tests the method)
        $results['set_banner_method'] = method_exists($db, 'set_banner_image') ? 'PASS' : 'FAIL';

        // Test getting banner image
        $results['get_banner_method'] = method_exists($db, 'get_banner_image') ? 'PASS' : 'FAIL';

        // Test validation method
        $results['validate_method'] = method_exists($db, 'validate_banner_image') ? 'PASS' : 'FAIL';

        // Test settings methods
        $results['get_settings_method'] = method_exists($db, 'get_settings') ? 'PASS' : 'FAIL';

        // Test actual database operations
        $current_banner = $db->get_banner_image();
        $results['get_current_banner'] = ($current_banner !== null) ? 'PASS' : 'FAIL';

        // Test option storage
        $test_option = get_option('wpb_banner_image', 'not_found');
        $results['option_storage'] = ($test_option !== 'not_found') ? 'PASS' : 'FAIL - No option found';

    } else {
        $results['database_class'] = 'FAIL - Class not found';
    }

    return $results;
}

/**
 * Test Italian Localization
 */
function wpb_test_italian_localization() {
    $results = array();

    // Check if translation files exist
    $po_file = WPB_PLUGIN_PATH . 'languages/woo-product-banner-it_IT.po';
    $mo_file = WPB_PLUGIN_PATH . 'languages/woo-product-banner-it_IT.mo';

    $results['po_file_exists'] = file_exists($po_file) ? 'PASS' : 'FAIL';
    $results['mo_file_exists'] = file_exists($mo_file) ? 'PASS' : 'FAIL';

    // Test if text domain is loaded
    $results['textdomain_loaded'] = is_textdomain_loaded('woo-product-banner') ? 'PASS' : 'FAIL';

    // Test some translations (temporarily switch locale)
    $original_locale = get_locale();

    // Load Italian translations
    if (file_exists($mo_file)) {
        load_textdomain('woo-product-banner', $mo_file);

        // Test key translations
        $banner_prodotti = __('Banner Prodotti', 'woo-product-banner');
        $results['banner_prodotti_translation'] = ($banner_prodotti === 'Banner Prodotti') ? 'PASS' : 'FAIL';

        $select_image = __('Select Image', 'woo-product-banner');
        $results['select_image_translation'] = ($select_image === 'Seleziona Immagine') ? 'PASS' : 'FAIL';

        $save_banner = __('Save Banner', 'woo-product-banner');
        $results['save_banner_translation'] = ($save_banner === 'Salva Banner') ? 'PASS' : 'FAIL';
    } else {
        $results['translation_test'] = 'FAIL - MO file not found';
    }

    return $results;
}

/**
 * Test Security Features
 */
function wpb_test_security_features() {
    $results = array();
    
    if (class_exists('WPB_Security')) {
        // Test security methods
        $results['generate_nonce'] = method_exists('WPB_Security', 'generate_nonce') ? 'PASS' : 'FAIL';
        $results['verify_nonce'] = method_exists('WPB_Security', 'verify_nonce') ? 'PASS' : 'FAIL';
        $results['check_permissions'] = method_exists('WPB_Security', 'check_permissions') ? 'PASS' : 'FAIL';
        $results['sanitize_filename'] = method_exists('WPB_Security', 'sanitize_filename') ? 'PASS' : 'FAIL';
    } else {
        $results['security_class'] = 'FAIL - Class not found';
    }
    
    return $results;
}

/**
 * Run all tests
 */
function wpb_run_all_tests() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo '<div class="wrap">';
    echo '<h1>WooCommerce Product Banner - Plugin Tests</h1>';
    
    // Test plugin structure
    echo '<h2>Plugin Structure Tests</h2>';
    $structure_tests = wpb_test_plugin_structure();
    wpb_display_test_results($structure_tests);
    
    // Test constants
    echo '<h2>Plugin Constants Tests</h2>';
    $constants_tests = wpb_test_plugin_constants();
    wpb_display_test_results($constants_tests);
    
    // Test WooCommerce integration
    echo '<h2>WooCommerce Integration Tests</h2>';
    $woocommerce_tests = wpb_test_woocommerce_integration();
    wpb_display_test_results($woocommerce_tests);
    
    // Test database functions
    echo '<h2>Database Functions Tests</h2>';
    $database_tests = wpb_test_database_functions();
    wpb_display_test_results($database_tests);
    
    // Test security features
    echo '<h2>Security Features Tests</h2>';
    $security_tests = wpb_test_security_features();
    wpb_display_test_results($security_tests);

    // Test Italian localization
    echo '<h2>Italian Localization Tests</h2>';
    $localization_tests = wpb_test_italian_localization();
    wpb_display_test_results($localization_tests);

    echo '</div>';
}

/**
 * Display test results
 */
function wpb_display_test_results($results) {
    echo '<table class="widefat">';
    echo '<thead><tr><th>Test</th><th>Result</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($results as $test => $result) {
        $class = $result === 'PASS' ? 'success' : 'error';
        echo '<tr>';
        echo '<td>' . esc_html($test) . '</td>';
        echo '<td><span class="' . $class . '">' . esc_html($result) . '</span></td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '<br>';
}

// Add admin page for testing (only if user is admin)
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'WPB Plugin Tests',
            'WPB Plugin Tests',
            'manage_options',
            'wpb-plugin-tests',
            'wpb_run_all_tests'
        );
    });
}

// Add styles for test results
add_action('admin_head', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'wpb-plugin-tests') {
        echo '<style>
            .success { color: green; font-weight: bold; }
            .error { color: red; font-weight: bold; }
            .widefat th, .widefat td { padding: 10px; }
        </style>';
    }
});
