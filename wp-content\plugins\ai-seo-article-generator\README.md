# AI SEO Article Generator

A WordPress plugin that leverages AI (Claude API or OpenAI) to generate high-quality, SEO-optimized articles in Hebrew and English.

## Features

### Core Features
- **AI-Powered Content Generation**: Uses Claude 4 or OpenAI GPT-4 for high-quality content
- **Multi-Language Support**: Full support for Hebrew (RTL) and English content
- **SEO Optimization**: Built-in SEO features including meta descriptions, keyword density analysis, and schema markup
- **Background Processing**: Generate long articles without timeout issues
- **Saved Structures Library**: Save and reuse article structures for consistent content

### Content Features
- **Smart Article Structure**: AI-generated outlines with H2/H3 hierarchy
- **Table of Contents**: Automatic generation with smooth scrolling
- **Readability Analysis**: Flesch reading score for content optimization
- **Keyword Integration**: Natural keyword placement throughout content
- **Word Count Control**: Specify target length (500-10,000 words)

### Technical Features
- **WordPress Standards Compliant**: Follows all WordPress coding standards
- **Security**: Nonce verification, data sanitization, and prepared statements
- **Performance**: Optimized database queries with custom tables
- **Extensibility**: Hooks and filters for customization
- **Debug Mode**: Optional debug logging for troubleshooting

## Requirements

- WordPress 5.0 or higher
- PHP 7.2 or higher
- MySQL 5.6 or higher
- Claude API key or OpenAI API key

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to AI SEO Generator > Settings
4. Enter your API key (Claude or OpenAI)
5. Configure your preferences

## Usage

### Quick Article Generation
1. Go to AI SEO Generator > New Article
2. Enter your main keyword
3. Add sub-keywords (optional)
4. Set target word count
5. Click "Generate Structure"
6. Review and click "Generate Content"

### Background Generation
For articles over 2000 words:
1. Enable background processing in settings
2. Generate as usual - you'll receive an email when complete
3. Check AI SEO Generator > Background Jobs for status

### Saved Structures
1. After generating a structure, click "Save Structure"
2. Access saved structures from AI SEO Generator > Structures Library
3. Load and reuse for consistent article formats

## API Configuration

### Claude API
1. Get your API key from [Anthropic Console](https://console.anthropic.com/)
2. Select "Claude" as AI Provider in settings
3. Enter your Claude API key

### OpenAI API
1. Get your API key from [OpenAI Platform](https://platform.openai.com/)
2. Select "OpenAI" as AI Provider in settings
3. Enter your OpenAI API key
4. Choose your preferred model (GPT-4 recommended)

## Database Tables

The plugin creates three custom tables:
- `{prefix}_ai_seo_article_generator_drafts` - Article drafts and content
- `{prefix}_ai_seo_article_generator_structures` - Article structures
- `{prefix}_ai_seo_article_generator_saved_structures` - Saved structure templates

## Hooks and Filters

### Actions
- `ai_seo_article_generator_before_generate` - Before article generation
- `ai_seo_article_generator_after_generate` - After article generation
- `ai_seo_article_generator_structure_saved` - When structure is saved

### Filters
- `ai_seo_article_generator_prompt` - Modify AI prompts
- `ai_seo_article_generator_content` - Filter generated content
- `ai_seo_article_generator_timeout` - Adjust API timeouts

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your API key is correctly entered and has sufficient credits
2. **Timeout Errors**: Enable background processing for long articles
3. **Hebrew Display Issues**: Ensure your WordPress site supports UTF-8
4. **Memory Errors**: Increase PHP memory limit in wp-config.php

### Debug Mode
Enable debug logging in settings to track issues:
- Logs are stored in `wp-content/debug.log`
- Only enable when troubleshooting
- Disable in production for performance

## Changelog

See [readme.txt](readme.txt) for version history.

## Support

For support, feature requests, or bug reports, please use the WordPress.org support forum.

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by Yotam Rozin (ytrofr) and Sigma