/*! elementor - v3.31.0 - 11-08-2025 */
(()=>{"use strict";var e={12470:e=>{e.exports=wp.i18n}},t={};var a,i,n=function __webpack_require__(a){var i=t[a];if(void 0!==i)return i.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,__webpack_require__),n.exports}(12470).__;a=jQuery,i={cacheElements:function cacheElements(){this.cache={$deactivateLink:a("#the-list").find('[data-slug="elementor"] span.deactivate a'),$dialogHeader:a("#elementor-deactivate-feedback-dialog-header"),$dialogForm:a("#elementor-deactivate-feedback-dialog-form")}},bindEvents:function bindEvents(){var e=this;e.cache.$deactivateLink.on("click",function(t){t.preventDefault(),e.getModal().show()})},deactivate:function deactivate(){location.href=this.cache.$deactivateLink.attr("href")},initModal:function initModal(){var e,t=this;t.getModal=function(){return e||(e=elementorCommon.dialogsManager.createWidget("lightbox",{id:"elementor-deactivate-feedback-modal",headerMessage:t.cache.$dialogHeader,message:t.cache.$dialogForm,hide:{onButtonClick:!1},position:{my:"center",at:"center"},onReady:function onReady(){DialogsManager.getWidgetType("lightbox").prototype.onReady.apply(this,arguments),this.addButton({name:"submit",text:n("Submit & Deactivate","elementor"),callback:t.sendFeedback.bind(t)}),this.addButton({name:"skip",text:n("Skip & Deactivate","elementor"),callback:function callback(){t.deactivate()}})},onShow:function onShow(){var e=a("#elementor-deactivate-feedback-modal"),t=".elementor-deactivate-feedback-dialog-input";e.find(t).on("change",function(){e.attr("data-feedback-selected",a(this).val())}),e.find(t+":checked").trigger("change")}})),e}},sendFeedback:function sendFeedback(){var e=this.cache.$dialogForm.serialize();this.getModal().getElements("submit").text("").addClass("elementor-loading"),a.post(ajaxurl,e,this.deactivate.bind(this))},init:function init(){this.initModal(),this.cacheElements(),this.bindEvents()}},a(function(){i.init()})})();