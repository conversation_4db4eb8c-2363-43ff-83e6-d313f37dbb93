<?php
/**
 * Debug script to test the database storage and Italian localization fixes
 * This file can be accessed via admin to test the fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Database Storage Fix
 */
function wpb_test_database_storage_fix() {
    echo '<h3>Database Storage Fix Test</h3>';
    
    // Test if database class is properly instantiated
    $plugin_instance = WooCommerce_Product_Banner::get_instance();
    
    if ($plugin_instance && isset($plugin_instance->database)) {
        echo '<p style="color: green;">✓ Database class is properly instantiated</p>';
        
        $db = $plugin_instance->database;
        
        // Test getting current banner
        $current_banner = $db->get_banner_image();
        echo '<p>Current banner ID: ' . ($current_banner ? $current_banner : 'None') . '</p>';
        
        // Test validation method
        if (method_exists($db, 'validate_banner_image')) {
            echo '<p style="color: green;">✓ Validation method exists</p>';
        } else {
            echo '<p style="color: red;">✗ Validation method missing</p>';
        }
        
        // Test set/get methods
        if (method_exists($db, 'set_banner_image') && method_exists($db, 'get_banner_image')) {
            echo '<p style="color: green;">✓ Set/Get methods exist</p>';
        } else {
            echo '<p style="color: red;">✗ Set/Get methods missing</p>';
        }
        
    } else {
        echo '<p style="color: red;">✗ Database class not properly instantiated</p>';
    }
    
    // Test admin class integration
    if ($plugin_instance && isset($plugin_instance->admin)) {
        echo '<p style="color: green;">✓ Admin class is properly instantiated</p>';
        
        // Check if admin class has database reference
        $admin_reflection = new ReflectionClass($plugin_instance->admin);
        if ($admin_reflection->hasProperty('database')) {
            echo '<p style="color: green;">✓ Admin class has database property</p>';
        } else {
            echo '<p style="color: red;">✗ Admin class missing database property</p>';
        }
    } else {
        echo '<p style="color: red;">✗ Admin class not properly instantiated</p>';
    }
    
    // Test WordPress option storage
    $option_value = get_option('wpb_banner_image', 'not_found');
    if ($option_value !== 'not_found') {
        echo '<p style="color: green;">✓ WordPress option storage working (Value: ' . $option_value . ')</p>';
    } else {
        echo '<p style="color: orange;">⚠ No banner image currently stored</p>';
    }
}

/**
 * Test Italian Localization Fix
 */
function wpb_test_italian_localization_fix() {
    echo '<h3>Italian Localization Fix Test</h3>';
    
    // Check translation files
    $po_file = WPB_PLUGIN_PATH . 'languages/woo-product-banner-it_IT.po';
    $mo_file = WPB_PLUGIN_PATH . 'languages/woo-product-banner-it_IT.mo';
    
    if (file_exists($po_file)) {
        echo '<p style="color: green;">✓ Italian PO file exists</p>';
    } else {
        echo '<p style="color: red;">✗ Italian PO file missing</p>';
    }
    
    if (file_exists($mo_file)) {
        echo '<p style="color: green;">✓ Italian MO file exists</p>';
        echo '<p>MO file size: ' . filesize($mo_file) . ' bytes</p>';
    } else {
        echo '<p style="color: red;">✗ Italian MO file missing</p>';
    }
    
    // Test text domain loading
    $textdomain_loaded = is_textdomain_loaded('woo-product-banner');
    if ($textdomain_loaded) {
        echo '<p style="color: green;">✓ Text domain is loaded</p>';
    } else {
        echo '<p style="color: orange;">⚠ Text domain not loaded (may load on demand)</p>';
    }
    
    // Test translations by loading the MO file
    if (file_exists($mo_file)) {
        // Load the Italian translation
        $loaded = load_textdomain('woo-product-banner', $mo_file);
        
        if ($loaded) {
            echo '<p style="color: green;">✓ Italian translations loaded successfully</p>';
            
            // Test key translations
            $translations_test = array(
                'Banner Prodotti' => __('Banner Prodotti', 'woo-product-banner'),
                'Select Image' => __('Select Image', 'woo-product-banner'),
                'Save Banner' => __('Save Banner', 'woo-product-banner'),
                'Remove Banner' => __('Remove Banner', 'woo-product-banner'),
                'Banner saved successfully!' => __('Banner saved successfully!', 'woo-product-banner'),
            );
            
            echo '<h4>Translation Tests:</h4>';
            echo '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
            echo '<tr><th>English</th><th>Italian</th><th>Status</th></tr>';
            
            foreach ($translations_test as $english => $italian) {
                $status = ($italian !== $english) ? '✓ Translated' : '⚠ Not translated';
                $color = ($italian !== $english) ? 'green' : 'orange';
                echo '<tr>';
                echo '<td>' . esc_html($english) . '</td>';
                echo '<td>' . esc_html($italian) . '</td>';
                echo '<td style="color: ' . $color . ';">' . $status . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
        } else {
            echo '<p style="color: red;">✗ Failed to load Italian translations</p>';
        }
    }
}

/**
 * Test Form Submission Simulation
 */
function wpb_test_form_submission() {
    echo '<h3>Form Submission Test</h3>';
    
    // Check if we can simulate a form submission
    if (class_exists('WPB_Admin')) {
        echo '<p style="color: green;">✓ Admin class available for testing</p>';
        
        // Check if the admin_init hook is properly registered
        $admin_init_callbacks = $GLOBALS['wp_filter']['admin_init'] ?? null;
        if ($admin_init_callbacks) {
            $found_callback = false;
            foreach ($admin_init_callbacks->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'WPB_Admin' &&
                        $callback['function'][1] === 'admin_init') {
                        $found_callback = true;
                        break 2;
                    }
                }
            }
            
            if ($found_callback) {
                echo '<p style="color: green;">✓ Admin init hook is properly registered</p>';
            } else {
                echo '<p style="color: red;">✗ Admin init hook not found</p>';
            }
        } else {
            echo '<p style="color: red;">✗ No admin_init hooks registered</p>';
        }
        
    } else {
        echo '<p style="color: red;">✗ Admin class not available</p>';
    }
}

/**
 * Display debug page
 */
function wpb_display_debug_page() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo '<div class="wrap">';
    echo '<h1>WooCommerce Product Banner - Debug Fixes</h1>';
    echo '<p>This page tests the database storage and Italian localization fixes.</p>';
    
    wpb_test_database_storage_fix();
    echo '<hr>';
    
    wpb_test_italian_localization_fix();
    echo '<hr>';
    
    wpb_test_form_submission();
    
    echo '<hr>';
    echo '<h3>Quick Actions</h3>';
    echo '<p><a href="' . admin_url('edit.php?post_type=product&page=woo-product-banner') . '" class="button button-primary">Go to Banner Settings</a></p>';
    echo '<p><a href="' . admin_url('tools.php?page=wpb-plugin-tests') . '" class="button">Run Full Plugin Tests</a></p>';
    
    echo '</div>';
}

// Add debug page to admin menu (only for administrators)
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'WPB Debug Fixes',
            'WPB Debug Fixes',
            'manage_options',
            'wpb-debug-fixes',
            'wpb_display_debug_page'
        );
    });
}
