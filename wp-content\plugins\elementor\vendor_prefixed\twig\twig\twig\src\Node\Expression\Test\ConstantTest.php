<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Node\Expression\Test;

use ElementorDeps\Twig\Compiler;
use ElementorDeps\Twig\Node\Expression\TestExpression;
/**
 * Checks if a variable is the exact same value as a constant.
 *
 *    {% if post.status is constant('Post::PUBLISHED') %}
 *      the status attribute is exactly the same as Post::PUBLISHED
 *    {% endif %}
 *
 * <AUTHOR> Potencier <<EMAIL>>
 */
class ConstantTest extends TestExpression
{
    public function compile(Compiler $compiler) : void
    {
        $compiler->raw('(')->subcompile($this->getNode('node'))->raw(' === constant(');
        if ($this->getNode('arguments')->hasNode('1')) {
            $compiler->raw('get_class(')->subcompile($this->getNode('arguments')->getNode('1'))->raw(')."::".');
        }
        $compiler->subcompile($this->getNode('arguments')->getNode('0'))->raw('))');
    }
}
