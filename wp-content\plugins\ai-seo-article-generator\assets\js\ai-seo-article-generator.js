jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize Table of Contents functionality
    initTableOfContents();
    
    // Initialize smooth scrolling for TOC links
    initSmoothScrolling();
    
    // Initialize content enhancements
    initContentEnhancements();
    
    function initTableOfContents() {
        const $toc = $('.ai-seo-article-generator-toc');
        if ($toc.length === 0) return;
        
        // Add scroll spy functionality
        const $tocLinks = $toc.find('a[href^="#"]');
        const $headings = $tocLinks.map(function() {
            const href = $(this).attr('href');
            return $(href).length ? $(href) : null;
        }).get();
        
        if ($headings.length === 0) return;
        
        // Highlight current section in TOC
        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const windowHeight = $(window).height();
            let currentSection = null;
            
            $headings.forEach(function($heading) {
                const headingTop = $heading.offset().top;
                const headingBottom = headingTop + $heading.outerHeight();
                
                if (scrollTop >= headingTop - 100 && scrollTop < headingBottom) {
                    currentSection = $heading.attr('id');
                }
            });
            
            // Update TOC highlighting
            $tocLinks.removeClass('active');
            if (currentSection) {
                $toc.find('a[href="#' + currentSection + '"]').addClass('active');
            }
        });
    }
    
    function initSmoothScrolling() {
        $(document).on('click', '.ai-seo-article-generator-toc a[href^="#"]', function(e) {
            e.preventDefault();
            
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 500);
            }
        });
    }
    
    function initContentEnhancements() {
        // Add reading time estimation
        addReadingTime();
        
        // Add copy functionality to code blocks
        addCopyButtons();
        
        // Lazy load images
        lazyLoadImages();
    }
    
    function addReadingTime() {
        const $content = $('.ai-seo-article-generator-content');
        if ($content.length === 0) return;
        
        const text = $content.text();
        const wordsPerMinute = 200; // Average reading speed in Hebrew
        const wordCount = text.split(/\s+/).length;
        const readingTime = Math.ceil(wordCount / wordsPerMinute);
        
        const readingTimeHtml = '<div class="ai-seo-article-generator-reading-time">' +
                               '<span class="reading-time-icon">📖</span>' +
                               'Estimated reading time: ' + readingTime + ' minutes' +
                               '</div>';
        
        $content.prepend(readingTimeHtml);
    }
    
    function addCopyButtons() {
        $('pre code').each(function() {
            const $code = $(this);
            const $pre = $code.parent();
            
            const $copyButton = $('<button class="copy-code-btn">Copy</button>');
            $pre.css('position', 'relative').append($copyButton);
            
            $copyButton.on('click', function() {
                const text = $code.text();
                
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        $copyButton.text('Copied!').addClass('copied');
                        setTimeout(function() {
                            $copyButton.text('Copy').removeClass('copied');
                        }, 2000);
                    });
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    $copyButton.text('Copied!').addClass('copied');
                    setTimeout(function() {
                        $copyButton.text('Copy').removeClass('copied');
                    }, 2000);
                }
            });
        });
    }
    
    function lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(function(img) {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(function(img) {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    }
    
    // Print functionality
    function initPrintStyles() {
        const $printButton = $('<button class="ai-seo-article-generator-print-btn">🖨️ Print Article</button>');
        $('.ai-seo-article-generator-content').before($printButton);
        
        $printButton.on('click', function() {
            window.print();
        });
    }
    
    // Social sharing
    function initSocialSharing() {
        const title = document.title;
        const url = window.location.href;
        
        const shareButtons = [
            {
                name: 'Facebook',
                url: 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(url),
                icon: '📘'
            },
            {
                name: 'Twitter',
                url: 'https://twitter.com/intent/tweet?text=' + encodeURIComponent(title) + '&url=' + encodeURIComponent(url),
                icon: '🐦'
            },
            {
                name: 'LinkedIn',
                url: 'https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(url),
                icon: '💼'
            },
            {
                name: 'WhatsApp',
                url: 'https://wa.me/?text=' + encodeURIComponent(title + ' ' + url),
                icon: '📱'
            }
        ];
        
        let shareHtml = '<div class="ai-seo-article-generator-share-buttons"><h4>Share Article:</h4>';
        shareButtons.forEach(function(button) {
            shareHtml += '<a href="' + button.url + '" target="_blank" rel="noopener" class="share-btn" data-platform="' + button.name.toLowerCase() + '">' +
                        button.icon + ' ' + button.name +
                        '</a>';
        });
        shareHtml += '</div>';
        
        $('.ai-seo-article-generator-content').after(shareHtml);
        
        // Track sharing
        $('.share-btn').on('click', function() {
            const platform = $(this).data('platform');
            
            // You can send analytics here
            if (typeof gtag !== 'undefined') {
                gtag('event', 'share', {
                    method: platform,
                    content_type: 'article',
                    content_id: window.location.pathname
                });
            }
        });
    }
    
    // Initialize additional features if needed
    if ($('.ai-seo-article-generator-content').length) {
        initPrintStyles();
        initSocialSharing();
    }
    
    // Back to top button
    function initBackToTop() {
        const $backToTop = $('<button class="ai-seo-article-generator-back-to-top">⬆️ Back to Top</button>');
        $('body').append($backToTop);
        
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 300) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        });
        
        $backToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });
    }
    
    initBackToTop();
});