# WordPress.org Plugin Assets Guide

## Required Assets for AI SEO Article Generator

### 1. Plugin Icon
Create these files and place in `/svn-repo/assets/`:
- `icon-128x128.png` - Standard icon (128×128 pixels)
- `icon-256x256.png` - Retina/HiDPI icon (256×256 pixels)
- `icon.svg` - Optional SVG version for scalability

**Design suggestions for AI SEO Article Generator:**
- Include AI/robot elements
- SEO-related symbols (graph, magnifying glass)
- Text/article representation
- Use brand colors from your website

### 2. Plugin Banner
Create these files and place in `/svn-repo/assets/`:
- `banner-772x250.png` - Standard banner (772×250 pixels)
- `banner-1544x500.png` - Retina/HiDPI banner (1544×500 pixels)

**Design suggestions:**
- Show the plugin name prominently
- Include tagline about AI-powered content generation
- Show supported AI models (Claude, OpenAI logos if allowed)
- Highlight Hebrew/English support
- Professional, modern design

### 3. Screenshots
Based on your readme.txt, you need 6 screenshots:
1. `screenshot-1.png` - Plugin main interface
2. `screenshot-2.png` - Article structure templates
3. `screenshot-3.png` - Real-time article generation
4. `screenshot-4.png` - Generated article preview
5. `screenshot-5.png` - API settings page
6. `screenshot-6.png` - Background processing status

Place these in `/svn-repo/assets/`

## How to Add Assets

1. **Create your assets** following the specifications above

2. **Add them to SVN:**
```bash
cd /mnt/c/Users/<USER>/Desktop/Local/postinor/app/public/wp-content/plugins/ai-seo-article-generator/svn-repo
# Copy your asset files to the assets directory
cp /path/to/your/icon-128x128.png assets/
cp /path/to/your/icon-256x256.png assets/
cp /path/to/your/banner-772x250.png assets/
cp /path/to/your/banner-1544x500.png assets/
cp /path/to/your/screenshot-*.png assets/

# Add to SVN
svn add assets/*
svn ci -m 'Add plugin assets: icons, banners, and screenshots' --username ytrofr --password svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2
```

## Design Tools Recommendations

### Free Tools:
- **Canva** - Easy templates for banners and icons
- **Figma** - Professional design tool with free tier
- **GIMP** - Open source image editor
- **Inkscape** - For creating SVG icons

### AI-Powered Tools:
- **DALL-E 3** - Generate banner/icon concepts
- **Midjourney** - Create professional plugin graphics
- **Canva AI** - AI-assisted design

## Asset Guidelines

### Do's:
✅ Use clear, readable fonts
✅ Include your plugin name on banners
✅ Show actual plugin interface in screenshots
✅ Use consistent branding across all assets
✅ Optimize file sizes (use TinyPNG or similar)
✅ Test on both light and dark backgrounds

### Don'ts:
❌ Use copyrighted images or logos without permission
❌ Include pricing information
❌ Use animated GIFs
❌ Exceed file size limits
❌ Use low-quality or pixelated images
❌ Include external links or promotional content

## Quick Asset Creation Process

1. **Icon Design:**
   - Primary element: AI brain or robot icon
   - Secondary: SEO graph or magnifying glass
   - Text: "AI" or "SEO" abbreviated
   - Colors: Professional blue/green palette

2. **Banner Design:**
   - Title: "AI SEO Article Generator"
   - Subtitle: "Powered by Claude & OpenAI"
   - Features: "Hebrew/English Support | Background Processing"
   - Visual: Abstract AI network or content creation imagery

3. **Screenshots:**
   - Take actual screenshots from your WordPress admin
   - Annotate key features with arrows/callouts
   - Show the plugin in action (generating content)
   - Include both Hebrew and English examples

## Example SVN Commands

```bash
# After creating your assets, run these commands:
cd svn-repo
svn add assets/icon-128x128.png
svn add assets/icon-256x256.png
svn add assets/banner-772x250.png
svn add assets/banner-1544x500.png
svn add assets/screenshot-1.png
svn add assets/screenshot-2.png
svn add assets/screenshot-3.png
svn add assets/screenshot-4.png
svn add assets/screenshot-5.png
svn add assets/screenshot-6.png

# Commit all at once
svn ci -m 'Add plugin assets: icons, banners, and screenshots' --username ytrofr --password svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2
```

## Notes
- Assets typically update on WordPress.org within 15-30 minutes
- The CDN cache can take up to 6 hours to fully refresh
- Always use exact filenames as specified above
- Consider creating RTL versions for Hebrew users (add -rtl suffix)