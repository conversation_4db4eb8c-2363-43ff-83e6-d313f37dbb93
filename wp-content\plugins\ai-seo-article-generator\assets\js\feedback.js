/**
 * Feedback System JavaScript
 */

(function($) {
    'use strict';
    
    // Extend the localized aiSeoFeedback object with additional methods
    window.aiSeoFeedback = window.aiSeoFeedback || {};
    
    // Add methods to the existing object
    $.extend(window.aiSeoFeedback, {
        
        /**
         * Open WhatsApp chat
         */
        openWhatsApp: function() {
            // Use this. to access the extended object properties
            var message = encodeURIComponent(this.strings ? this.strings.whatsappMessage : 'Welcome to AI SEO Article Generator Support, How Can I Help? :)');
            var number = this.whatsappNumber || '972546330446';
            var url = 'https://wa.me/' + number + '?text=' + message;
            window.open(url, '_blank');
        },
        
        /**
         * Quick action handler
         */
        quickAction: function(type) {
            // Set the form type
            $('#feedback_type').val(type);
            
            // Scroll to form
            $('html, body').animate({
                scrollTop: $('.ai-seo-feedback-form-section').offset().top - 50
            }, 500);
            
            // Focus on subject field
            $('#feedback_subject').focus();
            
            // Pre-fill subject based on type
            var subjects = {
                'bug': 'Bug Report: ',
                'feature': 'Feature Request: '
            };
            
            if (subjects[type]) {
                $('#feedback_subject').val(subjects[type]);
            }
        }
    });
    
    // Document ready
    $(document).ready(function() {
        
        // Auto-dismiss notices after 5 seconds
        $('.ai-seo-article-generator-wrap .notice').delay(5000).fadeOut();
        
        // Enhance form validation
        $('form').on('submit', function() {
            var $form = $(this);
            var $submitButton = $form.find('button[type="submit"]');
            
            // Disable submit button to prevent double submission
            $submitButton.prop('disabled', true).text('Sending...');
        });
        
        // Add tooltips to quick actions
        $('.ai-seo-quick-actions a').hover(
            function() {
                $(this).css('transform', 'translateX(5px)');
            },
            function() {
                $(this).css('transform', 'translateX(0)');
            }
        );
        
        // Floating button animation on page load
        setTimeout(function() {
            $('.ai-seo-whatsapp-float').addClass('animated');
        }, 1000);
    });
    
})(jQuery);