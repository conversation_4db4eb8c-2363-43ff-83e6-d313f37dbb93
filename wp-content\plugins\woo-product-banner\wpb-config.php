<?php
/**
 * Configurazione per WooCommerce Product Banner
 * Questo file contiene impostazioni per il debug e la risoluzione dei problemi
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Abilita debug specifico per il plugin
if (!defined('WPB_DEBUG')) {
    define('WPB_DEBUG', true);
}

// Abilita logging dettagliato
if (!defined('WPB_DEBUG_LOG')) {
    define('WPB_DEBUG_LOG', true);
}

/**
 * Funzione di logging personalizzata per il plugin
 */
if (!function_exists('wpb_log')) {
    function wpb_log($message, $level = 'info') {
        if (!WPB_DEBUG_LOG) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[$timestamp] [$level] WPB: $message";
        
        // Log in WordPress debug.log
        error_log($log_message);
        
        // Log in plugin-specific file
        $log_file = WPB_PLUGIN_PATH . 'debug.log';
        file_put_contents($log_file, $log_message . "\n", FILE_APPEND | LOCK_EX);
    }
}

/**
 * Verifica requisiti del plugin
 */
function wpb_check_requirements() {
    $errors = array();
    
    // Verifica versione PHP
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        $errors[] = 'PHP 7.4 o superiore richiesto. Versione corrente: ' . PHP_VERSION;
    }
    
    // Verifica versione WordPress
    global $wp_version;
    if (version_compare($wp_version, '5.0', '<')) {
        $errors[] = 'WordPress 5.0 o superiore richiesto. Versione corrente: ' . $wp_version;
    }
    
    // Verifica WooCommerce
    if (!class_exists('WooCommerce')) {
        $errors[] = 'WooCommerce non è installato o attivo';
    } else {
        $wc_version = WC()->version ?? 'sconosciuta';
        if (version_compare($wc_version, '5.0', '<')) {
            $errors[] = 'WooCommerce 5.0 o superiore richiesto. Versione corrente: ' . $wc_version;
        }
    }
    
    // Verifica permessi directory
    if (!is_writable(WPB_PLUGIN_PATH)) {
        $errors[] = 'Directory del plugin non scrivibile: ' . WPB_PLUGIN_PATH;
    }
    
    // Verifica funzioni WordPress essenziali
    $required_functions = [
        'add_action',
        'add_filter', 
        'wp_enqueue_script',
        'wp_enqueue_style',
        'get_option',
        'update_option'
    ];
    
    foreach ($required_functions as $function) {
        if (!function_exists($function)) {
            $errors[] = "Funzione WordPress richiesta non disponibile: $function";
        }
    }
    
    return $errors;
}

/**
 * Mostra errori di requisiti
 */
function wpb_show_requirements_errors($errors) {
    if (empty($errors)) {
        return;
    }
    
    add_action('admin_notices', function() use ($errors) {
        echo '<div class="notice notice-error">';
        echo '<p><strong>WooCommerce Product Banner - Errori di Requisiti:</strong></p>';
        echo '<ul>';
        foreach ($errors as $error) {
            echo '<li>' . esc_html($error) . '</li>';
        }
        echo '</ul>';
        echo '<p>Il plugin è stato disattivato automaticamente.</p>';
        echo '</div>';
    });
    
    // Disattiva il plugin
    if (function_exists('deactivate_plugins')) {
        deactivate_plugins(WPB_PLUGIN_BASENAME);
    }
}

/**
 * Verifica stato del plugin
 */
function wpb_check_plugin_status() {
    wpb_log('Verifica stato plugin iniziata');
    
    // Verifica requisiti
    $errors = wpb_check_requirements();
    if (!empty($errors)) {
        wpb_log('Errori di requisiti trovati: ' . implode(', ', $errors), 'error');
        wpb_show_requirements_errors($errors);
        return false;
    }
    
    // Verifica che le classi siano caricate
    $required_classes = [
        'WooCommerce_Product_Banner',
        'WPB_Database',
        'WPB_Admin',
        'WPB_Frontend',
        'WPB_Security'
    ];
    
    foreach ($required_classes as $class) {
        if (!class_exists($class)) {
            wpb_log("Classe richiesta non trovata: $class", 'error');
            return false;
        }
    }
    
    wpb_log('Verifica stato plugin completata con successo');
    return true;
}

/**
 * Inizializza il sistema di configurazione
 */
add_action('init', function() {
    if (WPB_DEBUG) {
        wpb_log('Sistema di configurazione inizializzato');
        
        // Verifica stato solo in admin
        if (is_admin()) {
            wpb_check_plugin_status();
        }
    }
}, 1);

/**
 * Aggiungi informazioni di debug al footer (solo per admin)
 */
if (WPB_DEBUG && is_admin()) {
    add_action('admin_footer', function() {
        if (current_user_can('manage_options')) {
            echo '<!-- WPB Debug Info: Plugin Version ' . WPB_PLUGIN_VERSION . ' -->';
        }
    });
}

/**
 * Comando per pulire i log
 */
function wpb_clear_logs() {
    $log_file = WPB_PLUGIN_PATH . 'debug.log';
    if (file_exists($log_file)) {
        unlink($log_file);
    }
    
    $activation_log = WPB_PLUGIN_PATH . 'activation-debug.log';
    if (file_exists($activation_log)) {
        unlink($activation_log);
    }
    
    wpb_log('Log puliti');
}

// Pulisci i log all'attivazione
register_activation_hook(WPB_PLUGIN_BASENAME, 'wpb_clear_logs');
?>
