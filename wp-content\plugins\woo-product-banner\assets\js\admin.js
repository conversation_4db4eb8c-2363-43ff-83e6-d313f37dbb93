/**
 * WooCommerce Product Banner - Admin JavaScript
 * Version: 1.0.0
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        
        var mediaUploader;
        var $uploadButton = $('#wpb_upload_button');
        var $removeButton = $('#wpb_remove_button');
        var $imageInput = $('#wpb_banner_image');
        var $imagePreview = $('#wpb_image_preview');
        
        // Initialize media uploader
        function initMediaUploader() {
            // If the media frame already exists, reopen it
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }
            
            // Create the media frame
            mediaUploader = wp.media({
                title: wpb_admin.media_title || 'Select Banner Image',
                button: {
                    text: wpb_admin.media_button || 'Use this image'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });
            
            // When an image is selected, run a callback
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                
                // Set the image ID
                $imageInput.val(attachment.id);
                
                // Update preview
                updateImagePreview(attachment.url, attachment.alt || '');
                
                // Show remove button
                $removeButton.show();
                
                // Update button text
                $uploadButton.text(wpb_admin.change_image || 'Change Image');
            });
        }
        
        // Update image preview
        function updateImagePreview(url, alt) {
            if (url) {
                $imagePreview.html('<img src="' + url + '" alt="' + alt + '" style="max-width: 300px; height: auto;" />');
                $imagePreview.removeClass('empty');
            } else {
                $imagePreview.html('');
                $imagePreview.addClass('empty');
            }
        }
        
        // Remove image
        function removeImage() {
            $imageInput.val('');
            $imagePreview.html('');
            $imagePreview.addClass('empty');
            $removeButton.hide();
            $uploadButton.text(wpb_admin.select_image_btn || 'Select Image');
        }
        
        // Upload button click handler
        $uploadButton.on('click', function(e) {
            e.preventDefault();
            initMediaUploader();
            mediaUploader.open();
        });
        
        // Remove button click handler
        $removeButton.on('click', function(e) {
            e.preventDefault();
            
            if (confirm(wpb_admin.confirm_remove || 'Are you sure you want to remove this image?')) {
                removeImage();
            }
        });
        
        // Initialize preview state
        if (!$imageInput.val()) {
            $imagePreview.addClass('empty');
            $removeButton.hide();
        }
        
        // Form validation
        $('form').on('submit', function(e) {
            var submitType = $(document.activeElement).attr('name');
            
            // Only validate if saving (not removing)
            if (submitType === 'wpb_submit') {
                var imageId = $imageInput.val();
                
                if (!imageId || imageId === '') {
                    alert(wpb_admin.select_image || 'Please select an image before saving.');
                    e.preventDefault();
                    return false;
                }
            }
        });
        
        // Add loading state during form submission
        $('form').on('submit', function() {
            $(this).addClass('wpb-loading');
            $('input[type="submit"]').prop('disabled', true);
        });
        
        // Handle drag and drop for image preview area
        $imagePreview.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });
        
        $imagePreview.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
        });
        
        $imagePreview.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
            
            // Open media uploader instead of handling file drop directly
            // This ensures we use WordPress's media handling
            initMediaUploader();
            mediaUploader.open();
        });
        
        // Keyboard accessibility
        $uploadButton.on('keydown', function(e) {
            if (e.keyCode === 13 || e.keyCode === 32) { // Enter or Space
                e.preventDefault();
                $(this).click();
            }
        });
        
        $removeButton.on('keydown', function(e) {
            if (e.keyCode === 13 || e.keyCode === 32) { // Enter or Space
                e.preventDefault();
                $(this).click();
            }
        });
        
        // Auto-save functionality (optional)
        var autoSaveTimeout;
        $imageInput.on('change', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(function() {
                // Could implement auto-save here if desired
                console.log('Image selection changed');
            }, 1000);
        });
        
        // Preview image click handler for full-size view
        $imagePreview.on('click', 'img', function() {
            var $img = $(this);
            var fullSizeUrl = $img.attr('src');
            
            // Create modal for full-size preview
            var modal = $('<div class="wpb-modal-overlay">' +
                '<div class="wpb-modal-content">' +
                '<img src="' + fullSizeUrl + '" alt="' + $img.attr('alt') + '" />' +
                '<button class="wpb-modal-close">&times;</button>' +
                '</div>' +
                '</div>');
            
            $('body').append(modal);
            modal.fadeIn(200);
            
            // Close modal handlers
            modal.on('click', '.wpb-modal-close, .wpb-modal-overlay', function(e) {
                if (e.target === this) {
                    modal.fadeOut(200, function() {
                        modal.remove();
                    });
                }
            });
            
            // ESC key to close modal
            $(document).on('keydown.wpb-modal', function(e) {
                if (e.keyCode === 27) { // ESC
                    modal.fadeOut(200, function() {
                        modal.remove();
                    });
                    $(document).off('keydown.wpb-modal');
                }
            });
        });
        
    });
    
})(jQuery);

// Add modal styles dynamically
jQuery(document).ready(function($) {
    var modalStyles = '<style>' +
        '.wpb-modal-overlay {' +
        'position: fixed; top: 0; left: 0; width: 100%; height: 100%;' +
        'background: rgba(0,0,0,0.8); z-index: 100000; display: none;' +
        'align-items: center; justify-content: center;' +
        '}' +
        '.wpb-modal-content {' +
        'position: relative; max-width: 90%; max-height: 90%;' +
        'background: white; padding: 20px; border-radius: 4px;' +
        '}' +
        '.wpb-modal-content img {' +
        'max-width: 100%; height: auto; display: block;' +
        '}' +
        '.wpb-modal-close {' +
        'position: absolute; top: 10px; right: 15px;' +
        'background: none; border: none; font-size: 24px;' +
        'cursor: pointer; color: #666;' +
        '}' +
        '.wpb-modal-close:hover { color: #000; }' +
        '</style>';
    
    $('head').append(modalStyles);
});
