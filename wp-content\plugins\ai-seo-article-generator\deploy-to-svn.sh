#!/bin/bash

# WordPress.org SVN deployment script
# Run this script manually when you need to deploy to WordPress.org

# Configuration
PLUGIN_SLUG="ai-seo-article-generator"
SVN_URL="https://plugins.svn.wordpress.org/${PLUGIN_SLUG}"
MAIN_FILE="${PLUGIN_SLUG}.php"
SVN_DIR="svn-repo"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}WordPress.org SVN Deployment Script${NC}"
echo "======================================"

# Get version from main plugin file
VERSION=$(grep "Version:" $MAIN_FILE | head -1 | awk '{print $3}')
echo -e "Plugin version: ${GREEN}$VERSION${NC}"

# Check if SVN is installed
if ! command -v svn &> /dev/null; then
    echo -e "${RED}Error: SVN is not installed${NC}"
    exit 1
fi

# Prompt for credentials
echo -e "\n${BLUE}Enter your WordPress.org credentials:${NC}"
read -p "Username: " SVN_USERNAME
read -s -p "Password: " SVN_PASSWORD
echo ""

# Checkout SVN repository if not exists
if [ ! -d "$SVN_DIR" ]; then
    echo -e "\n${BLUE}Checking out SVN repository...${NC}"
    svn co $SVN_URL $SVN_DIR --username "$SVN_USERNAME" --password "$SVN_PASSWORD" --non-interactive --trust-server-cert
else
    echo -e "\n${BLUE}Updating SVN repository...${NC}"
    cd $SVN_DIR
    svn up --username "$SVN_USERNAME" --password "$SVN_PASSWORD" --non-interactive --trust-server-cert
    cd ..
fi

# Clean trunk
echo -e "\n${BLUE}Cleaning trunk directory...${NC}"
rm -rf $SVN_DIR/trunk/*

# Copy files to trunk
echo -e "${BLUE}Copying plugin files to trunk...${NC}"
cp -r $MAIN_FILE includes languages assets readme.txt $SVN_DIR/trunk/

# Add new files
cd $SVN_DIR
svn add trunk/* --force

# Show status
echo -e "\n${BLUE}SVN Status:${NC}"
svn status

# Confirm before commit
echo -e "\n${BLUE}Ready to commit version $VERSION to trunk${NC}"
read -p "Continue? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Commit to trunk
    echo -e "\n${BLUE}Committing to trunk...${NC}"
    svn ci -m "Update to version $VERSION" --username "$SVN_USERNAME" --password "$SVN_PASSWORD" --non-interactive --trust-server-cert
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Successfully committed to trunk${NC}"
        
        # Check if tag exists
        if [ ! -d "tags/$VERSION" ]; then
            echo -e "\n${BLUE}Creating tag $VERSION...${NC}"
            svn cp trunk tags/$VERSION
            svn ci -m "Tagging version $VERSION" --username "$SVN_USERNAME" --password "$SVN_PASSWORD" --non-interactive --trust-server-cert
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✓ Successfully created tag $VERSION${NC}"
            else
                echo -e "${RED}✗ Failed to create tag${NC}"
            fi
        else
            echo -e "${BLUE}Tag $VERSION already exists, skipping...${NC}"
        fi
    else
        echo -e "${RED}✗ Failed to commit to trunk${NC}"
        echo -e "${RED}If authentication fails, try these steps:${NC}"
        echo "1. Visit https://wordpress.org/support/users/your-username/edit/"
        echo "2. Scroll down to 'Account Management'"
        echo "3. Generate a new application password"
        echo "4. Use your username and the application password (without spaces)"
    fi
else
    echo -e "${RED}Deployment cancelled${NC}"
fi

cd ..
echo -e "\n${BLUE}Deployment script completed${NC}"