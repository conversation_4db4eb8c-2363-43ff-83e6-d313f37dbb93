<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace ElementorDeps\Twig\Sandbox;

/**
 * Exception thrown when a not allowed class method is used in a template.
 *
 * <AUTHOR> <<EMAIL>>
 */
final class SecurityNotAllowedMethodError extends SecurityError
{
    private $className;
    private $methodName;
    public function __construct(string $message, string $className, string $methodName)
    {
        parent::__construct($message);
        $this->className = $className;
        $this->methodName = $methodName;
    }
    public function getClassName() : string
    {
        return $this->className;
    }
    public function getMethodName()
    {
        return $this->methodName;
    }
}
