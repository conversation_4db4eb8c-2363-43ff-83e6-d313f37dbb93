/*! elementor - v3.31.0 - 11-08-2025 */
/*! For license information please see app-packages.min.js.LICENSE.txt */
(()=>{var r={205:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var c=u(s(39805)),p=u(s(40989)),m=u(s(15118)),v=u(s(29402)),h=u(s(41621)),y=u(s(87861)),g=u(s(85707)),_=u(s(47483));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}function _superPropGet(r,l,s,u){var c=(0,h.default)((0,v.default)(1&u?r.prototype:r),l,s);return 2&u&&"function"==typeof c?function(r){return c.apply(s,r)}:c}var b=l.default=function(r){function Button(){return(0,c.default)(this,Button),function _callSuper(r,l,s){return l=(0,v.default)(l),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,v.default)(r).constructor):l.apply(r,s))}(this,Button,arguments)}return(0,y.default)(Button,r),(0,p.default)(Button,[{key:"getCssId",value:function getCssId(){return"eps-app-header-btn-"+_superPropGet(Button,"getCssId",this,3)([])}},{key:"getClassName",value:function getClassName(){return this.props.includeHeaderBtnClass?"eps-app__header-btn "+_superPropGet(Button,"getClassName",this,3)([]):_superPropGet(Button,"getClassName",this,3)([])}}])}(_.default);(0,g.default)(b,"defaultProps",Object.assign({},_.default.defaultProps,{hideText:!0,includeHeaderBtnClass:!0}))},2363:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogTitle;var p=c(s(41594)),m=c(s(85707)),v=c(s(78304)),h=c(s(85418));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,m.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}function DialogTitle(r){return p.default.createElement(h.default,(0,v.default)({},r,{className:"eps-dialog__title ".concat(r.className)}))}DialogTitle.propTypes=_objectSpread(_objectSpread({},h.default.propTypes),{},{className:u.string}),DialogTitle.defaultProps=_objectSpread(_objectSpread({},h.default.propTypes),{},{variant:"h3",tag:"h3",className:""})},2526:(r,l,s)=>{"use strict";var u=s(62688),c=s(12470).__,p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=ModalTip;var m=p(s(41594)),v=s(79397),h=p(s(85418)),y=p(s(55725));function ModalTip(r){return m.default.createElement("div",{className:(0,v.arrayToClassName)(["eps-modal__tip",r.className])},m.default.createElement(h.default,{variant:"h3",tag:"h3"},r.title),r.description&&m.default.createElement(y.default,{variant:"xs"},r.description))}ModalTip.propTypes={className:u.string,title:u.string,description:u.string},ModalTip.defaultProps={className:"",title:c("Tip","elementor")}},3416:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Grid;var p=c(s(41594)),m=c(s(10906)),v=s(79397);function Grid(r){var l=["eps-grid",r.className].concat((0,m.default)(function getPropsClasses(r,l){var s=[];for(var u in r)if(l[u]){var c=isValidPropValue(l[u])?l[u]:"";s.push(getBaseClassName()+renderPropValueBrackets(r[u],c))}return s}({direction:"--direction{{ -VALUE }}",justify:"--justify{{ -VALUE }}",alignContent:"--align-content{{ -VALUE }}",alignItems:"--align-items{{ -VALUE }}",container:"-container",item:"-item",noWrap:"-container--no-wrap",wrapReverse:"-container--wrap-reverse",zeroMinWidth:"-item--zero-min-width",spacing:"-container--spacing",xs:"-item-xs{{ -VALUE }}",sm:"-item-sm{{ -VALUE }}",md:"-item-md{{ -VALUE }}",lg:"-item-lg{{ -VALUE }}",xl:"-item-xl{{ -VALUE }}",xxl:"-item-xxl{{ -VALUE }}"},r)));return p.default.createElement("div",{style:function getStyle(){return isValidPropValue(r.spacing)?{"--grid-spacing-gutter":(0,v.pxToRem)(r.spacing)}:{}}(),className:(0,v.arrayToClassName)(l)},r.children)}function renderPropValueBrackets(r,l){var s=r.match(/{{.*?}}/);if(s){var u=l?s[0].replace(/[{ }]/g,"").replace(/value/i,l):"";r=r.replace(s[0],u)}return r}function getBaseClassName(){return"eps-grid"}function isValidPropValue(r){return r&&"boolean"!=typeof r}s(28042),Grid.propTypes={className:u.string,direction:u.oneOf(["row","column","row-reverse","column-reverse"]),justify:u.oneOf(["start","center","end","space-between","space-evenly","space-around","stretch"]),alignContent:u.oneOf(["start","center","end","space-between","stretch"]),alignItems:u.oneOf(["start","center","end","baseline","stretch"]),container:u.bool,item:u.bool,noWrap:u.bool,wrapReverse:u.bool,zeroMinWidth:u.bool,spacing:u.number,xs:u.oneOfType([u.number,u.bool]),sm:u.oneOfType([u.number,u.bool]),md:u.oneOfType([u.number,u.bool]),lg:u.oneOfType([u.number,u.bool]),xl:u.oneOfType([u.number,u.bool]),xxl:u.oneOfType([u.number,u.bool]),children:u.any.isRequired},Grid.defaultProps={className:""}},3826:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Select;var p=c(s(41594));function Select(r){return p.default.createElement("select",{multiple:r.multiple,className:r.className,value:r.value,onChange:r.onChange,ref:r.elRef,onClick:function onClick(){var l;return null===(l=r.onClick)||void 0===l?void 0:l.call(r)}},r.options.map(function(r){return r.children?p.default.createElement("optgroup",{label:r.label,key:r.label},r.children.map(function(r){return p.default.createElement("option",{key:r.value,value:r.value},r.label)})):p.default.createElement("option",{key:r.value,value:r.value},r.label)}))}Select.propTypes={className:u.string,onChange:u.func,options:u.array,elRef:u.object,multiple:u.bool,value:u.oneOfType([u.array,u.string]),onClick:u.func},Select.defaultProps={className:"",options:[]}},4380:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardHeader;var p=c(s(41594)),m=s(79397);function CardHeader(r){var l="eps-card__header",s=[l,r.className],u={};return Object.prototype.hasOwnProperty.call(r,"padding")&&(u["--eps-card-header-padding"]=(0,m.pxToRem)(r.padding),s.push(l+"--padding")),p.default.createElement("header",{className:(0,m.arrayToClassName)(s),style:u},r.children)}s(45302),CardHeader.propTypes={className:u.string,padding:u.string,passive:u.bool,active:u.bool,children:u.any.isRequired},CardHeader.defaultProps={className:""}},4688:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=SiteParts;var m=p(s(41594)),v=p(s(78304)),h=p(s(47483)),y=p(s(70692)),g=p(s(61678)),_=p(s(44969)),b=s(8188),x=function InfoButton(r){var l={text:u("Info","elementor"),hideText:!0,icon:"eicon-info-circle e-site-part__info-toggle"};return m.default.createElement(g.default,{toggleButtonProps:l,title:r.title},m.default.createElement(y.default,{columns:2,spacing:60},m.default.createElement("section",null,m.default.createElement("h3",null,r.type),m.default.createElement("p",null,r.content,m.default.createElement("br",null),m.default.createElement(h.default,{text:u("Learn More","elementor"),color:"link",target:"_blank",url:r.docs})),m.default.createElement("div",{className:"eps-modal__tip"},m.default.createElement("h3",null,u("Tip","elementor")),m.default.createElement("p",null,r.tip))),m.default.createElement("section",null,m.default.createElement("h3",null,u("Watch Video","elementor")),m.default.createElement("div",{className:"video-wrapper"},m.default.createElement("iframe",{id:"ytplayer",src:r.video_url,frameBorder:"0"})))))};function SiteParts(r){var l=m.default.useContext(b.Context).templateTypes;return m.default.createElement(y.default,{className:"e-site-editor__site-parts",colMinWidth:200,spacing:25},l.map(function(l){return m.default.createElement(_.default,(0,v.default)({className:"e-site-editor__site-part",actionButton:m.default.createElement(x,(0,v.default)({type:l.title},l.tooltip_data)),thumbnail:l.urls.thumbnail,key:l.type},l),m.default.createElement(r.hoverElement,l))}))}x.propTypes={content:c.string.isRequired,docs:c.string.isRequired,tip:c.string.isRequired,title:c.string.isRequired,type:c.string.isRequired,video_url:c.string.isRequired},SiteParts.propTypes={hoverElement:c.func.isRequired}},4815:()=>{},6056:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=HeaderButtons;var m=p(s(41594)),v=p(s(78304)),h=p(s(46361)),y=p(s(205));function HeaderButtons(r){var l=(0,h.default)(),s="";if(r.buttons.length){var c=r.buttons.map(function(r){return m.default.createElement(y.default,(0,v.default)({key:r.id},r))});s=m.default.createElement(m.default.Fragment,null,c)}return m.default.createElement("div",{className:"eps-app__header-buttons"},m.default.createElement(y.default,{text:u("Close","elementor"),icon:"eicon-close",className:"eps-app__close-button",onClick:function actionOnClose(){r.onClose?r.onClose():l.backToDashboard()}}),s)}HeaderButtons.propTypes={buttons:c.arrayOf(c.object),onClose:c.func},HeaderButtons.defaultProps={buttons:[]}},7229:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardBody;var p=c(s(41594)),m=s(79397);function CardBody(r){var l="eps-card__body",s=[l,r.className],u={};return Object.prototype.hasOwnProperty.call(r,"padding")&&(u["--eps-card-body-padding"]=(0,m.pxToRem)(r.padding),s.push(l+"--padding")),p.default.createElement("main",{className:(0,m.arrayToClassName)(s),style:u},r.children)}s(45302),CardBody.propTypes={className:u.string,padding:u.string,passive:u.bool,active:u.bool,children:u.any.isRequired},CardBody.defaultProps={className:""}},8188:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=l.TemplateTypesConsumer=l.Context=void 0;var m=p(s(41594)),v=p(s(39805)),h=p(s(40989)),y=p(s(15118)),g=p(s(29402)),_=p(s(87861)),b=p(s(85707));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}s(96431);var x=l.Context=m.default.createContext(),w=function(r){function TemplateTypesContext(r){var l;return(0,v.default)(this,TemplateTypesContext),(l=function _callSuper(r,l,s){return l=(0,g.default)(l),(0,y.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,g.default)(r).constructor):l.apply(r,s))}(this,TemplateTypesContext,[r])).state={templateTypes:[],loading:!0,error:!1},l}return(0,_.default)(TemplateTypesContext,r),(0,h.default)(TemplateTypesContext,[{key:"componentDidMount",value:function componentDidMount(){var r=this;this.getTemplateTypes().then(function(l){r.setState({templateTypes:l,loading:!1})}).fail(function(l){r.setState({error:l.statusText?l.statusText:l,loading:!1})})}},{key:"getTemplateTypes",value:function getTemplateTypes(){return elementorCommon.ajax.load({action:"app_site_editor_template_types"})}},{key:"render",value:function render(){return this.state.error?m.default.createElement("div",{className:"e-loading-wrapper"},m.default.createElement("h3",null,u("Error:","elementor")," ",this.state.error)):this.state.loading?m.default.createElement("div",{className:"elementor-loading"},m.default.createElement("div",{className:"elementor-loader-wrapper"},m.default.createElement("div",{className:"elementor-loader"},m.default.createElement("div",{className:"elementor-loader-boxes"},m.default.createElement("div",{className:"elementor-loader-box"}),m.default.createElement("div",{className:"elementor-loader-box"}),m.default.createElement("div",{className:"elementor-loader-box"}),m.default.createElement("div",{className:"elementor-loader-box"}))),m.default.createElement("div",{className:"elementor-loading-title"},u("Loading","elementor")))):m.default.createElement(x.Provider,{value:this.state},this.props.children)}}])}(m.default.Component);(0,b.default)(w,"propTypes",{children:c.object.isRequired});l.TemplateTypesConsumer=x.Consumer,l.default=w},9535:(r,l,s)=>{var u=s(89736);function _regenerator(){var l,s,c="function"==typeof Symbol?Symbol:{},p=c.iterator||"@@iterator",m=c.toStringTag||"@@toStringTag";function i(r,c,p,m){var h=c&&c.prototype instanceof Generator?c:Generator,y=Object.create(h.prototype);return u(y,"_invoke",function(r,u,c){var p,m,h,y=0,g=c||[],_=!1,b={p:0,n:0,v:l,a:d,f:d.bind(l,4),d:function d(r,s){return p=r,m=0,h=l,b.n=s,v}};function d(r,u){for(m=r,h=u,s=0;!_&&y&&!c&&s<g.length;s++){var c,p=g[s],x=b.p,w=p[2];r>3?(c=w===u)&&(h=p[(m=p[4])?5:(m=3,3)],p[4]=p[5]=l):p[0]<=x&&((c=r<2&&x<p[1])?(m=0,b.v=u,b.n=p[1]):x<w&&(c=r<3||p[0]>u||u>w)&&(p[4]=r,p[5]=u,b.n=w,m=0))}if(c||r>1)return v;throw _=!0,u}return function(c,g,x){if(y>1)throw TypeError("Generator is already running");for(_&&1===g&&d(g,x),m=g,h=x;(s=m<2?l:h)||!_;){p||(m?m<3?(m>1&&(b.n=-1),d(m,h)):b.n=h:b.v=h);try{if(y=2,p){if(m||(c="next"),s=p[c]){if(!(s=s.call(p,h)))throw TypeError("iterator result is not an object");if(!s.done)return s;h=s.value,m<2&&(m=0)}else 1===m&&(s=p.return)&&s.call(p),m<2&&(h=TypeError("The iterator does not provide a '"+c+"' method"),m=1);p=l}else if((s=(_=b.n<0)?h:r.call(u,b))!==v)break}catch(r){p=l,m=1,h=r}finally{y=1}}return{value:s,done:_}}}(r,p,m),!0),y}var v={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}s=Object.getPrototypeOf;var h=[][p]?s(s([][p]())):(u(s={},p,function(){return this}),s),y=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(h);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,u(r,m,"GeneratorFunction")),r.prototype=Object.create(y),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,u(y,"constructor",GeneratorFunctionPrototype),u(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",u(GeneratorFunctionPrototype,m,"GeneratorFunction"),u(y),u(y,m,"Generator"),u(y,p,function(){return this}),u(y,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},10564:r=>{function _typeof(l){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(l)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10575:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function useIntroduction(r){var l,s=(0,p.useState)(!(null===(l=window.elementorAppConfig)||void 0===l||null===(l=l.user)||void 0===l||null===(l=l.introduction)||void 0===l||!l[r])),u=(0,c.default)(s,2),m=u[0],v=u[1];return{isViewed:m,markAsViewed:function markAsViewed(){return r?new Promise(function(l,s){m&&s(),elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:r},error:function error(){return s()},success:function success(){var s;v(!0),null!==(s=window.elementorAppConfig)&&void 0!==s&&null!==(s=s.user)&&void 0!==s&&s.introduction&&(window.elementorAppConfig.user.introduction[r]=!0),l()}})}):Promise.reject()}}};var c=u(s(18821)),p=s(41594)},10906:(r,l,s)=>{var u=s(91819),c=s(20365),p=s(37744),m=s(78687);r.exports=function _toConsumableArray(r){return u(r)||c(r)||p(r)||m()},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,l,s)=>{var u=s(10564).default;r.exports=function toPrimitive(r,l){if("object"!=u(r)||!r)return r;var s=r[Symbol.toPrimitive];if(void 0!==s){var c=s.call(r,l||"default");if("object"!=u(c))return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},12505:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Select2;var p=c(s(41594)),m=c(s(85707)),v=c(s(3826));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,m.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}s(4815);var h=function getDefaultSettings(){return{allowClear:!0,placeholder:"",dir:elementorCommon.config.isRTL?"rtl":"ltr"}};function Select2(r){var l=p.default.useRef(null);return p.default.useEffect(function(){var s=jQuery(l.current).select2(_objectSpread(_objectSpread(_objectSpread({},h()),r.settings),{},{placeholder:r.placeholder})).on("select2:select select2:unselect",r.onChange);return r.onReady&&r.onReady(s),function(){s.select2("destroy").off("select2:select select2:unselect")}},[r.settings,r.options]),p.default.useEffect(function(){jQuery(l.current).val(r.value).trigger("change")},[r.value]),p.default.createElement(v.default,{multiple:r.multiple,value:r.value,onChange:r.onChange,elRef:l,options:r.options,placeholder:r.placeholder})}Select2.propTypes={value:u.oneOfType([u.array,u.string]),onChange:u.func,onReady:u.func,options:u.array,settings:u.object,multiple:u.bool,placeholder:u.string},Select2.defaultProps={settings:{},options:[],dependencies:[],placeholder:""}},14546:()=>{},14718:(r,l,s)=>{var u=s(29402);r.exports=function _superPropBase(r,l){for(;!{}.hasOwnProperty.call(r,l)&&null!==(r=u(r)););return r},r.exports.__esModule=!0,r.exports.default=r.exports},14888:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Header;var p=c(s(41594)),m=c(s(78304)),v=c(s(3416)),h=c(s(6056)),y=c(s(80791));function Header(r){(0,y.default)({title:r.title});var l="span",s={};return r.titleRedirectRoute&&(l="a",s={href:"#".concat(r.titleRedirectRoute),target:"_self"}),p.default.createElement(v.default,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header"},p.default.createElement(l,(0,m.default)({className:"eps-app__logo-title-wrapper"},s),p.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),p.default.createElement("h1",{className:"eps-app__title"},r.title)),p.default.createElement(h.default,{buttons:r.buttons,onClose:r.onClose}))}Header.propTypes={title:u.string,titleRedirectRoute:u.string,buttons:u.arrayOf(u.object),onClose:u.func},Header.defaultProps={buttons:[]}},15118:(r,l,s)=>{var u=s(10564).default,c=s(36417);r.exports=function _possibleConstructorReturn(r,l){if(l&&("object"==u(l)||"function"==typeof l))return l;if(void 0!==l)throw new TypeError("Derived constructors may only return object or undefined");return c(r)},r.exports.__esModule=!0,r.exports.default=r.exports},15656:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Dialog;var p=c(s(41594)),m=c(s(54902)),v=c(s(51776)),h=c(s(2363)),y=c(s(59250)),g=c(s(20394)),_=c(s(18861));function Dialog(r){return p.default.createElement(m.default,{onSubmit:r.onSubmit,onClose:r.onClose},p.default.createElement(v.default,null,r.title&&p.default.createElement(h.default,null,r.title),r.text&&p.default.createElement(y.default,null,r.text),r.children),p.default.createElement(g.default,null,p.default.createElement(_.default,{key:"dismiss",text:r.dismissButtonText,onClick:r.dismissButtonOnClick,url:r.dismissButtonUrl,target:r.dismissButtonTarget,tabIndex:"2"}),p.default.createElement(_.default,{key:"approve",text:r.approveButtonText,onClick:r.approveButtonOnClick,url:r.approveButtonUrl,target:r.approveButtonTarget,color:r.approveButtonColor,elRef:r.approveButtonRef,tabIndex:"1"})))}s(91618),Dialog.propTypes={title:u.any,text:u.any,children:u.any,onSubmit:u.func,onClose:u.func,dismissButtonText:u.string.isRequired,dismissButtonOnClick:u.func,dismissButtonUrl:u.string,dismissButtonTarget:u.string,approveButtonText:u.string.isRequired,approveButtonOnClick:u.func,approveButtonUrl:u.string,approveButtonColor:u.string,approveButtonTarget:u.string,approveButtonRef:u.object},Dialog.defaultProps={},Dialog.Wrapper=m.default,Dialog.Content=v.default,Dialog.Title=h.default,Dialog.Text=y.default,Dialog.Actions=g.default,Dialog.Button=_.default},16357:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardHeadline;var p=c(s(41594)),m=s(79397);function CardHeadline(r){var l=["eps-card__headline",r.className];return p.default.createElement("h4",{className:(0,m.arrayToClassName)(l)},r.children)}s(45302),CardHeadline.propTypes={className:u.string,children:u.any.isRequired},CardHeadline.defaultProps={className:""}},16944:()=>{},18186:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Menu;var p=c(s(41594)),m=c(s(78304));s(37868);var v=c(s(47483)),h=c(s(47485)),y=s(83040);function Menu(r){var l=function ActionButton(l){return r.actionButton?r.actionButton(l):""};return r.promotion?p.default.createElement("nav",{className:"eps-menu"},r.children,p.default.createElement("ul",null,r.menuItems.map(function(r){return p.default.createElement("li",{key:r.type,className:"eps-menu-item"},p.default.createElement(v.default,(0,m.default)({text:r.title,className:"eps-menu-item__link"},r)),p.default.createElement(l,r))}))):p.default.createElement(y.LocationProvider,{history:h.default.appHistory},p.default.createElement("nav",{className:"eps-menu"},r.children,p.default.createElement("ul",null,r.menuItems.map(function(r){return p.default.createElement(y.Match,{key:r.type,path:r.url},function(s){var u=s.match;return p.default.createElement("li",{key:r.type,className:"eps-menu-item".concat(u?" eps-menu-item--active":"")},p.default.createElement(v.default,(0,m.default)({text:r.title,className:"eps-menu-item__link"},r)),p.default.createElement(l,r))})}))))}Menu.propTypes={menuItems:u.arrayOf(u.object),children:u.any,actionButton:u.func,promotion:u.bool}},18320:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardImage;var p=c(s(41594));function CardImage(r){var l=p.default.createElement("img",{src:r.src,alt:r.alt,className:"eps-card__image",loading:"lazy"});return p.default.createElement("figure",{className:"eps-card__figure ".concat(r.className)},l,r.children)}s(45302),CardImage.propTypes={className:u.string,src:u.string.isRequired,alt:u.string.isRequired,children:u.any},CardImage.defaultProps={className:""}},18821:(r,l,s)=>{var u=s(70569),c=s(65474),p=s(37744),m=s(11018);r.exports=function _slicedToArray(r,l){return u(r)||c(r,l)||p(r,l)||m()},r.exports.__esModule=!0,r.exports.default=r.exports},18861:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogButton;var p=c(s(41594)),m=c(s(85707)),v=c(s(78304)),h=c(s(47483));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,m.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}function DialogButton(r){return p.default.createElement(h.default,(0,v.default)({},r,{className:"eps-dialog__button ".concat(r.className)}))}DialogButton.propTypes=_objectSpread(_objectSpread({},h.default.propTypes),{},{tabIndex:u.string,type:u.string}),DialogButton.defaultProps=_objectSpread(_objectSpread({},h.default.defaultProps),{},{tabIndex:"0",type:"button"})},20365:r=>{r.exports=function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)},r.exports.__esModule=!0,r.exports.default=r.exports},20394:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogActions;var p=c(s(41594));function DialogActions(r){return p.default.createElement("div",{className:"eps-dialog__buttons"},r.children)}DialogActions.propTypes={children:u.any}},20567:r=>{"use strict";var warning=function(){};r.exports=warning},21114:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function useConfirmAction(r){var l=r.action,s=r.doNotShowAgainKey,u=void 0===s?null:s,h=(0,v.default)(u),y=h.isViewed,g=h.markAsViewed,_=(0,m.useState)({isOpen:!1,actionArgs:[]}),b=(0,p.default)(_,2),x=b[0],w=b[1],P=(0,m.useState)(!1),C=(0,p.default)(P,2),O=C[0],j=C[1];return{checkbox:{isChecked:O,setIsChecked:j},dialog:{isOpen:x.isOpen,approve:function approve(){l.apply(void 0,(0,c.default)(x.actionArgs)),O&&u&&g(),w({isOpen:!1,actionArgs:[]})},dismiss:function dismiss(){w({isOpen:!1,actionArgs:[]})}},runAction:function runAction(){for(var r=arguments.length,s=new Array(r),u=0;u<r;u++)s[u]=arguments[u];y?l.apply(void 0,s):w({isOpen:!0,actionArgs:s})}}};var c=u(s(10906)),p=u(s(18821)),m=s(41594),v=u(s(10575))},21689:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Box;var p=c(s(41594)),m=s(79397);function Box(r){var l="eps-box",s=[l,r.className],u={};return Object.prototype.hasOwnProperty.call(r,"padding")&&(u["--eps-box-padding"]=(0,m.pxToRem)(r.padding),s.push(l+"--padding")),p.default.createElement("div",{style:u,className:(0,m.arrayToClassName)(s)},r.children)}s(51959),Box.propTypes={className:u.string,padding:u.string,children:u.oneOfType([u.string,u.object,u.arrayOf(u.object)]).isRequired},Box.defaultProps={className:""}},22057:()=>{},22322:()=>{},23074:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardFooter;var p=c(s(41594)),m=s(79397);function CardFooter(r){var l="eps-card__footer",s=[l,r.className],u={};return Object.prototype.hasOwnProperty.call(r,"padding")&&(u["--eps-card-footer-padding"]=(0,m.pxToRem)(r.padding),s.push(l+"--padding")),p.default.createElement("footer",{className:(0,m.arrayToClassName)(s),style:u},r.children)}s(45302),CardFooter.propTypes={className:u.string,padding:u.string,passive:u.bool,active:u.bool,children:u.object.isRequired},CardFooter.defaultProps={className:""}},24017:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Sidebar;var p=c(s(41594));function Sidebar(r){return p.default.createElement("div",{className:"eps-app__sidebar"},r.children)}Sidebar.propTypes={children:u.object}},25368:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Content;var p=c(s(41594));function Content(r){return p.default.createElement("main",{className:"eps-app__content ".concat(r.className)},r.children)}Content.propTypes={children:u.any,className:u.string},Content.defaultProps={className:""}},26587:()=>{},27843:()=>{},28042:()=>{},28101:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function NotFound(){var r={title:u("Not Found","elementor"),className:"eps-app__not-found",content:p.default.createElement("h1",null," ",u("Not Found","elementor")," "),sidebar:p.default.createElement(p.default.Fragment,null)};return p.default.createElement(m.default,r)};var p=c(s(41594)),m=c(s(80226))},28127:(r,l,s)=>{"use strict";var u="__global_unique_id__";r.exports=function(){return s.g[u]=(s.g[u]||0)+1}},28929:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784),p=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Collapse;var m=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,m,v={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((m=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(m.get||m.set)?c(v,h,m):v[h]=r[h]);return v}(r,l)}(s(41594)),v=c(s(85707)),h=c(s(18821)),y=s(79397),g=s(77879),_=c(s(79514)),b=c(s(62521));function Collapse(r){var l=(0,m.useState)(r.isOpened),s=(0,h.default)(l,2),u=s[0],c=s[1],p="e-app-collapse",_=[p,r.className,(0,v.default)({},p+"--opened",u)];return(0,m.useEffect)(function(){r.isOpened!==u&&c(r.isOpened)},[r.isOpened]),(0,m.useEffect)(function(){r.onChange&&r.onChange(u)},[u]),m.default.createElement(g.CollapseContext.Provider,{value:{toggle:function toggle(){return c(function(r){return!r})}}},m.default.createElement("div",{className:(0,y.arrayToClassName)(_)},r.children))}s(57463),Collapse.propTypes={className:u.string,isOpened:u.bool,onChange:u.func,children:u.oneOfType([u.node,u.arrayOf(u.node)])},Collapse.defaultProps={className:"",isOpened:!1},Collapse.Toggle=_.default,Collapse.Content=b.default},29402:r=>{function _getPrototypeOf(l){return r.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},r.exports.__esModule=!0,r.exports.default=r.exports,_getPrototypeOf(l)}r.exports=_getPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},31659:(r,l,s)=>{"use strict";l.__esModule=!0;var u=s(41594),c=(_interopRequireDefault(u),_interopRequireDefault(s(62688))),p=_interopRequireDefault(s(28127));_interopRequireDefault(s(20567));function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}}function _classCallCheck(r,l){if(!(r instanceof l))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(r,l){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?r:l}function _inherits(r,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);r.prototype=Object.create(l&&l.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(r,l):r.__proto__=l)}var m=**********;l.default=function createReactContext(r,l){var s,v,h="__create-react-context-"+(0,p.default)()+"__",y=function(r){function Provider(){var l,s;_classCallCheck(this,Provider);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return l=s=_possibleConstructorReturn(this,r.call.apply(r,[this].concat(c))),s.emitter=function createEventEmitter(r){var l=[];return{on:function on(r){l.push(r)},off:function off(r){l=l.filter(function(l){return l!==r})},get:function get(){return r},set:function set(s,u){r=s,l.forEach(function(l){return l(r,u)})}}}(s.props.value),_possibleConstructorReturn(s,l)}return _inherits(Provider,r),Provider.prototype.getChildContext=function getChildContext(){var r;return(r={})[h]=this.emitter,r},Provider.prototype.componentWillReceiveProps=function componentWillReceiveProps(r){if(this.props.value!==r.value){var s=this.props.value,u=r.value,c=void 0;!function objectIs(r,l){return r===l?0!==r||1/r==1/l:r!=r&&l!=l}(s,u)?(c="function"==typeof l?l(s,u):m,0!==(c|=0)&&this.emitter.set(r.value,c)):c=0}},Provider.prototype.render=function render(){return this.props.children},Provider}(u.Component);y.childContextTypes=((s={})[h]=c.default.object.isRequired,s);var g=function(l){function Consumer(){var r,s;_classCallCheck(this,Consumer);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return r=s=_possibleConstructorReturn(this,l.call.apply(l,[this].concat(c))),s.state={value:s.getValue()},s.onUpdate=function(r,l){0!==((0|s.observedBits)&l)&&s.setState({value:s.getValue()})},_possibleConstructorReturn(s,r)}return _inherits(Consumer,l),Consumer.prototype.componentWillReceiveProps=function componentWillReceiveProps(r){var l=r.observedBits;this.observedBits=null==l?m:l},Consumer.prototype.componentDidMount=function componentDidMount(){this.context[h]&&this.context[h].on(this.onUpdate);var r=this.props.observedBits;this.observedBits=null==r?m:r},Consumer.prototype.componentWillUnmount=function componentWillUnmount(){this.context[h]&&this.context[h].off(this.onUpdate)},Consumer.prototype.getValue=function getValue(){return this.context[h]?this.context[h].get():r},Consumer.prototype.render=function render(){return function onlyChild(r){return Array.isArray(r)?r[0]:r}(this.props.children)(this.state.value)},Consumer}(u.Component);return g.contextTypes=((v={})[h]=c.default.object,v),{Provider:y,Consumer:g}},r.exports=l.default},32091:r=>{"use strict";r.exports=function(r,l,s,u,c,p,m,v){if(!r){var h;if(void 0===l)h=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var y=[s,u,c,p,m,v],g=0;(h=new Error(l.replace(/%s/g,function(){return y[g++]}))).name="Invariant Violation"}throw h.framesToPop=1,h}}},33791:()=>{},33929:(r,l,s)=>{var u=s(67114),c=s(89736);r.exports=function AsyncIterator(r,l){function n(s,c,p,m){try{var v=r[s](c),h=v.value;return h instanceof u?l.resolve(h.v).then(function(r){n("next",r,p,m)},function(r){n("throw",r,p,m)}):l.resolve(h).then(function(r){v.value=r,p(v)},function(r){return n("throw",r,p,m)})}catch(r){m(r)}}var s;this.next||(c(AsyncIterator.prototype),c(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),c(this,"_invoke",function(r,u,c){function f(){return new l(function(l,s){n(r,c,l,s)})}return s=s?s.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},34444:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Layout;var m=p(s(41594)),v=p(s(80226)),h=p(s(84691)),y=p(s(8188));function Layout(r){var l,s={title:u("Theme Builder","elementor"),titleRedirectRoute:null!==(l=r.titleRedirectRoute)&&void 0!==l?l:null,headerButtons:r.headerButtons,sidebar:m.default.createElement(h.default,{allPartsButton:r.allPartsButton,promotion:r.promotion}),content:r.children};return m.default.createElement(y.default,null,m.default.createElement(v.default,s))}s(27843),Layout.propTypes={headerButtons:c.arrayOf(c.object),allPartsButton:c.element.isRequired,children:c.object.isRequired,promotion:c.bool,titleRedirectRoute:c.string},Layout.defaultProps={headerButtons:[]}},35210:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function Promotion(){var r=elementorAppConfig.promotion.upgrade_url||"https://go.elementor.com/go-pro-theme-builder/",l=function PromotionHoverElement(l){var s="".concat(r,"?type=").concat(l.type);return m.default.createElement(y.default,{className:"e-site-editor__promotion-overlay"},m.default.createElement("a",{className:"e-site-editor__promotion-overlay__link",target:"_blank",rel:"noopener noreferrer",href:s},m.default.createElement("i",{className:"e-site-editor__promotion-overlay__icon eicon-lock"}),m.default.createElement(h.default,{size:"sm",color:"brand",variant:"contained",text:u("Upgrade","elementor")})))};return l.propTypes={className:c.string,type:c.string.isRequired},m.default.createElement(b.default,{allPartsButton:m.default.createElement(v.default,{promotion:!0}),promotion:!0},m.default.createElement("section",{className:"e-site-editor__promotion"},m.default.createElement(g.default,{container:!0,className:"page-header"},m.default.createElement(g.default,{item:!0,sm:7,justify:"end"},m.default.createElement(_.default,{variant:"h1"},u("Customize every part of your site","elementor")),m.default.createElement(w.default,null,u("Get total control, consistency and a faster workflow by designing the recurring parts that make up a complete website like the Header & Footer, Archive, 404, WooCommerce pages and more.","elementor"))),m.default.createElement(g.default,{item:!0,container:!0,justify:"end",alignItems:"start",sm:5},m.default.createElement(h.default,{size:"sm",color:"cta",variant:"contained",url:r,target:"_blank",text:u("Upgrade Now","elementor")}))),m.default.createElement("hr",{className:"eps-separator"}),m.default.createElement(x.default,{hoverElement:l})))};var m=p(s(41594)),v=p(s(76615)),h=p(s(47483)),y=p(s(70097)),g=p(s(3416)),_=p(s(85418)),b=p(s(34444)),x=p(s(4688)),w=p(s(55725));s(69978)},35676:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var p=c(s(41594)),m=c(s(4380)),v=c(s(7229)),h=c(s(18320)),y=c(s(70097)),g=c(s(23074)),_=c(s(16357)),b=c(s(62992));s(45302);var x=p.default.forwardRef(function(r,l){return p.default.createElement("article",{className:"eps-card ".concat(r.className),ref:l},r.children)});x.propTypes={type:u.string,className:u.string,children:u.any},x.defaultProps={className:""},x.displayName="Card",x.Header=m.default,x.Body=v.default,x.Image=h.default,x.Overlay=y.default,x.Footer=g.default,x.Headline=_.default,x.Divider=b.default;l.default=x},36417:r=>{r.exports=function _assertThisInitialized(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r},r.exports.__esModule=!0,r.exports.default=r.exports},37744:(r,l,s)=>{var u=s(78113);r.exports=function _unsupportedIterableToArray(r,l){if(r){if("string"==typeof r)return u(r,l);var s={}.toString.call(r).slice(8,-1);return"Object"===s&&r.constructor&&(s=r.constructor.name),"Map"===s||"Set"===s?Array.from(r):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?u(r,l):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},37868:()=>{},39805:r=>{r.exports=function _classCallCheck(r,l){if(!(r instanceof l))throw new TypeError("Cannot call a class as a function")},r.exports.__esModule=!0,r.exports.default=r.exports},39970:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DropZone;var m=p(s(41594)),v=p(s(78304)),h=s(79397),y=p(s(98718)),g=p(s(59824)),_=p(s(76547)),b=p(s(85418)),x=p(s(55725));function DropZone(r){var l=["e-app-drop-zone",r.className],s={onDrop:function onDrop(l){if(!r.isLoading){var s=l.dataTransfer.files[0];s&&(0,h.isOneOf)(s.type,r.filetypes)?r.onFileSelect(s,l,"drop"):r.onError({id:"file_not_allowed",message:u("This file type is not allowed","elementor")})}}};return m.default.createElement("section",{className:(0,h.arrayToClassName)(l)},m.default.createElement(g.default,(0,v.default)({},s,{isLoading:r.isLoading}),r.icon&&m.default.createElement(_.default,{className:"e-app-drop-zone__icon ".concat(r.icon)}),r.heading&&m.default.createElement(b.default,{variant:"display-3"},r.heading),r.text&&m.default.createElement(x.default,{variant:"xl",className:"e-app-drop-zone__text"},r.text),r.secondaryText&&m.default.createElement(x.default,{variant:"xl",className:"e-app-drop-zone__secondary-text"},r.secondaryText),r.showButton&&m.default.createElement(y.default,{isLoading:r.isLoading,type:r.type,onButtonClick:r.onButtonClick,onFileSelect:r.onFileSelect,onWpMediaSelect:function onWpMediaSelect(l){return r.onWpMediaSelect(l)},onError:function onError(l){return r.onError(l)},text:r.buttonText,filetypes:r.filetypes,variant:r.buttonVariant,color:r.buttonColor,onFileChoose:r.onFileChoose}),r.description&&m.default.createElement(x.default,{variant:"xl",className:"e-app-drop-zone__description"},r.description)))}s(22322),DropZone.propTypes={className:c.string,children:c.any,type:c.string,onFileSelect:c.func.isRequired,onWpMediaSelect:c.func,heading:c.string,text:c.string,secondaryText:c.string,buttonText:c.string,buttonVariant:c.string,buttonColor:c.string,icon:c.string,showButton:c.bool,showIcon:c.bool,isLoading:c.bool,filetypes:c.array.isRequired,onError:c.func,description:c.string,onButtonClick:c.func,onFileChoose:c.func},DropZone.defaultProps={className:"",type:"file-explorer",icon:"eicon-library-upload",showButton:!0,showIcon:!0,onError:function onError(){}}},40362:(r,l,s)=>{"use strict";var u=s(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,r.exports=function(){function shim(r,l,s,c,p,m){if(m!==u){var v=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw v.name="Invariant Violation",v}}function getShim(){return shim}shim.isRequired=shim;var r={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return r.PropTypes=r,r}},40587:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Notice;var p=c(s(41594)),m=s(79397),v=c(s(55725)),h=c(s(76547)),y=c(s(3416));s(26587);var g={danger:"eicon-warning",info:"eicon-info-circle-o",warning:"eicon-warning"};function Notice(r){var l="eps-notice",s=[l,r.className];return r.color&&s.push(l+"-semantic",l+"--"+r.color),p.default.createElement(y.default,{className:(0,m.arrayToClassName)(s),container:!0,noWrap:!0,alignItems:"center",justify:"space-between"},p.default.createElement(y.default,{item:!0,container:!0,alignItems:"start",noWrap:!0},r.withIcon&&r.color&&p.default.createElement(h.default,{className:(0,m.arrayToClassName)(["eps-notice__icon",g[r.color]])}),p.default.createElement(v.default,{variant:"xs",className:"eps-notice__text"},r.label&&p.default.createElement("strong",null,r.label+" "),r.children)),r.button&&p.default.createElement(y.default,{item:!0,container:!0,justify:"end",className:l+"__button-container"},r.button))}Notice.propTypes={className:u.string,color:u.string,label:u.string,children:u.any.isRequired,icon:u.string,withIcon:u.bool,button:u.object},Notice.defaultProps={className:"",withIcon:!0,button:null}},40989:(r,l,s)=>{var u=s(45498);function _defineProperties(r,l){for(var s=0;s<l.length;s++){var c=l[s];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(r,u(c.key),c)}}r.exports=function _createClass(r,l,s){return l&&_defineProperties(r.prototype,l),s&&_defineProperties(r,s),Object.defineProperty(r,"prototype",{writable:!1}),r},r.exports.__esModule=!0,r.exports.default=r.exports},41494:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function useQueryParams(){var r,l=new URLSearchParams(window.location.search),s=Object.fromEntries(l.entries()),u=null===(r=location.hash.match(/\?(.+)/))||void 0===r?void 0:r[1],c={};u&&u.split("&").forEach(function(r){var l=r.split("="),s=(0,p.default)(l,2),u=s[0],m=s[1];c[u]=m});var m=_objectSpread(_objectSpread({},s),c);return{getAll:function getAll(){return m}}};var c=u(s(85707)),p=u(s(18821));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,c.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}},41594:r=>{"use strict";r.exports=React},41621:(r,l,s)=>{var u=s(14718);function _get(){return r.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(r,l,s){var c=u(r,l);if(c){var p=Object.getOwnPropertyDescriptor(c,l);return p.get?p.get.call(arguments.length<3?r:s):p.value}},r.exports.__esModule=!0,r.exports.default=r.exports,_get.apply(null,arguments)}r.exports=_get,r.exports.__esModule=!0,r.exports.default=r.exports},42209:(r,l,s)=>{"use strict";var u=s(41594),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var p=c(s(39805)),m=c(s(40989)),v=c(s(85707)),h=new(function(){return(0,m.default)(function Router(){(0,p.default)(this,Router),(0,v.default)(this,"routes",[]),(0,v.default)(this,"history",null)},[{key:"addRoute",value:function addRoute(r){this.routes.push(r)}},{key:"getRoutes",value:function getRoutes(){return this.routes.map(function(r){var l=r.props||{};return l.path=l.key=r.path,u.createElement(r.component,l)})}}])}());window.elementorAppPackages={router:h};l.default=h},43496:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var c=u(s(39805)),p=u(s(40989)),m=u(s(15118)),v=u(s(29402)),h=u(s(41621)),y=u(s(87861));s(54792);var g=u(s(47483));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}function _superPropGet(r,l,s,u){var c=(0,h.default)((0,v.default)(1&u?r.prototype:r),l,s);return 2&u&&"function"==typeof c?function(r){return c.apply(s,r)}:c}l.default=function(r){function SideMenuItem(){return(0,c.default)(this,SideMenuItem),function _callSuper(r,l,s){return l=(0,v.default)(l),(0,m.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,v.default)(r).constructor):l.apply(r,s))}(this,SideMenuItem,arguments)}return(0,y.default)(SideMenuItem,r),(0,p.default)(SideMenuItem,[{key:"getCssId",value:function getCssId(){return"eps-menu-item-"+_superPropGet(SideMenuItem,"getCssId",this,3)([])}},{key:"getClassName",value:function getClassName(){return"eps-menu-item "+_superPropGet(SideMenuItem,"getClassName",this,3)([])}}])}(g.default)},44969:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=SitePart;var p=c(s(41594)),m=c(s(35676)),v=c(s(4380)),h=c(s(7229)),y=c(s(18320)),g=c(s(85418));function SitePart(r){return p.default.createElement(m.default,{className:"e-site-part"},p.default.createElement(v.default,null,p.default.createElement(g.default,{tag:"h1",variant:"text-sm",className:"eps-card__headline"},r.title),r.actionButton),p.default.createElement(h.default,null,p.default.createElement(y.default,{alt:r.title,src:r.thumbnail},r.children)))}s(86161),SitePart.propTypes={thumbnail:u.string.isRequired,title:u.string.isRequired,children:u.object,showIndicator:u.bool,actionButton:u.object}},45302:()=>{},45498:(r,l,s)=>{var u=s(10564).default,c=s(11327);r.exports=function toPropertyKey(r){var l=c(r,"string");return"symbol"==u(l)?l:l+""},r.exports.__esModule=!0,r.exports.default=r.exports},45735:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=ListItem;var p=c(s(41594)),m=s(79397);function ListItem(r){var l,s="eps-list__item",u=[s,r.className];return Object.prototype.hasOwnProperty.call(r,"padding")&&(l={"--eps-list-item-padding":(0,m.pxToRem)(r.padding)},u.push(s+"--padding")),p.default.createElement("li",{style:l,className:(0,m.arrayToClassName)(u)},r.children)}ListItem.propTypes={className:u.string,padding:u.string,children:u.any.isRequired},ListItem.defaultProps={className:""}},45744:()=>{},46313:(r,l,s)=>{var u=s(9535),c=s(33929);r.exports=function _regeneratorAsyncGen(r,l,s,p,m){return new c(u().w(r,l,s,p),m||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},46361:(r,l)=>{"use strict";Object.defineProperty(l,"__esModule",{value:!0}),l.default=function useAction(){return{backToDashboard:function backToDashboard(){window.top===window?window.top.location=elementorAppConfig.admin_url:window.top.$e.run("app/close")},backToReferrer:function backToReferrer(){window.top===window?window.top.location=elementorAppConfig.return_url.includes(elementorAppConfig.login_url)?elementorAppConfig.admin_url:elementorAppConfig.return_url:window.top.$e.run("app/close")}}}},47483:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var p=c(s(41594)),m=c(s(78304)),v=c(s(39805)),h=c(s(40989)),y=c(s(15118)),g=c(s(29402)),_=c(s(87861)),b=c(s(85707)),x=s(83040),w=c(s(47485)),P=c(s(76547));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var C=l.default=function(r){function Button(){return(0,v.default)(this,Button),function _callSuper(r,l,s){return l=(0,g.default)(l),(0,y.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,g.default)(r).constructor):l.apply(r,s))}(this,Button,arguments)}return(0,_.default)(Button,r),(0,h.default)(Button,[{key:"getCssId",value:function getCssId(){return this.props.id}},{key:"getClassName",value:function getClassName(){var r="eps-button";return[r,this.props.className].concat(this.getStylePropsClasses(r)).filter(function(r){return""!==r}).join(" ")}},{key:"getStylePropsClasses",value:function getStylePropsClasses(r){var l=this,s=[];return["color","size","variant"].forEach(function(u){var c=l.props[u];c&&s.push(r+"--"+c)}),s}},{key:"getIcon",value:function getIcon(){if(this.props.icon){var r=this.props.tooltip||this.props.text,l=p.default.createElement(P.default,{className:this.props.icon,"aria-hidden":"true",title:r}),s="";return this.props.hideText&&(s=p.default.createElement("span",{className:"sr-only"},r)),p.default.createElement(p.default.Fragment,null,l,s)}return""}},{key:"getText",value:function getText(){return this.props.hideText?"":p.default.createElement("span",null,this.props.text)}},{key:"render",value:function render(){var r={},l=this.getCssId(),s=this.getClassName();l&&(r.id=l),s&&(r.className=s),this.props.onClick&&(r.onClick=this.props.onClick),this.props.rel&&(r.rel=this.props.rel),this.props.elRef&&(r.ref=this.props.elRef);var u=p.default.createElement(p.default.Fragment,null,this.getIcon(),this.getText());return this.props.url?0===this.props.url.indexOf("http")?p.default.createElement("a",(0,m.default)({href:this.props.url,target:this.props.target},r),u):(r.getProps=function(l){return l.isCurrent&&(r.className+=" active"),{className:r.className}},p.default.createElement(x.LocationProvider,{history:w.default.appHistory},p.default.createElement(x.Link,(0,m.default)({to:this.props.url},r),u))):p.default.createElement("div",r,u)}}])}(p.default.Component);(0,b.default)(C,"propTypes",{text:u.string.isRequired,hideText:u.bool,icon:u.string,tooltip:u.string,id:u.string,className:u.string,url:u.string,onClick:u.func,variant:u.oneOf(["contained","underlined","outlined",""]),color:u.oneOf(["primary","secondary","cta","link","disabled"]),size:u.oneOf(["sm","md","lg"]),target:u.string,rel:u.string,elRef:u.object}),(0,b.default)(C,"defaultProps",{id:"",className:"",variant:"",target:"_parent"})},47485:r=>{"use strict";r.exports=elementorAppPackages.router},47579:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Checkbox;var p=c(s(41594)),m=s(79397);function Checkbox(r){var l=r.className,s=r.checked,u=r.rounded,c=r.indeterminate,v=r.error,h=r.disabled,y=r.onChange,g=r.id,_="eps-checkbox",b=[_,l];return u&&b.push(_+"--rounded"),c&&b.push(_+"--indeterminate"),v&&b.push(_+"--error"),p.default.createElement("input",{className:(0,m.arrayToClassName)(b),type:"checkbox",checked:s,disabled:h,onChange:y,id:g})}s(33791),Checkbox.propTypes={className:u.string,checked:u.bool,disabled:u.bool,indeterminate:u.bool,rounded:u.bool,error:u.bool,onChange:u.func,id:u.string},Checkbox.defaultProps={className:"",checked:null,disabled:!1,indeterminate:!1,error:!1,onChange:function onChange(){}}},48590:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Popover;var p=c(s(41594));function Popover(r){return p.default.createElement(p.default.Fragment,null,p.default.createElement("div",{className:"eps-popover__background",onClick:r.closeFunction}),p.default.createElement("ul",{className:"eps-popover ".concat(function getArrowPositionClass(){switch(r.arrowPosition){case"start":return"eps-popover--arrow-start";case"end":return"eps-popover--arrow-end";case"none":return"eps-popover--arrow-none";default:return"eps-popover--arrow-center"}}()," ").concat(r.className),onClick:r.closeFunction},r.children))}s(45744),Popover.propTypes={children:u.any.isRequired,className:u.string,closeFunction:u.func,arrowPosition:u.oneOf(["start","center","end","none"])},Popover.defaultProps={className:"",arrowPosition:"center"}},49194:()=>{},49477:(r,l,s)=>{"use strict";l.__esModule=!0;var u=_interopRequireDefault(s(41594)),c=_interopRequireDefault(s(31659));function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}}l.default=u.default.createContext||c.default,r.exports=l.default},51776:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogContent;var p=c(s(41594));function DialogContent(r){return p.default.createElement("div",{className:"eps-dialog__content"},r.children)}DialogContent.propTypes={children:u.any}},51959:()=>{},53051:(r,l,s)=>{var u=s(67114),c=s(9535),p=s(62507),m=s(46313),v=s(33929),h=s(95315),y=s(66961);function _regeneratorRuntime(){"use strict";var l=c(),s=l.m(_regeneratorRuntime),g=(Object.getPrototypeOf?Object.getPrototypeOf(s):s.__proto__).constructor;function n(r){var l="function"==typeof r&&r.constructor;return!!l&&(l===g||"GeneratorFunction"===(l.displayName||l.name))}var _={throw:1,return:2,break:3,continue:3};function a(r){var l,s;return function(u){l||(l={stop:function stop(){return s(u.a,2)},catch:function _catch(){return u.v},abrupt:function abrupt(r,l){return s(u.a,_[r],l)},delegateYield:function delegateYield(r,c,p){return l.resultName=c,s(u.d,y(r),p)},finish:function finish(r){return s(u.f,r)}},s=function t(r,s,c){u.p=l.prev,u.n=l.next;try{return r(s,c)}finally{l.next=u.n}}),l.resultName&&(l[l.resultName]=u.v,l.resultName=void 0),l.sent=u.v,l.next=u.n;try{return r.call(this,l)}finally{u.p=l.prev,u.n=l.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,s,u,c){return l.w(a(r),s,u,c&&c.reverse())},isGeneratorFunction:n,mark:l.m,awrap:function awrap(r,l){return new u(r,l)},AsyncIterator:v,async:function async(r,l,s,u,c){return(n(l)?m:p)(a(r),l,s,u,c)},keys:h,values:y}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},53441:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784),m=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=UnfilteredFilesDialog;var v=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,p,v={__proto__:null,default:r};if(null===r||"object"!=m(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((p=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(p.get||p.set)?c(v,h,p):v[h]=r[h]);return v}(r,l)}(s(41594)),h=p(s(18821)),y=p(s(15656)),g=p(s(73921));function UnfilteredFilesDialog(r){var l=r.show,s=r.setShow,c=r.onReady,p=r.onCancel,m=r.onDismiss,_=r.onLoad,b=r.onEnable,x=r.onClose,w=(0,g.default)(),P=w.ajaxState,C=w.setAjax,O=(0,v.useState)(!1),j=(0,h.default)(O,2),E=j[0],N=j[1],T=(0,v.useState)(!1),R=(0,h.default)(T,2),M=R[0],S=R[1];return(0,v.useEffect)(function(){E&&(s(!1),C({data:{action:"elementor_ajax",actions:JSON.stringify({enable_unfiltered_files_upload:{action:"enable_unfiltered_files_upload"}})}}),b&&b())},[E]),(0,v.useEffect)(function(){switch(P.status){case"success":c();break;case"error":S(!0),s(!0)}},[P]),(0,v.useEffect)(function(){l&&_&&_()},[l]),l?v.default.createElement(v.default.Fragment,null,M?v.default.createElement(y.default,{title:u("Something went wrong.","elementor"),text:r.errorModalText,approveButtonColor:"link",approveButtonText:u("Continue","elementor"),approveButtonOnClick:c,dismissButtonText:u("Go Back","elementor"),dismissButtonOnClick:p,onClose:p}):v.default.createElement(y.default,{title:u("First, enable unfiltered file uploads.","elementor"),text:r.confirmModalText,approveButtonColor:"link",approveButtonText:u("Enable","elementor"),approveButtonOnClick:function approveButtonOnClick(){return N(!0)},dismissButtonText:u("Skip","elementor"),dismissButtonOnClick:m||c,onClose:x||m||c})):null}UnfilteredFilesDialog.propTypes={show:c.bool,setShow:c.func.isRequired,onReady:c.func.isRequired,onCancel:c.func.isRequired,onDismiss:c.func,confirmModalText:c.string.isRequired,errorModalText:c.string.isRequired,onLoad:c.func,onEnable:c.func,onClose:c.func},UnfilteredFilesDialog.defaultProps={show:!1,onReady:function onReady(){},onCancel:function onCancel(){}}},54792:()=>{},54902:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogWrapper;var m=p(s(41594)),v=p(s(47483));function DialogWrapper(r){var l="div";return r.onSubmit&&(l="form"),m.default.createElement("section",{className:"eps-modal__overlay"},m.default.createElement(l,{className:"eps-modal eps-dialog",onSubmit:r.onSubmit},r.onClose&&m.default.createElement(v.default,{onClick:r.onClose,text:u("Close","elementor"),hideText:!0,icon:"eicon-close",className:"eps-dialog__close-button"}),r.children))}DialogWrapper.propTypes={onClose:c.func,onSubmit:c.func,children:c.any}},54999:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=InlineLink;var p=c(s(41594)),m=s(83040),v=c(s(47485)),h=s(79397);function InlineLink(r){var l="eps-inline-link",s=[l,"".concat(l,"--color-").concat(r.color),"none"!==r.underline?"".concat(l,"--underline-").concat(r.underline):"",r.italic?"".concat(l,"--italic"):"",r.className],u=(0,h.arrayToClassName)(s);return r.url?r.url.includes("http")?function getExternalLink(){return p.default.createElement("a",{href:r.url,target:r.target,rel:r.rel,className:u,onClick:r.onClick},r.children)}():function getRouterLink(){return p.default.createElement(m.LocationProvider,{history:v.default.appHistory},p.default.createElement(m.Link,{to:r.url,className:u},r.children))}():function getActionLink(){return p.default.createElement("button",{className:u,onClick:r.onClick},r.children)}()}s(72701),InlineLink.propTypes={className:u.string,children:u.any,url:u.string,target:u.string,rel:u.string,text:u.string,color:u.oneOf(["primary","secondary","cta","link","disabled"]),underline:u.oneOf(["none","hover","always"]),italic:u.bool,onClick:u.func},InlineLink.defaultProps={className:"",color:"link",underline:"always",target:"_blank",rel:"noopener noreferrer"}},55198:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Footer;var p=c(s(41594));function Footer(r){return p.default.createElement("footer",{className:"eps-app__footer"},r.children)}Footer.propTypes={children:u.object}},55725:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Text;var p=c(s(41594)),m=s(79397);function Text(r){var l=[r.className],s=r.variant&&"md"!==r.variant?"-"+r.variant:"";l.push("eps-text"+s);var u=function Element(){return p.default.createElement(r.tag,{className:(0,m.arrayToClassName)(l)},r.children)};return p.default.createElement(u,null)}Text.propTypes={className:u.string,variant:u.oneOf(["xl","lg","md","sm","xs","xxs"]),tag:u.string,children:u.any.isRequired},Text.defaultProps={className:"",tag:"p"}},56441:r=>{"use strict";r.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},57463:()=>{},58155:r=>{function asyncGeneratorStep(r,l,s,u,c,p,m){try{var v=r[p](m),h=v.value}catch(r){return void s(r)}v.done?l(h):Promise.resolve(h).then(u,c)}r.exports=function _asyncToGenerator(r){return function(){var l=this,s=arguments;return new Promise(function(u,c){var p=r.apply(l,s);function _next(r){asyncGeneratorStep(p,u,c,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(p,u,c,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},59250:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DialogText;var c=u(s(41594)),p=u(s(85707)),m=u(s(78304)),v=u(s(55725));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,p.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}function DialogText(r){return c.default.createElement(v.default,(0,m.default)({variant:"xs"},r,{className:"eps-dialog__text ".concat(r.className)}))}DialogText.propTypes=_objectSpread({},v.default.propTypes),DialogText.defaultProps=_objectSpread(_objectSpread({},v.default.defaultProps),{},{tag:"p",variant:"sm"})},59824:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784),p=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=DragDrop;var m=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,m,v={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((m=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(m.get||m.set)?c(v,h,m):v[h]=r[h]);return v}(r,l)}(s(41594)),v=c(s(78304)),h=c(s(18821)),y=s(79397);function DragDrop(r){var l=(0,m.useState)(!1),s=(0,h.default)(l,2),u=s[0],c=s[1],p=function onDragDropActions(r){r.preventDefault(),r.stopPropagation()},g={onDrop:function onDrop(l){p(l),c(!1),r.onDrop&&r.onDrop(l)},onDragOver:function onDragOver(l){p(l),c(!0),r.onDragOver&&r.onDragOver(l)},onDragLeave:function onDragLeave(l){p(l),c(!1),r.onDragLeave&&r.onDragLeave(l)}};return m.default.createElement("div",(0,v.default)({},g,{className:function getClassName(){var l="e-app-drag-drop",s=[l,r.className];return u&&!r.isLoading&&s.push(l+"--drag-over"),(0,y.arrayToClassName)(s)}()}),r.children)}s(49194),DragDrop.propTypes={className:u.string,children:u.any,onDrop:u.func,onDragLeave:u.func,onDragOver:u.func,isLoading:u.bool},DragDrop.defaultProps={className:""}},61678:(r,l,s)=>{"use strict";var u=s(62688),c=s(12470).__,p=s(96784),m=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.Modal=void 0,l.default=ModalProvider;var v=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,p,v={__proto__:null,default:r};if(null===r||"object"!=m(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((p=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(p.get||p.set)?c(v,h,p):v[h]=r[h]);return v}(r,l)}(s(41594)),h=p(s(78304)),y=p(s(85707)),g=p(s(18821)),_=s(79397),b=p(s(47483)),x=p(s(3416)),w=p(s(76547)),P=p(s(55725)),C=p(s(63980)),O=p(s(2526));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,y.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}function ModalProvider(r){var l=(0,v.useState)(r.show),s=(0,g.default)(l,2),u=s[0],c=s[1],p=function showModal(){c(!0),r.setShow&&r.setShow(!0)},m=_objectSpread(_objectSpread({},r),{},{show:u,hideModal:function hideModal(){c(!1),r.setShow&&r.setShow(!1)},showModal:p});return(0,v.useEffect)(function(){c(r.show)},[r.show]),v.default.createElement(v.default.Fragment,null,r.toggleButtonProps&&v.default.createElement(b.default,(0,h.default)({},r.toggleButtonProps,{onClick:p})),v.default.createElement(j,m,r.children))}s(97088),ModalProvider.propTypes={children:u.node.isRequired,toggleButtonProps:u.object,title:u.string,icon:u.string,show:u.bool,setShow:u.func,onOpen:u.func,onClose:u.func},ModalProvider.defaultProps={show:!1},ModalProvider.Section=C.default,ModalProvider.Tip=O.default;var j=l.Modal=function Modal(r){var l=(0,v.useRef)(null),s=(0,v.useRef)(null),u=function closeModal(u){var c=l.current,p=s.current,m=p&&p.contains(u.target);c&&c.contains(u.target)&&!m||(r.hideModal(),r.onClose&&r.onClose(u))};return(0,v.useEffect)(function(){var l;r.show&&(document.addEventListener("mousedown",u,!1),null===(l=r.onOpen)||void 0===l||l.call(r));return function(){return document.removeEventListener("mousedown",u,!1)}},[r.show]),r.show?v.default.createElement("div",{className:"eps-modal__overlay",onClick:u},v.default.createElement("div",{className:(0,_.arrayToClassName)(["eps-modal",r.className]),ref:l},v.default.createElement(x.default,{container:!0,className:"eps-modal__header",justify:"space-between",alignItems:"center"},v.default.createElement(x.default,{item:!0},v.default.createElement(w.default,{className:"eps-modal__icon ".concat(r.icon)}),v.default.createElement(P.default,{className:"title",tag:"span"},r.title)),v.default.createElement(x.default,{item:!0},v.default.createElement("div",{className:"eps-modal__close-wrapper",ref:s},v.default.createElement(b.default,{text:c("Close","elementor"),hideText:!0,icon:"eicon-close",onClick:r.closeModal})))),v.default.createElement("div",{className:"eps-modal__body"},r.children))):null};j.propTypes={className:u.string,children:u.any.isRequired,title:u.string.isRequired,icon:u.string,show:u.bool,setShow:u.func,hideModal:u.func,showModal:u.func,closeModal:u.func,onOpen:u.func,onClose:u.func},j.defaultProps={className:""}},61790:(r,l,s)=>{var u=s(53051)();r.exports=u;try{regeneratorRuntime=u}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=u:Function("r","regeneratorRuntime = r")(u)}},62507:(r,l,s)=>{var u=s(46313);r.exports=function _regeneratorAsync(r,l,s,c,p){var m=u(r,l,s,c,p);return m.next().then(function(r){return r.done?r.value:m.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},62521:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CollapseContent;var p=c(s(41594));function CollapseContent(r){return p.default.createElement("div",{className:"e-app-collapse-content"},r.children)}CollapseContent.propTypes={className:u.string,children:u.any},CollapseContent.defaultProps={className:""}},62688:(r,l,s)=>{r.exports=s(40362)()},62967:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function NotFound(){var r=p.default.useMemo(function(){var r;return(null===(r=elementorAppConfig.menu_url.split("#"))||void 0===r?void 0:r[1])||"/site-editor"},[]);return p.default.createElement(m.default,{title:u("Theme Builder could not be loaded","elementor"),text:u("We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.","elementor"),approveButtonUrl:"https://go.elementor.com/app-theme-builder-load-issue/",approveButtonColor:"link",approveButtonTarget:"_blank",approveButtonText:u("Learn More","elementor"),dismissButtonText:u("Go Back","elementor"),dismissButtonUrl:r})};var p=c(s(41594)),m=c(s(15656))},62992:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardDivider;var p=c(s(41594)),m=s(79397);function CardDivider(r){var l=["eps-card__divider",r.className];return p.default.createElement("hr",{className:(0,m.arrayToClassName)(l)})}s(45302),CardDivider.propTypes={className:u.string},CardDivider.defaultProps={className:""}},63980:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=ModalSection;var p=c(s(41594)),m=s(79397);function ModalSection(r){return p.default.createElement("section",{className:(0,m.arrayToClassName)(["eps-modal__section",r.className])},r.children)}ModalSection.propTypes={className:u.string,children:u.any},ModalSection.defaultProps={className:""}},65474:r=>{r.exports=function _iterableToArrayLimit(r,l){var s=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=s){var u,c,p,m,v=[],h=!0,y=!1;try{if(p=(s=s.call(r)).next,0===l){if(Object(s)!==s)return;h=!1}else for(;!(h=(u=p.call(s)).done)&&(v.push(u.value),v.length!==l);h=!0);}catch(r){y=!0,c=r}finally{try{if(!h&&null!=s.return&&(m=s.return(),Object(m)!==m))return}finally{if(y)throw c}}return v}},r.exports.__esModule=!0,r.exports.default=r.exports},65949:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var m=p(s(41594)),v=p(s(39805)),h=p(s(40989)),y=p(s(15118)),g=p(s(29402)),_=p(s(87861)),b=p(s(85707)),x=p(s(15656));function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}var w=l.default=function(r){function ErrorBoundary(r){var l;return(0,v.default)(this,ErrorBoundary),(l=function _callSuper(r,l,s){return l=(0,g.default)(l),(0,y.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,g.default)(r).constructor):l.apply(r,s))}(this,ErrorBoundary,[r])).state={hasError:null},l}return(0,_.default)(ErrorBoundary,r),(0,h.default)(ErrorBoundary,[{key:"goBack",value:function goBack(){window.top!==window.self&&window.top.$e.run("app/close"),window.location=elementorAppConfig.return_url}},{key:"render",value:function render(){return this.state.hasError?m.default.createElement(x.default,{title:this.props.title,text:this.props.text,approveButtonUrl:this.props.learnMoreUrl,approveButtonColor:"link",approveButtonTarget:"_blank",approveButtonText:u("Learn More","elementor"),dismissButtonText:u("Go Back","elementor"),dismissButtonOnClick:this.goBack}):this.props.children}}],[{key:"getDerivedStateFromError",value:function getDerivedStateFromError(){return{hasError:!0}}}])}(m.default.Component);(0,b.default)(w,"propTypes",{children:c.any,title:c.string,text:c.string,learnMoreUrl:c.string}),(0,b.default)(w,"defaultProps",{title:u("App could not be loaded","elementor"),text:u("We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.","elementor"),learnMoreUrl:"https://go.elementor.com/app-general-load-issue/"})},66848:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var c=u(s(76615)),p=u(s(34444)),m=u(s(81286)),v=u(s(62967)),h=u(s(4688)),y=u(s(44969)),g=s(8188);l.default={AllPartsButton:c.default,Layout:p.default,Module:m.default,NotFound:v.default,SitePart:y.default,SiteParts:h.default,TemplateTypesContext:g.Context}},66961:(r,l,s)=>{var u=s(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var l=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],s=0;if(l)return l.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&s>=r.length&&(r=void 0),{value:r&&r[s++],done:!r}}}}throw new TypeError(u(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,l){this.v=r,this.k=l},r.exports.__esModule=!0,r.exports.default=r.exports},69783:(r,l,s)=>{"use strict";var u=s(62688),c=s(12470).__,p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=GoProButton;var m=p(s(41594)),v=p(s(78304)),h=p(s(47483)),y=s(79397);function GoProButton(r){var l=["e-app-go-pro-button",r.className];return m.default.createElement(h.default,(0,v.default)({},r,{className:(0,y.arrayToClassName)(l),text:r.text}))}GoProButton.propTypes={className:u.string,text:u.string},GoProButton.defaultProps={className:"",variant:"outlined",size:"sm",color:"cta",target:"_blank",rel:"noopener noreferrer",text:c("Upgrade Now","elementor")}},69978:()=>{},70097:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CardOverlay;var p=c(s(41594));function CardOverlay(r){return p.default.createElement("div",{className:"eps-card__image-overlay ".concat(r.className)},r.children)}s(45302),CardOverlay.propTypes={className:u.string,children:u.object.isRequired},CardOverlay.defaultProps={className:""}},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},70692:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CssGrid;var p=c(s(41594)),m=s(79397);function CssGrid(r){var l={"--eps-grid-columns":r.columns,"--eps-grid-spacing":(0,m.pxToRem)(r.spacing),"--eps-grid-col-min-width":(0,m.pxToRem)(r.colMinWidth),"--eps-grid-col-max-width":(0,m.pxToRem)(r.colMaxWidth)};return p.default.createElement("div",{style:l,className:"eps-css-grid ".concat(r.className)},r.children)}s(16944),CssGrid.propTypes={className:u.string,children:u.any.isRequired,columns:u.number,spacing:u.number,colMinWidth:u.number,colMaxWidth:u.number},CssGrid.defaultProps={spacing:24,className:""}},72701:()=>{},73921:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=function useAjax(){var r=(0,h.useState)(null),l=(0,v.default)(r,2),s=l[0],u=l[1],p="initial",y={status:p,isComplete:!1,response:null},g=(0,h.useState)(y),_=(0,v.default)(g,2),b=_[0],x=_[1],w={reset:function reset(){return x(p)}},P=function(){var r=(0,m.default)(c.default.mark(function _callee(r){return c.default.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.abrupt("return",new Promise(function(l,s){var u=new FormData;if(r.data){for(var c in r.data)u.append(c,r.data[c]);r.data.nonce||u.append("_nonce",elementorCommon.config.ajax.nonce)}var p=_objectSpread(_objectSpread({type:"post",url:elementorCommon.config.ajax.url,headers:{},cache:!1,contentType:!1,processData:!1},r),{},{data:u,success:function success(r){l(r)},error:function error(r){s(r)}});jQuery.ajax(p)}));case 1:case"end":return l.stop()}},_callee)}));return function runRequest(l){return r.apply(this,arguments)}}();return(0,h.useEffect)(function(){s&&P(s).then(function(r){var l=r.success?"success":"error";x(function(s){return _objectSpread(_objectSpread({},s),{},{status:l,response:null==r?void 0:r.data})})}).catch(function(r){var l,s=408===r.status?"timeout":null===(l=r.responseJSON)||void 0===l?void 0:l.data;x(function(r){return _objectSpread(_objectSpread({},r),{},{status:"error",response:s})})}).finally(function(){x(function(r){return _objectSpread(_objectSpread({},r),{},{isComplete:!0})})})},[s]),{ajax:s,setAjax:u,ajaxState:b,ajaxActions:w,runRequest:P}};var c=u(s(61790)),p=u(s(85707)),m=u(s(58155)),v=u(s(18821)),h=s(41594);function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,p.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}},76547:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Icon;var p=c(s(41594));function Icon(r){return p.default.createElement("i",{className:"eps-icon ".concat(r.className)})}Icon.propTypes={className:u.string.isRequired},Icon.defaultProps={className:""}},76615:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=AllPartsButton;var m=p(s(41594)),v=p(s(43496)),h=s(83040);function AllPartsButton(r){return m.default.createElement(h.Match,{path:"/site-editor/templates"},function(l){var s=l.match,c="eps-menu-item__link".concat(s||r.promotion?" eps-menu-item--active":"");return m.default.createElement(v.default,{text:u("All Parts","elementor"),className:c,icon:"eicon-filter",url:r.url})})}AllPartsButton.propTypes={url:c.string,promotion:c.bool}},77879:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.CollapseContext=void 0;var c=u(s(41594));l.CollapseContext=c.default.createContext()},78113:r=>{r.exports=function _arrayLikeToArray(r,l){(null==l||l>r.length)&&(l=r.length);for(var s=0,u=Array(l);s<l;s++)u[s]=r[s];return u},r.exports.__esModule=!0,r.exports.default=r.exports},78304:r=>{function _extends(){return r.exports=_extends=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var u in s)({}).hasOwnProperty.call(s,u)&&(r[u]=s[u])}return r},r.exports.__esModule=!0,r.exports.default=r.exports,_extends.apply(null,arguments)}r.exports=_extends,r.exports.__esModule=!0,r.exports.default=r.exports},78687:r=>{r.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},79397:(r,l,s)=>{"use strict";var u=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.stringToRemValues=l.rgbToHex=l.pxToRem=l.isOneOf=l.arrayToObjectByKey=l.arrayToClassName=void 0;var c=u(s(10564)),p=l.pxToRem=function pxToRem(r){if(r)return"string"!=typeof r&&(r=r.toString()),r.split(" ").map(function(r){return"".concat(.0625*r,"rem")}).join(" ")};l.arrayToClassName=function arrayToClassName(r,l){return r.filter(function(r){return"object"===(0,c.default)(r)?Object.entries(r)[0][1]:r}).map(function(r){var s="object"===(0,c.default)(r)?Object.entries(r)[0][0]:r;return l?l(s):s}).join(" ")},l.stringToRemValues=function stringToRemValues(r){return r.split(" ").map(function(r){return p(r)}).join(" ")},l.rgbToHex=function rgbToHex(r,l,s){return"#"+[r,l,s].map(function(r){var l=r.toString(16);return 1===l.length?"0"+l:l}).join("")},l.isOneOf=function isOneOf(r,l){return l.some(function(l){return r.includes(l)})},l.arrayToObjectByKey=function arrayToObjectByKey(r,l){var s={};return r.forEach(function(r){return s[r[l]]=r}),s}},79514:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784),p=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=CollapseToggle;var m=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,m,v={__proto__:null,default:r};if(null===r||"object"!=p(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((m=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(m.get||m.set)?c(v,h,m):v[h]=r[h]);return v}(r,l)}(s(41594)),v=c(s(85707)),h=s(79397),y=s(77879);function CollapseToggle(r){var l=(0,m.useContext)(y.CollapseContext),s={"--e-app-collapse-toggle-icon-spacing":(0,h.pxToRem)(r.iconSpacing)},u="e-app-collapse-toggle",c=[u,(0,v.default)({},u+"--active",r.active)],p={style:s,className:(0,h.arrayToClassName)(c)};return r.active&&(p.onClick=function(){return l.toggle()}),m.default.createElement("div",p,r.children,r.active&&r.showIcon&&m.default.createElement("i",{className:"eicon-caret-down e-app-collapse-toggle__icon"}))}CollapseToggle.propTypes={className:u.string,iconSpacing:u.number,showIcon:u.bool,active:u.bool,children:u.any},CollapseToggle.defaultProps={className:"",iconSpacing:20,showIcon:!0,active:!0}},80226:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Page;var p=c(s(41594)),m=c(s(14888)),v=c(s(24017)),h=c(s(25368)),y=c(s(55198));function Page(r){return p.default.createElement("div",{className:"eps-app__lightbox ".concat(r.className)},p.default.createElement("div",{className:"eps-app"},p.default.createElement(m.default,{title:r.title,buttons:r.headerButtons,titleRedirectRoute:r.titleRedirectRoute,onClose:r.onClose}),p.default.createElement("div",{className:"eps-app__main"},function AppSidebar(){if(r.sidebar)return p.default.createElement(v.default,null,r.sidebar)}(),p.default.createElement(h.default,null,r.content)),function AppFooter(){if(r.footer)return p.default.createElement(y.default,null,r.footer)}()))}Page.propTypes={title:u.string,titleRedirectRoute:u.string,className:u.string,headerButtons:u.arrayOf(u.object),sidebar:u.object,content:u.object.isRequired,footer:u.object,onClose:u.func},Page.defaultProps={className:""}},80791:(r,l,s)=>{"use strict";var u=s(12470).__;Object.defineProperty(l,"__esModule",{value:!0}),l.default=function usePageTitle(r){var l=r.title,s=r.prefix;(0,c.useEffect)(function(){s||(s=u("Elementor","elementor")),document.title="".concat(s," | ").concat(l)},[l,s])};var c=s(41594)},81286:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var p=c(s(39805)),m=c(s(40989)),v=c(s(47485)),h=c(s(35210)),y=c(s(62967));l.default=function(){return(0,m.default)(function SiteEditor(){(0,p.default)(this,SiteEditor),this.saveTemplateTypesToCache(),v.default.addRoute({path:"/site-editor/promotion",component:h.default}),v.default.addRoute({path:"/site-editor/*",component:y.default})},[{key:"saveTemplateTypesToCache",value:function saveTemplateTypesToCache(){var r=this.getTypes();elementorCommon.ajax.addRequestCache({unique_id:"app_site_editor_template_types"},r)}},{key:"getTypes",value:function getTypes(){return[{type:"header",icon:"eicon-header",title:u("Header","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/header.svg"},tooltip_data:{title:u("What is a Header Template?","elementor"),content:u("The header template allows you to easily design and edit custom WordPress headers so you are no longer constrained by your theme’s header design limitations.","elementor"),tip:u("You can create multiple headers, and assign each to different areas of your site.","elementor"),docs:"https://go.elementor.com/app-theme-builder-header/",video_url:"https://www.youtube.com/embed/HHy5RK6W-6I"}},{type:"footer",icon:"eicon-footer",title:u("Footer","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/footer.svg"},tooltip_data:{title:u("What is a Footer Template?","elementor"),content:u("The footer template allows you to easily design and edit custom WordPress footers without the limits of your theme’s footer design constraints","elementor"),tip:u("You can create multiple footers, and assign each to different areas of your site.","elementor"),docs:"https://go.elementor.com/app-theme-builder-footer/",video_url:"https://www.youtube.com/embed/xa8DoR4tQrY"}},{type:"single-page",icon:"eicon-single-page",title:u("Single Page","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/single-page.svg"},tooltip_data:{title:u("What is a Single Page Template?","elementor"),content:u("A single page template allows you to easily create the layout and style of pages, ensuring design consistency across all the pages of your site.","elementor"),tip:u("You can create multiple single page templates, and assign each to different areas of your site.","elementor"),docs:"https://go.elementor.com/app-theme-builder-page/",video_url:"https://www.youtube.com/embed/_y5eZ60lVoY"}},{type:"single-post",icon:"eicon-single-post",title:u("Single Post","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/single-post.svg"},tooltip_data:{title:u("What is a Single Post Template?","elementor"),content:u("A single post template allows you to easily design the layout and style of posts, ensuring a design consistency throughout all your blog posts, for example.","elementor"),tip:u("You can create multiple single post templates, and assign each to a different category.","elementor"),docs:"https://go.elementor.com/app-theme-builder-post/",video_url:"https://www.youtube.com/embed/8Fk-Edu7DL0"}},{type:"archive",icon:"eicon-archive",title:u("Archive","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/archive.svg"},tooltip_data:{title:u("What is an Archive Template?","elementor"),content:u("An archive template allows you to easily design the layout and style of archive pages - those pages that show a list of posts (e.g. a blog’s list of recent posts), which may be filtered by terms such as authors, categories, tags, search results, etc.","elementor"),tip:u("If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts.","elementor"),docs:"https://go.elementor.com/app-theme-builder-archive/",video_url:"https://www.youtube.com/embed/wxElpEh9bfA"}},{type:"search-results",icon:"eicon-search-results",title:u("search results page","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/search-results.svg"},tooltip_data:{title:u("What is a Search Results Template?","elementor"),content:u("You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results.","elementor"),tip:u("You can customize the message if there are no results for the search term.","elementor"),docs:"https://go.elementor.com/app-theme-builder-search-results/",video_url:"https://www.youtube.com/embed/KKkIU_L5sDo"}},{type:"product",icon:"eicon-single-product",title:u("Product","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/product.svg"},tooltip_data:{title:u("What is a Single Product Template?","elementor"),content:u("A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign.","elementor"),tip:u("You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products.","elementor"),docs:"https://go.elementor.com/app-theme-builder-product/",video_url:"https://www.youtube.com/embed/PjhoB1RWkBM"}},{type:"products",icon:"eicon-products",title:u("Products Archive","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/products.svg"},tooltip_data:{title:u("What is a Products Archive Template?","elementor"),content:u("A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc.","elementor"),tip:u("You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown.","elementor"),docs:"https://go.elementor.com/app-theme-builder-products-archive/",video_url:"https://www.youtube.com/embed/cQLeirgkguA"}},{type:"error-404",icon:"eicon-error-404",title:u("404 page","elementor"),urls:{thumbnail:elementorAppConfig.assets_url+"/images/app/site-editor/error-404.svg"},tooltip_data:{title:u("What is a 404 Page Template?","elementor"),content:u("A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist.","elementor"),tip:u("Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for.","elementor"),docs:"https://go.elementor.com/app-theme-builder-404/",video_url:"https://www.youtube.com/embed/ACCNp9tBMQg"}}]}}])}()},83040:(r,l,s)=>{"use strict";s.r(l),s.d(l,{Link:()=>ee,Location:()=>q,LocationProvider:()=>U,Match:()=>ae,Redirect:()=>ne,Router:()=>K,ServerLocation:()=>H,createHistory:()=>S,createMemorySource:()=>D,globalHistory:()=>A,isRedirect:()=>te,matchPath:()=>_,navigate:()=>W,redirectTo:()=>re,useLocation:()=>ie,useMatch:()=>ue,useNavigate:()=>le,useParams:()=>se});var u=s(41594),c=s.n(u),p=s(32091),m=s.n(p),v=s(49477),h=s.n(v);function componentWillMount(){var r=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=r&&this.setState(r)}function componentWillReceiveProps(r){this.setState(function updater(l){var s=this.constructor.getDerivedStateFromProps(r,l);return null!=s?s:null}.bind(this))}function componentWillUpdate(r,l){try{var s=this.props,u=this.state;this.props=r,this.state=l,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(s,u)}finally{this.props=s,this.state=u}}componentWillMount.__suppressDeprecationWarning=!0,componentWillReceiveProps.__suppressDeprecationWarning=!0,componentWillUpdate.__suppressDeprecationWarning=!0;var y=function startsWith(r,l){return r.substr(0,l.length)===l},g=function pick(r,l){for(var s=void 0,u=void 0,c=l.split("?")[0],p=E(c),v=""===p[0],h=j(r),y=0,g=h.length;y<g;y++){var _=!1,b=h[y].route;if(b.default)u={route:b,params:{},uri:l};else{for(var x=E(b.path),P={},O=Math.max(p.length,x.length),N=0;N<O;N++){var R=x[N],M=p[N];if(C(R)){P[R.slice(1)||"*"]=p.slice(N).map(decodeURIComponent).join("/");break}if(void 0===M){_=!0;break}var S=w.exec(R);if(S&&!v){-1===T.indexOf(S[1])||m()(!1);var D=decodeURIComponent(M);P[S[1]]=D}else if(R!==M){_=!0;break}}if(!_){s={route:b,params:P,uri:"/"+p.slice(0,N).join("/")};break}}}return s||u||null},_=function match(r,l){return g([{path:r}],l)},b=function resolve(r,l){if(y(r,"/"))return r;var s=r.split("?"),u=s[0],c=s[1],p=l.split("?")[0],m=E(u),v=E(p);if(""===m[0])return N(p,c);if(!y(m[0],".")){var h=v.concat(m).join("/");return N(("/"===p?"":"/")+h,c)}for(var g=v.concat(m),_=[],b=0,x=g.length;b<x;b++){var w=g[b];".."===w?_.pop():"."!==w&&_.push(w)}return N("/"+_.join("/"),c)},x=function insertParams(r,l){var s=r.split("?"),u=s[0],c=s[1],p=void 0===c?"":c,m="/"+E(u).map(function(r){var s=w.exec(r);return s?l[s[1]]:r}).join("/"),v=l.location,h=(v=void 0===v?{}:v).search,y=(void 0===h?"":h).split("?")[1]||"";return m=N(m,p,y)},w=/^:(.+)/,P=function isDynamic(r){return w.test(r)},C=function isSplat(r){return r&&"*"===r[0]},O=function rankRoute(r,l){return{route:r,score:r.default?0:E(r.path).reduce(function(r,l){return r+=4,!function isRootSegment(r){return""===r}(l)?P(l)?r+=2:C(l)?r-=5:r+=3:r+=1,r},0),index:l}},j=function rankRoutes(r){return r.map(O).sort(function(r,l){return r.score<l.score?1:r.score>l.score?-1:r.index-l.index})},E=function segmentize(r){return r.replace(/(^\/+|\/+$)/g,"").split("/")},N=function addQuery(r){for(var l=arguments.length,s=Array(l>1?l-1:0),u=1;u<l;u++)s[u-1]=arguments[u];return r+((s=s.filter(function(r){return r&&r.length>0}))&&s.length>0?"?"+s.join("&"):"")},T=["uri","path"],R=Object.assign||function(r){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var u in s)Object.prototype.hasOwnProperty.call(s,u)&&(r[u]=s[u])}return r},M=function getLocation(r){var l=r.location,s=l.search,u=l.hash,c=l.href,p=l.origin,m=l.protocol,v=l.host,h=l.hostname,y=l.port,g=r.location.pathname;!g&&c&&B&&(g=new URL(c).pathname);return{pathname:encodeURI(decodeURI(g)),search:s,hash:u,href:c,origin:p,protocol:m,host:v,hostname:h,port:y,state:r.history.state,key:r.history.state&&r.history.state.key||"initial"}},S=function createHistory(r,l){var s=[],u=M(r),c=!1,p=function resolveTransition(){};return{get location(){return u},get transitioning(){return c},_onTransitionComplete:function _onTransitionComplete(){c=!1,p()},listen:function listen(l){s.push(l);var c=function popstateListener(){u=M(r),l({location:u,action:"POP"})};return r.addEventListener("popstate",c),function(){r.removeEventListener("popstate",c),s=s.filter(function(r){return r!==l})}},navigate:function navigate(l){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},v=m.state,h=m.replace,y=void 0!==h&&h;if("number"==typeof l)r.history.go(l);else{v=R({},v,{key:Date.now()+""});try{c||y?r.history.replaceState(v,null,l):r.history.pushState(v,null,l)}catch(s){r.location[y?"replace":"assign"](l)}}u=M(r),c=!0;var g=new Promise(function(r){return p=r});return s.forEach(function(r){return r({location:u,action:"PUSH"})}),g}}},D=function createMemorySource(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",l=r.indexOf("?"),s={pathname:l>-1?r.substr(0,l):r,search:l>-1?r.substr(l):""},u=0,c=[s],p=[null];return{get location(){return c[u]},addEventListener:function addEventListener(r,l){},removeEventListener:function removeEventListener(r,l){},history:{get entries(){return c},get index(){return u},get state(){return p[u]},pushState:function pushState(r,l,s){var m=s.split("?"),v=m[0],h=m[1],y=void 0===h?"":h;u++,c.push({pathname:v,search:y.length?"?"+y:y}),p.push(r)},replaceState:function replaceState(r,l,s){var m=s.split("?"),v=m[0],h=m[1],y=void 0===h?"":h;c[u]={pathname:v,search:y},p[u]=r},go:function go(r){var l=u+r;l<0||l>p.length-1||(u=l)}}}},B=!("undefined"==typeof window||!window.document||!window.document.createElement),A=S(function getSource(){return B?window:D()}()),W=A.navigate,I=Object.assign||function(r){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var u in s)Object.prototype.hasOwnProperty.call(s,u)&&(r[u]=s[u])}return r};function _objectWithoutProperties(r,l){var s={};for(var u in r)l.indexOf(u)>=0||Object.prototype.hasOwnProperty.call(r,u)&&(s[u]=r[u]);return s}function _classCallCheck(r,l){if(!(r instanceof l))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(r,l){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!l||"object"!=typeof l&&"function"!=typeof l?r:l}function _inherits(r,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function, not "+typeof l);r.prototype=Object.create(l&&l.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(r,l):r.__proto__=l)}var F=function createNamedContext(r,l){var s=h()(l);return s.displayName=r,s},L=F("Location"),q=function Location(r){var l=r.children;return c().createElement(L.Consumer,null,function(r){return r?l(r):c().createElement(U,null,l)})},U=function(r){function LocationProvider(){var l,s;_classCallCheck(this,LocationProvider);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return l=s=_possibleConstructorReturn(this,r.call.apply(r,[this].concat(c))),s.state={context:s.getContext(),refs:{unlisten:null}},_possibleConstructorReturn(s,l)}return _inherits(LocationProvider,r),LocationProvider.prototype.getContext=function getContext(){var r=this.props.history;return{navigate:r.navigate,location:r.location}},LocationProvider.prototype.componentDidCatch=function componentDidCatch(r,l){if(!te(r))throw r;(0,this.props.history.navigate)(r.uri,{replace:!0})},LocationProvider.prototype.componentDidUpdate=function componentDidUpdate(r,l){l.context.location!==this.state.context.location&&this.props.history._onTransitionComplete()},LocationProvider.prototype.componentDidMount=function componentDidMount(){var r=this,l=this.state.refs,s=this.props.history;s._onTransitionComplete(),l.unlisten=s.listen(function(){Promise.resolve().then(function(){requestAnimationFrame(function(){r.unmounted||r.setState(function(){return{context:r.getContext()}})})})})},LocationProvider.prototype.componentWillUnmount=function componentWillUnmount(){var r=this.state.refs;this.unmounted=!0,r.unlisten()},LocationProvider.prototype.render=function render(){var r=this.state.context,l=this.props.children;return c().createElement(L.Provider,{value:r},"function"==typeof l?l(r):l||null)},LocationProvider}(c().Component);U.defaultProps={history:A};var H=function ServerLocation(r){var l=r.url,s=r.children,u=l.indexOf("?"),p=void 0,m="";return u>-1?(p=l.substring(0,u),m=l.substring(u)):p=l,c().createElement(L.Provider,{value:{location:{pathname:p,search:m,hash:""},navigate:function navigate(){throw new Error("You can't call navigate on the server.")}}},s)},G=F("Base",{baseuri:"/",basepath:"/"}),K=function Router(r){return c().createElement(G.Consumer,null,function(l){return c().createElement(q,null,function(s){return c().createElement(V,I({},l,s,r))})})},V=function(r){function RouterImpl(){return _classCallCheck(this,RouterImpl),_possibleConstructorReturn(this,r.apply(this,arguments))}return _inherits(RouterImpl,r),RouterImpl.prototype.render=function render(){var r=this.props,l=r.location,s=r.navigate,u=r.basepath,p=r.primary,m=r.children,v=(r.baseuri,r.component),h=void 0===v?"div":v,y=_objectWithoutProperties(r,["location","navigate","basepath","primary","children","baseuri","component"]),_=c().Children.toArray(m).reduce(function(r,l){var s=pe(u)(l);return r.concat(s)},[]),x=l.pathname,w=g(_,x);if(w){var P=w.params,C=w.uri,O=w.route,j=w.route.value;u=O.default?u:O.path.replace(/\*$/,"");var E=I({},P,{uri:C,location:l,navigate:function navigate(r,l){return s(b(r,C),l)}}),N=c().cloneElement(j,E,j.props.children?c().createElement(K,{location:l,primary:p},j.props.children):void 0),T=p?Y:h,R=p?I({uri:C,location:l,component:h},y):y;return c().createElement(G.Provider,{value:{baseuri:C,basepath:u}},c().createElement(T,R,N))}return null},RouterImpl}(c().PureComponent);V.defaultProps={primary:!0};var z=F("Focus"),Y=function FocusHandler(r){var l=r.uri,s=r.location,u=r.component,p=_objectWithoutProperties(r,["uri","location","component"]);return c().createElement(z.Consumer,null,function(r){return c().createElement(Z,I({},p,{component:u,requestFocus:r,uri:l,location:s}))})},Q=!0,$=0,Z=function(r){function FocusHandlerImpl(){var l,s;_classCallCheck(this,FocusHandlerImpl);for(var u=arguments.length,c=Array(u),p=0;p<u;p++)c[p]=arguments[p];return l=s=_possibleConstructorReturn(this,r.call.apply(r,[this].concat(c))),s.state={},s.requestFocus=function(r){!s.state.shouldFocus&&r&&r.focus()},_possibleConstructorReturn(s,l)}return _inherits(FocusHandlerImpl,r),FocusHandlerImpl.getDerivedStateFromProps=function getDerivedStateFromProps(r,l){if(null==l.uri)return I({shouldFocus:!0},r);var s=r.uri!==l.uri,u=l.location.pathname!==r.location.pathname&&r.location.pathname===r.uri;return I({shouldFocus:s||u},r)},FocusHandlerImpl.prototype.componentDidMount=function componentDidMount(){$++,this.focus()},FocusHandlerImpl.prototype.componentWillUnmount=function componentWillUnmount(){0===--$&&(Q=!0)},FocusHandlerImpl.prototype.componentDidUpdate=function componentDidUpdate(r,l){r.location!==this.props.location&&this.state.shouldFocus&&this.focus()},FocusHandlerImpl.prototype.focus=function focus(){var r=this.props.requestFocus;r?r(this.node):Q?Q=!1:this.node&&(this.node.contains(document.activeElement)||this.node.focus())},FocusHandlerImpl.prototype.render=function render(){var r=this,l=this.props,s=(l.children,l.style),u=(l.requestFocus,l.component),p=void 0===u?"div":u,m=(l.uri,l.location,_objectWithoutProperties(l,["children","style","requestFocus","component","uri","location"]));return c().createElement(p,I({style:I({outline:"none"},s),tabIndex:"-1",ref:function ref(l){return r.node=l}},m),c().createElement(z.Provider,{value:this.requestFocus},this.props.children))},FocusHandlerImpl}(c().Component);!function polyfill(r){var l=r.prototype;if(!l||!l.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof r.getDerivedStateFromProps&&"function"!=typeof l.getSnapshotBeforeUpdate)return r;var s=null,u=null,c=null;if("function"==typeof l.componentWillMount?s="componentWillMount":"function"==typeof l.UNSAFE_componentWillMount&&(s="UNSAFE_componentWillMount"),"function"==typeof l.componentWillReceiveProps?u="componentWillReceiveProps":"function"==typeof l.UNSAFE_componentWillReceiveProps&&(u="UNSAFE_componentWillReceiveProps"),"function"==typeof l.componentWillUpdate?c="componentWillUpdate":"function"==typeof l.UNSAFE_componentWillUpdate&&(c="UNSAFE_componentWillUpdate"),null!==s||null!==u||null!==c){var p=r.displayName||r.name,m="function"==typeof r.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+p+" uses "+m+" but also contains the following legacy lifecycles:"+(null!==s?"\n  "+s:"")+(null!==u?"\n  "+u:"")+(null!==c?"\n  "+c:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof r.getDerivedStateFromProps&&(l.componentWillMount=componentWillMount,l.componentWillReceiveProps=componentWillReceiveProps),"function"==typeof l.getSnapshotBeforeUpdate){if("function"!=typeof l.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");l.componentWillUpdate=componentWillUpdate;var v=l.componentDidUpdate;l.componentDidUpdate=function componentDidUpdatePolyfill(r,l,s){var u=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:s;v.call(this,r,l,u)}}return r}(Z);var J=function k(){},X=c().forwardRef;void 0===X&&(X=function forwardRef(r){return r});var ee=X(function(r,l){var s=r.innerRef,u=_objectWithoutProperties(r,["innerRef"]);return c().createElement(G.Consumer,null,function(r){r.basepath;var p=r.baseuri;return c().createElement(q,null,function(r){var m=r.location,v=r.navigate,h=u.to,g=u.state,_=u.replace,x=u.getProps,w=void 0===x?J:x,P=_objectWithoutProperties(u,["to","state","replace","getProps"]),C=b(h,p),O=encodeURI(C),j=m.pathname===O,E=y(m.pathname,O);return c().createElement("a",I({ref:l||s,"aria-current":j?"page":void 0},P,w({isCurrent:j,isPartiallyCurrent:E,href:C,location:m}),{href:C,onClick:function onClick(r){if(P.onClick&&P.onClick(r),de(r)){r.preventDefault();var l=_;if("boolean"!=typeof _&&j){var s=I({},m.state),u=(s.key,_objectWithoutProperties(s,["key"]));l=function shallowCompare(r,l){var s=Object.keys(r);return s.length===Object.keys(l).length&&s.every(function(s){return l.hasOwnProperty(s)&&r[s]===l[s]})}(I({},g),u)}v(C,{state:g,replace:l})}}}))})})});function RedirectRequest(r){this.uri=r}ee.displayName="Link";var te=function isRedirect(r){return r instanceof RedirectRequest},re=function redirectTo(r){throw new RedirectRequest(r)},oe=function(r){function RedirectImpl(){return _classCallCheck(this,RedirectImpl),_possibleConstructorReturn(this,r.apply(this,arguments))}return _inherits(RedirectImpl,r),RedirectImpl.prototype.componentDidMount=function componentDidMount(){var r=this.props,l=r.navigate,s=r.to,u=(r.from,r.replace),c=void 0===u||u,p=r.state,m=(r.noThrow,r.baseuri),v=_objectWithoutProperties(r,["navigate","to","from","replace","state","noThrow","baseuri"]);Promise.resolve().then(function(){var r=b(s,m);l(x(r,v),{replace:c,state:p})})},RedirectImpl.prototype.render=function render(){var r=this.props,l=(r.navigate,r.to),s=(r.from,r.replace,r.state,r.noThrow),u=r.baseuri,c=_objectWithoutProperties(r,["navigate","to","from","replace","state","noThrow","baseuri"]),p=b(l,u);return s||re(x(p,c)),null},RedirectImpl}(c().Component),ne=function Redirect(r){return c().createElement(G.Consumer,null,function(l){var s=l.baseuri;return c().createElement(q,null,function(l){return c().createElement(oe,I({},l,{baseuri:s},r))})})},ae=function Match(r){var l=r.path,s=r.children;return c().createElement(G.Consumer,null,function(r){var u=r.baseuri;return c().createElement(q,null,function(r){var c=r.navigate,p=r.location,m=b(l,u),v=_(m,p.pathname);return s({navigate:c,location:p,match:v?I({},v.params,{uri:v.uri,path:l}):null})})})},ie=function useLocation(){var r=(0,u.useContext)(L);if(!r)throw new Error("useLocation hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");return r.location},le=function useNavigate(){var r=(0,u.useContext)(L);if(!r)throw new Error("useNavigate hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");return r.navigate},se=function useParams(){var r=(0,u.useContext)(G);if(!r)throw new Error("useParams hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");var l=ie(),s=_(r.basepath,l.pathname);return s?s.params:null},ue=function useMatch(r){if(!r)throw new Error("useMatch(path: string) requires an argument of a string to match against");var l=(0,u.useContext)(G);if(!l)throw new Error("useMatch hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");var s=ie(),c=b(r,l.baseuri),p=_(c,s.pathname);return p?I({},p.params,{uri:p.uri,path:r}):null},ce=function stripSlashes(r){return r.replace(/(^\/+|\/+$)/g,"")},pe=function createRoute(r){return function(l){if(!l)return null;if(l.type===c().Fragment&&l.props.children)return c().Children.map(l.props.children,createRoute(r));if(l.props.path||l.props.default||l.type===ne||m()(!1),l.type!==ne||l.props.from&&l.props.to||m()(!1),l.type!==ne||function validateRedirect(r,l){var s=function filter(r){return P(r)};return E(r).filter(s).sort().join("/")===E(l).filter(s).sort().join("/")}(l.props.from,l.props.to)||m()(!1),l.props.default)return{value:l,default:!0};var s=l.type===ne?l.props.from:l.props.path,u="/"===s?r:ce(r)+"/"+ce(s);return{value:l,default:l.props.default,path:l.props.children?ce(u)+"/*":u}}},de=function shouldNavigate(r){return!r.defaultPrevented&&0===r.button&&!(r.metaKey||r.altKey||r.ctrlKey||r.shiftKey)}},84624:()=>{},84691:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Menu;var m=p(s(41594)),v=p(s(18186)),h=s(8188),y=p(s(47483)),g=p(s(90384));function Menu(r){var l=m.default.useContext(h.Context).templateTypes;return m.default.createElement(v.default,{menuItems:l,actionButton:function actionButton(l){var s="eps-menu-item__action-button";if(r.promotion)return m.default.createElement(y.default,{text:u("Upgrade Now","elementor"),hideText:!0,icon:"eicon-lock",className:s});return m.default.createElement("span",{className:s},m.default.createElement(g.default,{hideText:!0,size:"sm",onClick:function onClick(){return function goToCreate(){location.href=l.urls.create}()}}))},promotion:r.promotion},r.allPartsButton,m.default.createElement("div",{className:"eps-menu__title"},u("Site Parts","elementor")))}s(22057),Menu.propTypes={allPartsButton:c.element.isRequired,promotion:c.bool}},85418:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=Heading;var p=c(s(41594)),m=s(79397);function Heading(r){var l=[r.className];r.variant&&l.push("eps-"+r.variant);var s=function Element(){return p.default.createElement(r.tag,{className:(0,m.arrayToClassName)(l)},r.children)};return p.default.createElement(s,null)}Heading.propTypes={className:u.string,children:u.oneOfType([u.string,u.object,u.arrayOf(u.object)]).isRequired,tag:u.oneOf(["h1","h2","h3","h4","h5","h6"]),variant:u.oneOf(["display-1","display-2","display-3","display-4","h1","h2","h3","h4","h5","h6"]).isRequired},Heading.defaultProps={className:"",tag:"h1"}},85707:(r,l,s)=>{var u=s(45498);r.exports=function _defineProperty(r,l,s){return(l=u(l))in r?Object.defineProperty(r,l,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[l]=s,r},r.exports.__esModule=!0,r.exports.default=r.exports},86161:()=>{},87861:(r,l,s)=>{var u=s(91270);r.exports=function _inherits(r,l){if("function"!=typeof l&&null!==l)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(l&&l.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),l&&u(r,l)},r.exports.__esModule=!0,r.exports.default=r.exports},89736:r=>{function _regeneratorDefine(l,s,u,c){var p=Object.defineProperty;try{p({},"",{})}catch(l){p=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,l,s,u){if(l)p?p(r,l,{value:s,enumerable:!u,configurable:!u,writable:!u}):r[l]=s;else{var c=function o(l,s){_regeneratorDefine(r,l,function(r){return this._invoke(l,s,r)})};c("next",0),c("throw",1),c("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(l,s,u,c)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},90384:(r,l,s)=>{"use strict";var u=s(62688),c=s(12470).__,p=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var m=p(s(39805)),v=p(s(40989)),h=p(s(15118)),y=p(s(29402)),g=p(s(87861)),_=p(s(85707)),b=p(s(47483));function ownKeys(r,l){var s=Object.keys(r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(r);l&&(u=u.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),s.push.apply(s,u)}return s}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var s=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(s),!0).forEach(function(l){(0,_.default)(r,l,s[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(s,l))})}return r}function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(r){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r})()}s(84624);var x=l.default=function(r){function AddNewButton(){return(0,m.default)(this,AddNewButton),function _callSuper(r,l,s){return l=(0,y.default)(l),(0,h.default)(r,_isNativeReflectConstruct()?Reflect.construct(l,s||[],(0,y.default)(r).constructor):l.apply(r,s))}(this,AddNewButton,arguments)}return(0,g.default)(AddNewButton,r),(0,v.default)(AddNewButton,[{key:"getClassName",value:function getClassName(){var r=this.props.className;return this.props.size&&(r+=" eps-add-new-button--"+this.props.size),r}}])}(b.default);(0,_.default)(x,"propTypes",_objectSpread(_objectSpread({},b.default.propTypes),{},{text:u.string,size:u.string})),(0,_.default)(x,"defaultProps",_objectSpread(_objectSpread({},b.default.defaultProps),{},{className:"eps-add-new-button",text:c("Add New","elementor"),icon:"eicon-plus"}))},91270:r=>{function _setPrototypeOf(l,s){return r.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,l){return r.__proto__=l,r},r.exports.__esModule=!0,r.exports.default=r.exports,_setPrototypeOf(l,s)}r.exports=_setPrototypeOf,r.exports.__esModule=!0,r.exports.default=r.exports},91618:()=>{},91819:(r,l,s)=>{var u=s(78113);r.exports=function _arrayWithoutHoles(r){if(Array.isArray(r))return u(r)},r.exports.__esModule=!0,r.exports.default=r.exports},93279:(r,l,s)=>{"use strict";var u=s(62688),c=s(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.default=List;var p=c(s(41594)),m=s(79397),v=c(s(45735));function List(r){var l,s="eps-list",u=[s,r.className];return Object.prototype.hasOwnProperty.call(r,"padding")&&(l={"--eps-list-padding":(0,m.pxToRem)(r.padding)},u.push(s+"--padding")),r.separated&&u.push(s+"--separated"),p.default.createElement("ul",{style:l,className:(0,m.arrayToClassName)(u)},r.children)}s(99835),List.propTypes={className:u.string,divided:u.any,separated:u.any,padding:u.string,children:u.oneOfType([u.object,u.arrayOf(u.object)]).isRequired},List.defaultProps={className:""},List.Item=v.default},95315:r=>{r.exports=function _regeneratorKeys(r){var l=Object(r),s=[];for(var u in l)s.unshift(u);return function e(){for(;s.length;)if((u=s.pop())in l)return e.value=u,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},96431:()=>{},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports},97088:()=>{},97266:(r,l,s)=>{"use strict";var u=s(96784),c=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.hooks=l.components=l.appUi=void 0;var p=u(s(90384)),m=u(s(21689)),v=u(s(47483)),h=u(s(35676)),y=u(s(7229)),g=u(s(23074)),_=u(s(18320)),b=u(s(4380)),x=u(s(70097)),w=u(s(47579)),P=u(s(28929)),C=u(s(70692)),O=u(s(15656)),j=u(s(59824)),E=u(s(39970)),N=u(s(65949)),T=u(s(85418)),R=u(s(69783)),M=u(s(3416)),S=u(s(76547)),D=u(s(93279)),B=u(s(18186)),A=u(s(43496)),W=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var p,m,v={__proto__:null,default:r};if(null===r||"object"!=c(r)&&"function"!=typeof r)return v;if(p=l?u:s){if(p.has(r))return p.get(r);p.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((m=(p=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(m.get||m.set)?p(v,h,m):v[h]=r[h]);return v}(r,l)}(s(61678)),I=u(s(28101)),F=u(s(40587)),L=u(s(80226)),q=u(s(48590)),U=u(s(3826)),H=u(s(12505)),G=u(s(55725)),K=u(s(98718)),V=u(s(54999)),z=u(s(53441)),Y=u(s(73921)),Q=u(s(46361)),$=u(s(80791)),Z=u(s(41494)),J=u(s(10575)),X=u(s(21114));l.appUi={AddNewButton:p.default,Box:m.default,Button:v.default,Card:h.default,CardBody:y.default,CardFooter:g.default,CardHeader:b.default,CardImage:_.default,CardOverlay:x.default,Checkbox:w.default,Collapse:P.default,CssGrid:C.default,Dialog:O.default,DragDrop:j.default,DropZone:E.default,ErrorBoundary:N.default,Heading:T.default,GoProButton:R.default,Grid:M.default,Icon:S.default,List:D.default,Menu:B.default,MenuItem:A.default,Modal:W.Modal,ModalProvider:W.default,NotFound:I.default,Notice:F.default,Page:L.default,Popover:q.default,Select:U.default,Select2:H.default,Text:G.default,UploadFile:K.default,InlineLink:V.default},l.components={UnfilteredFilesDialog:z.default},l.hooks={useAjax:Y.default,useAction:Q.default,usePageTitle:$.default,useQueryParams:Z.default,useIntroduction:J.default,useConfirmAction:X.default}},98718:(r,l,s)=>{"use strict";var u=s(12470).__,c=s(62688),p=s(96784),m=s(10564);Object.defineProperty(l,"__esModule",{value:!0}),l.default=UploadFile;var v=function _interopRequireWildcard(r,l){if("function"==typeof WeakMap)var s=new WeakMap,u=new WeakMap;return function _interopRequireWildcard(r,l){if(!l&&r&&r.__esModule)return r;var c,p,v={__proto__:null,default:r};if(null===r||"object"!=m(r)&&"function"!=typeof r)return v;if(c=l?u:s){if(c.has(r))return c.get(r);c.set(r,v)}for(var h in r)"default"!==h&&{}.hasOwnProperty.call(r,h)&&((p=(c=Object.defineProperty)&&Object.getOwnPropertyDescriptor(r,h))&&(p.get||p.set)?c(v,h,p):v[h]=r[h]);return v}(r,l)}(s(41594)),h=p(s(47483)),y=s(79397);function UploadFile(r){var l,s=(0,v.useRef)(null),c=["e-app-upload-file",r.className];return v.default.createElement("div",{className:(0,y.arrayToClassName)(c)},v.default.createElement("input",{ref:s,type:"file",accept:r.filetypes.map(function(r){return"."+r}).join(", "),className:"e-app-upload-file__input",onChange:function onChange(l){var c=l.target.files[0];c&&(0,y.isOneOf)(c.type,r.filetypes)?r.onFileSelect(c,l,"browse"):(s.current.value="",r.onError({id:"file_not_allowed",message:u("This file type is not allowed","elementor")}))}}),v.default.createElement(h.default,{className:"e-app-upload-file__button",text:r.text,variant:r.variant,color:r.color,size:"lg",hideText:r.isLoading,icon:r.isLoading?"eicon-loading eicon-animation-spin":"",onClick:function onClick(){if(r.onFileChoose&&r.onFileChoose(),!r.isLoading)if(r.onButtonClick&&r.onButtonClick(),"file-explorer"===r.type)s.current.click();else if("wp-media"===r.type){if(l)return void l.open();(l=wp.media({multiple:!1,library:{type:["image","image/svg+xml"]}})).on("select",function(){r.onWpMediaSelect&&r.onWpMediaSelect(l)}),l.open()}}}))}s(14546),UploadFile.propTypes={className:c.string,type:c.string,onWpMediaSelect:c.func,text:c.string,onFileSelect:c.func,isLoading:c.bool,filetypes:c.array.isRequired,onError:c.func,variant:c.string,color:c.string,onButtonClick:c.func,onFileChoose:c.func},UploadFile.defaultProps={className:"",type:"file-explorer",text:u("Select File","elementor"),onError:function onError(){},variant:"contained",color:"primary"}},99835:()=>{}},l={};function __webpack_require__(s){var u=l[s];if(void 0!==u)return u.exports;var c=l[s]={exports:{}};return r[s](c,c.exports,__webpack_require__),c.exports}__webpack_require__.n=r=>{var l=r&&r.__esModule?()=>r.default:()=>r;return __webpack_require__.d(l,{a:l}),l},__webpack_require__.d=(r,l)=>{for(var s in l)__webpack_require__.o(l,s)&&!__webpack_require__.o(r,s)&&Object.defineProperty(r,s,{enumerable:!0,get:l[s]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(r){if("object"==typeof window)return window}}(),__webpack_require__.o=(r,l)=>Object.prototype.hasOwnProperty.call(r,l),__webpack_require__.r=r=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},(()=>{"use strict";var r=__webpack_require__(96784),l=r(__webpack_require__(42209)),s=__webpack_require__(97266),u=r(__webpack_require__(66848));window.elementorAppPackages={appUi:s.appUi,components:s.components,hooks:s.hooks,router:l.default,siteEditor:u.default}})()})();