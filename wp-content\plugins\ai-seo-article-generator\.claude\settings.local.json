{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(php -l:*)", "Bash(ls:*)", "Bash(for file in ./AI_CONTENT_DEVELOPMENT_APPROACH.md ./ARTICLE_TRUNCATION_FIXES.md ./LESSONS_LEARNED.md ./STRUCTURE_DATA_FIX_PLAN.md ./TESTING_CONCLUSIONS.md)", "Bash(do echo \"=== $file ===\")", "Bash(wc:*)", "Bash(done)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "Bash(if command -v msgfmt)", "Bash(/dev/null)", "<PERSON>sh(then msgfmt ai-seo-article-generator-he_IL.po -o ai-seo-article-generator-he_IL.mo)", "Bash(else echo \"msgfmt not available, will create .mo file manually\")", "Bash(fi)", "Bash(php:*)", "Bash(rm:*)", "Bash(wp cron event:*)", "WebFetch(domain:developer.wordpress.org)", "<PERSON><PERSON>(svn:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(git add:*)", "WebFetch(domain:postinor.local)"], "deny": []}}