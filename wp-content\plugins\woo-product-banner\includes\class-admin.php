<?php
/**
 * Admin functionality for WooCommerce Product Banner
 */

if (!defined('ABSPATH')) {
    exit;
}

class WPB_Admin {

    /**
     * Database instance
     */
    private $database;

    /**
     * Constructor
     */
    public function __construct($database = null) {
        $this->database = $database;
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=product',
            __('Banner Prodotti', 'woo-product-banner'),
            __('Banner Prodotti', 'woo-product-banner'),
            'manage_woocommerce',
            'woo-product-banner',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        register_setting('wpb_settings', 'wpb_banner_image', array(
            'type' => 'integer',
            'sanitize_callback' => array($this, 'sanitize_banner_image'),
            'default' => ''
        ));

        // Debug: Log admin_init execution
        if (function_exists('wpb_log')) {
            wpb_log('admin_init called');
        }

        // Handle form submissions
        if (isset($_POST['wpb_submit']) && wp_verify_nonce($_POST['wpb_nonce'], 'wpb_save_banner')) {
            if (function_exists('wpb_log')) {
                wpb_log('Form submission detected - wpb_submit');
                wpb_log('POST data: ' . print_r($_POST, true));
            }
            $this->handle_banner_upload();
        }

        if (isset($_POST['wpb_remove']) && wp_verify_nonce($_POST['wpb_nonce'], 'wpb_save_banner')) {
            if (function_exists('wpb_log')) {
                wpb_log('Form submission detected - wpb_remove');
            }
            $this->handle_banner_removal();
        }

        // Debug: Check if we're on the right page
        if (isset($_GET['page']) && $_GET['page'] === 'woo-product-banner') {
            if (function_exists('wpb_log')) {
                wpb_log('On banner admin page');
            }
        }
    }
    
    /**
     * Sanitize banner image ID
     */
    public function sanitize_banner_image($value) {
        if (empty($value)) {
            return '';
        }
        
        $value = absint($value);
        
        // Verify the attachment exists and is an image
        if (!wp_attachment_is_image($value)) {
            add_settings_error('wpb_messages', 'wpb_message', __('Invalid image selected.', 'woo-product-banner'), 'error');
            return get_option('wpb_banner_image', '');
        }
        
        return $value;
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        $current_banner = $this->database ? $this->database->get_banner_image() : get_option('wpb_banner_image', '');
        $banner_url = '';
        $banner_alt = '';

        if (!empty($current_banner) && wp_attachment_is_image($current_banner)) {
            $banner_url = wp_get_attachment_url($current_banner);
            $banner_alt = get_post_meta($current_banner, '_wp_attachment_image_alt', true);
        }
        ?>
        <div class="wrap wpb-admin-page">
            <h1><?php _e('Banner Prodotti', 'woo-product-banner'); ?></h1>
            <p><?php _e('Upload a banner image to display below product descriptions on single product pages.', 'woo-product-banner'); ?></p>
            
            <?php $this->display_admin_notices(); ?>

            <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
            <div class="notice notice-info">
                <p><strong>Debug Info:</strong></p>
                <ul>
                    <li>Current banner from get_option: <?php echo esc_html(get_option('wpb_banner_image', 'not_set')); ?></li>
                    <li>Current banner from database class: <?php echo esc_html($this->database ? $this->database->get_banner_image() : 'database_not_available'); ?></li>
                    <li>POST data available: <?php echo !empty($_POST) ? 'YES' : 'NO'; ?></li>
                    <?php if (!empty($_POST)): ?>
                    <li>POST wpb_banner_image: <?php echo isset($_POST['wpb_banner_image']) ? esc_html($_POST['wpb_banner_image']) : 'not_set'; ?></li>
                    <li>POST wpb_submit: <?php echo isset($_POST['wpb_submit']) ? 'YES' : 'NO'; ?></li>
                    <li>Nonce valid: <?php echo (isset($_POST['wpb_nonce']) && wp_verify_nonce($_POST['wpb_nonce'], 'wpb_save_banner')) ? 'YES' : 'NO'; ?></li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>
            
            <form method="post" enctype="multipart/form-data" class="wpb-admin-form">
                <?php wp_nonce_field('wpb_save_banner', 'wpb_nonce'); ?>
                
                <table class="form-table wpb-form-table">
                    <tr>
                        <th scope="row">
                            <label for="wpb_banner_image"><?php _e('Banner Image', 'woo-product-banner'); ?></label>
                        </th>
                        <td>
                            <input type="hidden" id="wpb_banner_image" name="wpb_banner_image" value="<?php echo esc_attr($current_banner); ?>" />
                            
                            <div class="wpb-button-group">
                                <button type="button" class="button button-primary" id="wpb_upload_button">
                                    <?php echo !empty($current_banner) ? __('Change Image', 'woo-product-banner') : __('Select Image', 'woo-product-banner'); ?>
                                </button>
                                <button type="button" class="button button-secondary" id="wpb_remove_button" style="<?php echo empty($current_banner) ? 'display:none;' : ''; ?>">
                                    <?php _e('Remove Image', 'woo-product-banner'); ?>
                                </button>
                            </div>
                            
                            <div id="wpb_image_preview" class="<?php echo empty($current_banner) ? 'empty' : ''; ?>">
                                <?php if (!empty($current_banner) && $banner_url): ?>
                                    <img src="<?php echo esc_url($banner_url); ?>" alt="<?php echo esc_attr($banner_alt); ?>" />
                                <?php endif; ?>
                            </div>
                            
                            <p class="description wpb-description">
                                <?php _e('Select an image to display as a banner below product descriptions. Recommended size: 1200x300 pixels or similar aspect ratio.', 'woo-product-banner'); ?>
                            </p>
                            
                            <?php if (!empty($current_banner) && $banner_url): ?>
                                <div class="wpb-image-info">
                                    <p><strong><?php _e('Current Image Details:', 'woo-product-banner'); ?></strong></p>
                                    <ul>
                                        <li><?php _e('Image ID:', 'woo-product-banner'); ?> <?php echo esc_html($current_banner); ?></li>
                                        <li><?php _e('File URL:', 'woo-product-banner'); ?> <a href="<?php echo esc_url($banner_url); ?>" target="_blank"><?php echo esc_html(basename($banner_url)); ?></a></li>
                                        <?php
                                        $image_meta = wp_get_attachment_metadata($current_banner);
                                        if ($image_meta && isset($image_meta['width'], $image_meta['height'])):
                                        ?>
                                        <li><?php _e('Dimensions:', 'woo-product-banner'); ?> <?php echo esc_html($image_meta['width'] . ' × ' . $image_meta['height']); ?> px</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="wpb_submit" class="button-primary" value="<?php _e('Save Banner', 'woo-product-banner'); ?>" />
                    <?php if (!empty($current_banner)): ?>
                        <input type="submit" name="wpb_remove" class="button-secondary" value="<?php _e('Remove Banner', 'woo-product-banner'); ?>" onclick="return confirm('<?php _e('Are you sure you want to remove the banner?', 'woo-product-banner'); ?>');" />
                    <?php endif; ?>
                </p>
            </form>
            
            <div class="wpb-help-section">
                <h3><?php _e('Help & Tips', 'woo-product-banner'); ?></h3>
                <ul>
                    <li><?php _e('The banner will appear below the product description on all single product pages.', 'woo-product-banner'); ?></li>
                    <li><?php _e('For best results, use images with a wide aspect ratio (e.g., 1200x300 pixels).', 'woo-product-banner'); ?></li>
                    <li><?php _e('The image will automatically scale to fit different screen sizes.', 'woo-product-banner'); ?></li>
                    <li><?php _e('Supported formats: JPG, PNG, GIF, WebP.', 'woo-product-banner'); ?></li>
                </ul>
            </div>
        </div>
        <?php
    }
    
    /**
     * Handle banner upload
     */
    private function handle_banner_upload() {
        if (function_exists('wpb_log')) {
            wpb_log('handle_banner_upload called');
        }

        if (!current_user_can('manage_woocommerce')) {
            if (function_exists('wpb_log')) {
                wpb_log('Permission denied for user');
            }
            wp_die(__('You do not have sufficient permissions to access this page.', 'woo-product-banner'));
        }

        $banner_id = sanitize_text_field($_POST['wpb_banner_image']);

        if (function_exists('wpb_log')) {
            wpb_log('Banner ID from POST: ' . $banner_id);
            wpb_log('Database instance available: ' . ($this->database ? 'YES' : 'NO'));
        }

        if (!empty($banner_id) && is_numeric($banner_id)) {
            $banner_id = absint($banner_id);

            if (function_exists('wpb_log')) {
                wpb_log('Processing banner ID: ' . $banner_id);
            }

            // Use database class for validation and saving
            if ($this->database) {
                if (function_exists('wpb_log')) {
                    wpb_log('Using database class for validation');
                }

                $validation = $this->database->validate_banner_image($banner_id);
                if (!is_wp_error($validation)) {
                    if (function_exists('wpb_log')) {
                        wpb_log('Validation passed, attempting to save');
                    }

                    $result = $this->database->set_banner_image($banner_id);

                    if (function_exists('wpb_log')) {
                        wpb_log('Save result: ' . ($result ? 'SUCCESS' : 'FAILED'));
                    }

                    if ($result) {
                        add_settings_error('wpb_messages', 'wpb_message', __('Banner saved successfully!', 'woo-product-banner'), 'updated');
                        error_log('WPB: Banner image updated. ID: ' . $banner_id);
                    } else {
                        add_settings_error('wpb_messages', 'wpb_message', __('Failed to save banner image.', 'woo-product-banner'), 'error');
                    }
                } else {
                    if (function_exists('wpb_log')) {
                        wpb_log('Validation failed: ' . $validation->get_error_message());
                    }
                    add_settings_error('wpb_messages', 'wpb_message', $validation->get_error_message(), 'error');
                }
            } else {
                if (function_exists('wpb_log')) {
                    wpb_log('Using fallback direct option update');
                }

                // Fallback to direct option update if database class not available
                if (wp_attachment_is_image($banner_id)) {
                    $update_result = update_option('wpb_banner_image', $banner_id);

                    if (function_exists('wpb_log')) {
                        wpb_log('Direct option update result: ' . ($update_result ? 'SUCCESS' : 'FAILED'));
                    }

                    add_settings_error('wpb_messages', 'wpb_message', __('Banner saved successfully!', 'woo-product-banner'), 'updated');
                    error_log('WPB: Banner image updated (fallback). ID: ' . $banner_id);
                } else {
                    if (function_exists('wpb_log')) {
                        wpb_log('Image validation failed for ID: ' . $banner_id);
                    }
                    add_settings_error('wpb_messages', 'wpb_message', __('Invalid image selected. Please choose a valid image file.', 'woo-product-banner'), 'error');
                }
            }
        } else {
            if (function_exists('wpb_log')) {
                wpb_log('Empty or invalid banner ID received');
            }
            add_settings_error('wpb_messages', 'wpb_message', __('Please select an image.', 'woo-product-banner'), 'error');
        }
    }
    
    /**
     * Handle banner removal
     */
    private function handle_banner_removal() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'woo-product-banner'));
        }

        if ($this->database) {
            $old_banner = $this->database->get_banner_image();
            $result = $this->database->remove_banner_image();
            if ($result) {
                add_settings_error('wpb_messages', 'wpb_message', __('Banner removed successfully!', 'woo-product-banner'), 'updated');
                // Log the action
                error_log('WPB: Banner image removed. Previous ID: ' . $old_banner);
            } else {
                add_settings_error('wpb_messages', 'wpb_message', __('Failed to remove banner image.', 'woo-product-banner'), 'error');
            }
        } else {
            // Fallback to direct option deletion
            $old_banner = get_option('wpb_banner_image', '');
            delete_option('wpb_banner_image');
            add_settings_error('wpb_messages', 'wpb_message', __('Banner removed successfully!', 'woo-product-banner'), 'updated');
            error_log('WPB: Banner image removed (fallback). Previous ID: ' . $old_banner);
        }
    }
    
    /**
     * Display admin notices
     */
    private function display_admin_notices() {
        settings_errors('wpb_messages');
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('product_page_woo-product-banner' === $hook) {
            wp_enqueue_media();
            wp_enqueue_script('wpb-admin-script', WPB_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), WPB_PLUGIN_VERSION, true);
            wp_enqueue_style('wpb-admin-style', WPB_PLUGIN_URL . 'assets/css/admin.css', array(), WPB_PLUGIN_VERSION);
            
            // Localize script for translations
            wp_localize_script('wpb-admin-script', 'wpb_admin', array(
                'confirm_remove' => __('Are you sure you want to remove this image?', 'woo-product-banner'),
                'select_image' => __('Please select an image before saving.', 'woo-product-banner'),
                'media_title' => __('Select Banner Image', 'woo-product-banner'),
                'media_button' => __('Use this image', 'woo-product-banner'),
                'change_image' => __('Change Image', 'woo-product-banner'),
                'select_image_btn' => __('Select Image', 'woo-product-banner'),
                'remove_image' => __('Remove Image', 'woo-product-banner'),
            ));
        }
    }
}
