<?php
/**
 * Plugin Name: WooCommerce Product Banner
 * Plugin URI: https://example.com/woo-product-banner
 * Description: Allows administrators to display custom banner images below product descriptions on single product pages.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * Text Domain: woo-product-banner
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WPB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WPB_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WPB_PLUGIN_VERSION', '1.0.0');
define('WPB_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main WooCommerce Product Banner Class
 */
class WooCommerce_Product_Banner {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Admin instance
     */
    public $admin = null;

    /**
     * Frontend instance
     */
    public $frontend = null;

    /**
     * Database instance
     */
    public $database = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Include required files
        $this->includes();

        // Include debug files in development
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->include_debug_files();
        }
    }

    /**
     * Include required files
     */
    private function includes() {
        require_once WPB_PLUGIN_PATH . 'includes/class-database.php';
        require_once WPB_PLUGIN_PATH . 'includes/class-admin.php';
        require_once WPB_PLUGIN_PATH . 'includes/class-frontend.php';
        require_once WPB_PLUGIN_PATH . 'includes/class-security.php';
    }

    /**
     * Include debug files for development
     */
    private function include_debug_files() {
        if (file_exists(WPB_PLUGIN_PATH . 'test-plugin.php')) {
            require_once WPB_PLUGIN_PATH . 'test-plugin.php';
        }
        if (file_exists(WPB_PLUGIN_PATH . 'debug-fixes.php')) {
            require_once WPB_PLUGIN_PATH . 'debug-fixes.php';
        }
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Initialize database functionality
        $this->database = new WPB_Database();

        // Initialize security functionality
        new WPB_Security();

        // Initialize admin functionality
        if (is_admin()) {
            $this->admin = new WPB_Admin($this->database);
        }

        // Initialize frontend functionality
        $this->frontend = new WPB_Frontend($this->database);
    }
    
    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Display notice if WooCommerce is not active
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('WooCommerce Product Banner requires WooCommerce to be installed and active.', 'woo-product-banner'); ?></p>
        </div>
        <?php
    }
    

    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('woo-product-banner', false, dirname(WPB_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database table or options if needed
        $this->create_database_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        flush_rewrite_rules();
    }
    
    /**
     * Create database options
     */
    private function create_database_options() {
        // Initialize plugin options
        if (!get_option('wpb_plugin_version')) {
            add_option('wpb_plugin_version', WPB_PLUGIN_VERSION);
        }
    }
    

    

    

}

// Initialize the plugin
WooCommerce_Product_Banner::get_instance();
