<?php
/**
 * Plugin Name: WooCommerce Product Banner
 * Plugin URI: https://example.com/woo-product-banner
 * Description: Allows administrators to display custom banner images below product descriptions on single product pages.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * Text Domain: woo-product-banner
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
if (!defined('WPB_PLUGIN_URL')) {
    define('WPB_PLUGIN_URL', plugin_dir_url(__FILE__));
}
if (!defined('WPB_PLUGIN_PATH')) {
    define('WPB_PLUGIN_PATH', plugin_dir_path(__FILE__));
}
if (!defined('WPB_PLUGIN_VERSION')) {
    define('WPB_PLUGIN_VERSION', '1.0.0');
}
if (!defined('WPB_PLUGIN_BASENAME')) {
    define('WPB_PLUGIN_BASENAME', plugin_basename(__FILE__));
}

// Include configuration file
if (file_exists(__DIR__ . '/wpb-config.php')) {
    require_once __DIR__ . '/wpb-config.php';
}

/**
 * Main WooCommerce Product Banner Class
 */
class WooCommerce_Product_Banner {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Admin instance
     */
    public $admin = null;

    /**
     * Frontend instance
     */
    public $frontend = null;

    /**
     * Database instance
     */
    public $database = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        try {
            add_action('init', array($this, 'init'));
            add_action('plugins_loaded', array($this, 'load_textdomain'));

            // Activation and deactivation hooks
            register_activation_hook(__FILE__, array($this, 'activate'));
            register_deactivation_hook(__FILE__, array($this, 'deactivate'));

            // Include required files
            $this->includes();

            // Include debug files in development
            if (defined('WP_DEBUG') && WP_DEBUG) {
                $this->include_debug_files();
            }

            // Include activation debug (always active for troubleshooting)
            if (file_exists(WPB_PLUGIN_PATH . 'debug-activation.php')) {
                require_once WPB_PLUGIN_PATH . 'debug-activation.php';
            }

        } catch (Exception $e) {
            error_log('WPB Constructor Error: ' . $e->getMessage());
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>WooCommerce Product Banner Fatal Error:</strong> ' . esc_html($e->getMessage());
                echo '</p></div>';
            });
        }
    }

    /**
     * Include required files
     */
    private function includes() {
        $required_files = array(
            'includes/class-database.php',
            'includes/class-admin.php',
            'includes/class-frontend.php',
            'includes/class-security.php'
        );

        foreach ($required_files as $file) {
            $file_path = WPB_PLUGIN_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                error_log('WPB: Required file not found: ' . $file_path);
                throw new Exception('Required file not found: ' . $file);
            }
        }
    }

    /**
     * Include debug files for development
     */
    private function include_debug_files() {
        if (file_exists(WPB_PLUGIN_PATH . 'test-plugin.php')) {
            require_once WPB_PLUGIN_PATH . 'test-plugin.php';
        }
        if (file_exists(WPB_PLUGIN_PATH . 'debug-fixes.php')) {
            require_once WPB_PLUGIN_PATH . 'debug-fixes.php';
        }
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        try {
            // Check if WooCommerce is active
            if (!$this->is_woocommerce_active()) {
                add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
                return;
            }

            // Initialize database functionality
            if (class_exists('WPB_Database')) {
                $this->database = new WPB_Database();
            } else {
                throw new Exception('WPB_Database class not found');
            }

            // Initialize security functionality
            if (class_exists('WPB_Security')) {
                new WPB_Security();
            } else {
                error_log('WPB: WPB_Security class not found');
            }

            // Initialize admin functionality
            if (is_admin()) {
                if (class_exists('WPB_Admin')) {
                    $this->admin = new WPB_Admin($this->database);
                } else {
                    throw new Exception('WPB_Admin class not found');
                }
            }

            // Initialize frontend functionality
            if (class_exists('WPB_Frontend')) {
                $this->frontend = new WPB_Frontend($this->database);
            } else {
                throw new Exception('WPB_Frontend class not found');
            }

        } catch (Exception $e) {
            error_log('WPB Init Error: ' . $e->getMessage());
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>WooCommerce Product Banner Error:</strong> ' . esc_html($e->getMessage());
                echo '</p></div>';
            });
        }
    }
    
    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Display notice if WooCommerce is not active
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('WooCommerce Product Banner requires WooCommerce to be installed and active.', 'woo-product-banner'); ?></p>
        </div>
        <?php
    }
    

    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('woo-product-banner', false, dirname(WPB_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database table or options if needed
        $this->create_database_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        flush_rewrite_rules();
    }
    
    /**
     * Create database options
     */
    private function create_database_options() {
        // Initialize plugin options
        if (!get_option('wpb_plugin_version')) {
            add_option('wpb_plugin_version', WPB_PLUGIN_VERSION);
        }
    }
    

    

    

}

// Initialize the plugin safely
add_action('plugins_loaded', function() {
    try {
        WooCommerce_Product_Banner::get_instance();
    } catch (Exception $e) {
        error_log('WPB Plugin Initialization Error: ' . $e->getMessage());
        add_action('admin_notices', function() use ($e) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>WooCommerce Product Banner:</strong> Errore di inizializzazione - ' . esc_html($e->getMessage());
            echo '</p></div>';
        });
    }
}, 10);
