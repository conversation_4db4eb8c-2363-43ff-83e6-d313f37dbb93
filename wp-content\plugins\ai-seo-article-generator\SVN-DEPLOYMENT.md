# WordPress.org SVN Deployment Guide

## Repository Information
- **Plugin Slug**: ai-seo-article-generator
- **SVN URL**: https://plugins.svn.wordpress.org/ai-seo-article-generator
- **Username**: ytrofr
- **Last Updated**: July 25, 2025
- **Current Version**: 1.1.0

## ✅ WORKING SOLUTION - GitHub Actions Deployment

### Critical Discovery: Environment Secrets
The key to making GitHub Actions work with WordPress.org SVN is using **Environment Secrets** with proper environment specification in workflows.

### Setup GitHub Environment Secrets
1. Go to GitHub Repository → Settings → Environments
2. Create a "production" environment (if not exists)
3. Add secrets to the production environment:
   - `SVN_USERNAME`: ytrofr
   - `SVN_PASSWORD`: [your SVN password]

**Important**: These must be Environment Secrets, NOT Repository Secrets!

### Working Automated Deployment Methods

#### Method 1: Manual Trigger Deployment (Recommended)
Use `.github/workflows/deploy-svn-simple.yml`:
- Go to Actions → "Simple SVN Deploy" → Run workflow
- Enter version number
- Workflow includes `environment: production` specification

#### Method 2: Tag-Based Deployment
Use `.github/workflows/deploy-wordpress-org.yml`:
```bash
git tag 1.1.1
git push origin 1.1.1
```

### Manual Deployment Process

### 1. Initial Setup
```bash
# Checkout the WordPress.org SVN repository
svn co https://plugins.svn.wordpress.org/ai-seo-article-generator svn-repo
cd svn-repo
```

### 2. Update Trunk with Latest Code
```bash
# Copy all plugin files to trunk (from parent directory)
cp -r ../ai-seo-article-generator.php ../includes/ ../languages/ ../assets/ ../readme.txt trunk/

# Add any new files
svn add trunk/* --force

# Check status
svn status
```

### 3. Commit Changes to Trunk
```bash
# Commit with your credentials
svn ci -m 'Update to version X.X.X' --username ytrofr --password svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2
```

### 4. Create Version Tag
```bash
# Copy trunk to a new version tag
svn cp trunk tags/X.X.X

# Commit the tag
svn ci -m 'Tagging version X.X.X' --username ytrofr --password svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2
```

## Important Notes

1. **Version Consistency**: Always ensure the version number in:
   - `ai-seo-article-generator.php` header
   - `readme.txt` "Stable tag" field
   - SVN tag directory name
   All match exactly.

2. **Assets Directory**: Plugin banners and screenshots should be placed in the `/assets` directory at the root level (not in trunk or tags).

3. **Testing**: Always test your plugin thoroughly before deploying to WordPress.org.

## Automated Deployment

Two GitHub Actions workflows are available for automated deployment:

### 1. Automatic Deployment on Push
- **File**: `.github/workflows/deploy-to-wordpress.yml`
- **Trigger**: Automatically runs when pushing to the `main` branch
- **Action**: Updates trunk and creates version tag if it doesn't exist

### 2. Manual Deployment
- **File**: `.github/workflows/manual-wordpress-deploy.yml`
- **Trigger**: Manual trigger from GitHub Actions tab
- **Options**: 
  - Specify custom version or use plugin file version
  - Choose whether to create a version tag

### Setting Up GitHub Secrets
1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add these repository secrets:
   - `SVN_USERNAME`: ytrofr
   - `SVN_PASSWORD`: svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2

### Before Deployment
1. Update version number in `ai-seo-article-generator.php`
2. Update "Stable tag" in `readme.txt`
3. Update changelog in `readme.txt`
4. Commit and push to main branch

The automatic workflow will handle the rest!

## Quick Commands Reference

```bash
# Check current SVN status
svn status

# See recent commits
svn log -l 5 --username ytrofr --password svn_pd03eKZYSv5GGLQBUjf6gXr2ihqUpmvD2f0777e2

# Update from remote
svn update

# Revert local changes
svn revert -R .
```