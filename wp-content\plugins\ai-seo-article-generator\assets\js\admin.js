jQuery(document).ready(function($) {
    'use strict';
    
    // Language detection and direction setup
    if (typeof ai_seo_article_generator_ajax !== 'undefined' && ai_seo_article_generator_ajax.language) {
        const isHebrew = ai_seo_article_generator_ajax.language.is_hebrew;
        const textDirection = ai_seo_article_generator_ajax.language.text_direction;
        const textAlignment = ai_seo_article_generator_ajax.language.text_alignment;
        
        // Initialize UI language based on WordPress locale
        window.currentArticleLanguage = isHebrew ? 'hebrew' : 'english';
        
        // Apply language-specific styling to admin interface
        $('body').addClass('ai-seo-' + textDirection);
        
        // Set initial direction for all language-aware elements
        $('.ai-seo-article-generator-content-analysis').addClass(textDirection);
        $('#structure-preview').addClass(textDirection);
        
        // Log language detection for debugging
        console.log('AI SEO Article Generator Language Detection:', {
            locale: ai_seo_article_generator_ajax.language.locale,
            isHebrew: isHebrew,
            textDirection: textDirection,
            textAlignment: textAlignment,
            uiLanguage: window.currentArticleLanguage
        });
    } else {
        // Default to English if language detection fails
        window.currentArticleLanguage = 'english';
    }
    
    let currentStep = 'keywords';
    let articleData = {
        main_keyword: '',
        sub_keywords: '',
        target_words: 1000,
        structure: null,
        content: '',
        draft_id: null,
        outbound_links: {}
    };
    
    // Text selection tracking
    let selectedText = '';
    let selectedRange = null;
    
    // UI Translations
    const uiTranslations = {
        english: {
            seo_score: 'SEO Score',
            needs_improvement: 'Needs improvement',
            word_count: 'Word Count',
            words: 'words',
            main_keyword: 'Main Keyword',
            times: 'times',
            heading_structure: 'Heading Structure',
            external_links: 'External Links',
            links: 'links',
            issues_to_improve: 'Issues to Improve',
            content_too_short: 'Content too short (under 500 words)',
            too_few_h2: 'Too few H2 headings',
            sentences_too_long: 'Sentences too long',
            missing_toc: 'Missing table of contents',
            no_external_links: 'No external links',
            keyword_density_too_high: 'Keyword density too high',
            keyword_density_too_low: 'Keyword density too low',
            too_many_h1: 'Too many H1 headings',
            too_many_external_links: 'Too many external links',
            detected_features: 'Detected Features',
            no_special_features: 'No special features detected',
            enhance_improve: 'Improve',
            enhance_seo: 'SEO',
            enhance_expand: 'Expand',
            score_excellent: 'Excellent',
            score_very_good: 'Very Good',
            score_good: 'Good',
            score_ok: 'OK',
            score_needs_improvement: 'Needs Improvement',
            score_needs_fixes: 'Needs Fixes',
            generating_content: 'Generating article of %d words - this may take up to %d minutes (adjusted for article length)...',
            estimated_time: 'Estimated time: about %d minutes',
            save_structure: 'Save Article Structure',
            structure_name: 'Structure Name',
            structure_name_desc: 'Choose a descriptive name for this structure',
            structure_description: 'Description (optional)',
            structure_description_desc: 'Brief description of the structure and its use',
            structure_saved_success: 'Structure saved successfully!',
            structure_save_error: 'Error saving structure',
            cancel: 'Cancel',
            save: 'Save Structure',
            saving_structure: 'Saving structure...',
            draft_saved_success: 'Draft saved successfully',
            creating_post: 'Creating post...',
            article_meets_target_excellent: '\n✅ Article excellently meets your word target!',
            article_meets_target_good: '\n✅ Article meets your word target well!',
            article_has_quality_content: '\n📝 Article contains quality content. Can be expanded with manual editing.',
            article_short_of_target: '\n⚠️ Article is short of target. Consider adding more content or trying again.',
            publish_article: 'Publish Article',
            error_publishing: 'Error publishing article',
            feature_toc: 'Table of Contents',
            feature_faq: 'FAQ Section',
            article_created_success: 'Article created successfully!',
            article_created_editing: 'Article created successfully! Redirecting to editor...',
            toc_title: '📋 Table of Contents',
            toggle_show_hide: 'Show/Hide'
        },
        hebrew: {
            seo_score: 'ציון SEO',
            needs_improvement: 'זקוק לשיפור',
            word_count: 'מספר מילים',
            words: 'מילים',
            main_keyword: 'מילת מפתח ראשית',
            times: 'פעמים',
            heading_structure: 'מבנה כותרות',
            external_links: 'קישורים חיצוניים',
            links: 'קישורים',
            issues_to_improve: 'נושאים לשיפור',
            content_too_short: 'התוכן קצר מדי (מתחת ל-500 מילים)',
            too_few_h2: 'מעט מדי כותרות H2',
            sentences_too_long: 'משפטים ארוכים מדי',
            missing_toc: 'חסר תוכן עניינים',
            no_external_links: 'אין קישורים חיצוניים',
            keyword_density_too_high: 'צפיפות מילת המפתח גבוהה מדי',
            keyword_density_too_low: 'צפיפות מילת המפתח נמוכה מדי',
            too_many_h1: 'יותר מדי כותרות H1',
            too_many_external_links: 'יותר מדי קישורים חיצוניים',
            detected_features: 'תכונות שזוהו',
            no_special_features: 'לא זוהו תכונות מיוחדות',
            enhance_improve: 'שפר',
            enhance_seo: 'SEO',
            enhance_expand: 'הרחב',
            score_excellent: 'מעולה',
            score_very_good: 'טוב מאוד',
            score_good: 'טוב',
            score_ok: 'בסדר',
            score_needs_improvement: 'זקוק לשיפור',
            score_needs_fixes: 'דורש תיקונים',
            generating_content: 'יוצר מאמר של %d מילים - זה עלול לקחת עד %d דקות (מותאם לאורך המאמר)...',
            estimated_time: 'זמן משוער: כ-%d דקות',
            save_structure: 'שמירת מבנה המאמר',
            structure_name: 'שם המבנה',
            structure_name_desc: 'בחר שם מתאר למבנה זה',
            structure_description: 'תיאור (אופציונלי)',
            structure_description_desc: 'תיאור קצר של המבנה ותחום השימוש',
            structure_saved_success: 'המבנה נשמר בהצלחה!',
            structure_save_error: 'שגיאה בשמירת המבנה',
            cancel: 'ביטול',
            save: 'שמור מבנה',
            saving_structure: 'שומר מבנה...',
            draft_saved_success: 'הטיוטה נשמרה בהצלחה',
            creating_post: 'יוצר פוסט...',
            article_meets_target_excellent: '\n✅ המאמר עומד מעולה ביעד המילים שלך!',
            article_meets_target_good: '\n✅ המאמר עומד היטב ביעד המילים שלך!',
            article_has_quality_content: '\n📝 המאמר מכיל תוכן איכותי. ניתן להרחיב בעריכה ידנית.',
            article_short_of_target: '\n⚠️ המאמר קצר מהיעד. מומלץ להוסיף תוכן נוסף או לנסות שוב.',
            publish_article: 'פרסם מאמר',
            error_publishing: 'שגיאה ביצירת המאמר',
            feature_toc: 'תוכן עניינים',
            feature_faq: 'שאלות ותשובות',
            article_created_success: '!המאמר נוצר בהצלחה',
            article_created_editing: '!המאמר נוצר בהצלחה מעבר לעריכה...',
            toc_title: '📋 תוכן עניינים',
            toggle_show_hide: 'הצג/הסתר'
        }
    };
    
    function getUIText(key, ...args) {
        // Use WordPress locale-based language if article language not set
        let lang = window.currentArticleLanguage;
        if (!lang && typeof ai_seo_article_generator_ajax !== 'undefined' && ai_seo_article_generator_ajax.language) {
            lang = ai_seo_article_generator_ajax.language.is_hebrew ? 'hebrew' : 'english';
        }
        lang = lang || 'english'; // Default to English instead of Hebrew
        const translations = uiTranslations[lang] || uiTranslations.english;
        let text = translations[key] || key;
        
        // Replace placeholders with arguments
        if (args.length > 0) {
            args.forEach((arg, index) => {
                text = text.replace(new RegExp('%' + (index + 1) + '\\$?[ds]', 'g'), arg);
            });
        }
        
        return text;
    }
    
    // Initialize
    init();
    
    /**
     * Handle language selection change
     */
    function handleLanguageChange() {
        const selectedLang = $('#article_language').val();
        updateUIDirection(selectedLang);
        updateUITexts(selectedLang);
    }
    
    /**
     * Update UI direction based on language
     */
    function updateUIDirection(language) {
        const $structurePreview = $('#structure-preview');
        const $contentPreview = $('#article-preview');
        const $contentAnalysis = $('.ai-seo-article-generator-content-analysis');
        const $previewContainer = $('.ai-seo-article-generator-preview');
        
        // Determine if Hebrew should be used
        let isHebrew = false;
        if (language === 'hebrew') {
            isHebrew = true;
        } else if (language === 'english') {
            isHebrew = false;
        } else if (language === 'auto') {
            // Use WordPress locale detection from the global variable
            isHebrew = typeof ai_seo_article_generator_ajax !== 'undefined' && 
                      ai_seo_article_generator_ajax.language && 
                      ai_seo_article_generator_ajax.language.is_hebrew;
        }
        
        // Store current language for use in other functions
        window.currentArticleLanguage = isHebrew ? 'hebrew' : 'english';
        
        // Update direction classes
        if (isHebrew) {
            $structurePreview.removeClass('ltr').addClass('rtl');
            $contentPreview.removeClass('ltr').addClass('rtl');
            $contentAnalysis.removeClass('ltr').addClass('rtl');
            $previewContainer.removeClass('ltr').addClass('rtl');
        } else {
            $structurePreview.removeClass('rtl').addClass('ltr');
            $contentPreview.removeClass('rtl').addClass('ltr');
            $contentAnalysis.removeClass('rtl').addClass('ltr');
            $previewContainer.removeClass('rtl').addClass('ltr');
        }
    }
    
    /**
     * Update UI texts based on language
     */
    function updateUITexts(language) {
        // This will be expanded to update progress messages and other UI text
        console.log('Language changed to:', language);
    }
    
    function init() {
        bindEvents();
        
        // Load draft if edit mode or structure if starting from saved structure
        const urlParams = new URLSearchParams(window.location.search);
        const draftId = urlParams.get('draft_id');
        const structureId = urlParams.get('structure_id');
        
        if (draftId) {
            loadDraft(draftId);
        } else if (structureId) {
            loadSavedStructure(structureId);
        }
    }
    
    function bindEvents() {
        // Test API Connection
        $('#test-connection').on('click', testConnection);
        
        // Create Tables
        $('#create-tables').on('click', createTables);
        
        // Language selection handler
        $('#article_language').on('change', handleLanguageChange);
        
        // Keywords Form
        $('#keywords-form').on('submit', generateStructure);
        
        // Background Generation
        $('#queue-background-generation').on('click', queueBackgroundGeneration);
        
        // Structure Actions
        $('#edit-structure').on('click', openStructureEditor);
        $('#save-structure').on('click', saveStructure);
        $('#approve-structure').on('click', approveStructure);
        $('#generate-background-from-structure').on('click', generateBackgroundFromStructure);
        $('#regenerate-structure').on('click', regenerateStructure);
        
        // Structure Editor
        $('#save-structure-edit').on('click', saveStructureEdit);
        $('#cancel-structure').on('click', closeStructureEditor);
        $('.ai-seo-article-generator-close').on('click', closeStructureEditor);
        
        // Article Actions
        $('#copy-article').on('click', copyArticleHTML);
        $('#save-draft').on('click', saveDraft);
        $('#publish-article').on('click', publishArticle);
        
        // Links Manager
        $('#manage-links').on('click', openLinksManager);
        $('#add-link-btn').on('click', addLinkToSelection);
        $('#remove-link-btn').on('click', removeLinkFromSelection);
        $('#close-links-manager').on('click', closeLinksManager);
        $('#google-search-btn').on('click', searchInGoogle);
        
        // Delete Draft
        $('.delete-draft').on('click', deleteDraft);
        
        // Debug Structures
        $('#debug-structures').on('click', debugStructures);
        
        // Retry Failed Jobs
        $(document).on('click', '.retry-job', retryFailedJob);
        
        // Manual Trigger Background Processing
        $('#manual-trigger-background').on('click', manualTriggerBackground);
        
        // Section Enhancement
        $(document).on('click', '.enhance-section', enhanceSection);
        
        // Structure Library Functions (for structures page)
        // Use event delegation to handle dynamically loaded content
        $(document).on('click', '.use-structure', function() {
            const structureId = $(this).data('id');
            
            $.ajax({
                url: ai_seo_article_generator_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ai_seo_article_generator_load_structure',
                    nonce: ai_seo_article_generator_ajax.nonce,
                    structure_id: structureId
                },
                success: function(response) {
                    if (response.success) {
                        // Redirect to new article page with structure data
                        window.location.href = 'admin.php?page=ai-seo-article-generator-new&structure_id=' + structureId;
                    } else {
                        showNotice(response.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error loading structure', 'error');
                }
            });
        });
        
        $(document).on('click', '.preview-structure', function() {
            const structureId = $(this).data('id');
                
            $.ajax({
                url: ai_seo_article_generator_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ai_seo_article_generator_load_structure',
                    nonce: ai_seo_article_generator_ajax.nonce,
                    structure_id: structureId
                },
                success: function(response) {
                        
                    if (response.success && response.structure) {
                        
                        // Validate and fix the structure data
                        const validatedStructure = validateStructure(response.structure.structure_data);
                        
                        if (validatedStructure) {
                            displayStructure(validatedStructure);
                            $('#structure-preview-content').html($('#structure-preview').html());
                            $('#structure-preview-modal').show();
                            
                            $('#use-previewed-structure').off('click').on('click', function() {
                                window.location.href = 'admin.php?page=ai-seo-article-generator-new&structure_id=' + structureId;
                            });
                            
                        } else {
                                showNotice('The saved structure is corrupted and cannot be displayed', 'error');
                        }
                    } else {
                        showNotice(response.message || 'Error loading structure', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showNotice('Error loading structure', 'error');
                }
            });
        });
        
        $(document).on('click', '.delete-structure', function() {
            if (!confirm('Are you sure you want to delete this structure?')) {
                return;
            }
            
            const structureId = $(this).data('id');
            const $row = $(this).closest('tr');
            
            $.ajax({
                url: ai_seo_article_generator_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ai_seo_article_generator_delete_structure',
                    nonce: ai_seo_article_generator_ajax.nonce,
                    structure_id: structureId
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut();
                        showNotice('Structure deleted successfully', 'success');
                    } else {
                        showNotice(response.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error deleting structure', 'error');
                }
            });
        });
        
        // Modal Close
        $(window).on('click', function(e) {
            if ($(e.target).hasClass('ai-seo-article-generator-modal')) {
                closeStructureEditor();
                closeLinksManager();
            }
        });
        
        // Text selection handling for article preview
        $(document).on('mouseup', '#article-preview', handleTextSelection);
        $(document).on('keyup', '#article-preview', handleTextSelection);
    }
    
    function testConnection() {
        const $button = $('#test-connection');
        const $status = $('#connection-status');
        
        $button.prop('disabled', true).text(ai_seo_article_generator_ajax.i18n.testing_connection);
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_test_connection',
                nonce: ai_seo_article_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $status.html('<span class=\"ai-seo-article-generator-status-success\">✅ ' + response.message + '</span>');
                    showNotice(response.message, 'success');
                } else {
                    $status.html('<span class=\"ai-seo-article-generator-status-error\">❌ ' + response.message + '</span>');
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                $status.html('<span class=\"ai-seo-article-generator-status-error\">❌ Connection test error</span>');
                showNotice('Connection test error', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Connection');
            }
        });
    }
    
    function createTables() {
        const $button = $('#create-tables');
        const $status = $('#database-status');
        
        $button.prop('disabled', true).text('Creating tables...');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_create_tables',
                nonce: ai_seo_article_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $status.html('<span class="ai-seo-article-generator-status-success">✅ Tables created successfully</span>');
                    showNotice(response.message, 'success');
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('Error creating tables', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text('Recreate Tables');
            }
        });
    }
    
    function queueBackgroundGeneration(e) {
        e.preventDefault();
        
        const formData = {
            main_keyword: $('#main_keyword').val().trim(),
            sub_keywords: $('#sub_keywords').val().trim(),
            target_words: parseInt($('#target_words').val()),
            article_language: $('#article_language').val() || 'auto'
        };
        
        if (!formData.main_keyword) {
            showNotice('Please enter a main keyword', 'error');
            return;
        }
        
        const $button = $('#queue-background-generation');
        $button.prop('disabled', true).text('Queuing...');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_queue_background_generation',
                nonce: ai_seo_article_generator_ajax.nonce,
                ...formData
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.message, 'success');
                    
                    // Show additional info about background processing
                    const jobsPageUrl = window.location.origin + window.location.pathname + '?page=ai-seo-article-generator-jobs';
                    const additionalInfo = '<div class="notice notice-info"><p><strong>Background Generation Started!</strong><br>' +
                        'Job ID: ' + response.job_id + '<br>' +
                        'You will receive an email when your article is ready.<br>' +
                        'You can track progress in <a href="' + jobsPageUrl + '">Background Jobs</a>.</p></div>';
                    
                    $('.ai-seo-article-generator-wrap').prepend(additionalInfo);
                    
                    // Reset form
                    $('#keywords-form')[0].reset();
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('Error queuing background generation', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text('Queue Background Generation');
            }
        });
    }
    
    function generateStructure(e) {
        e.preventDefault();
        
        const formData = {
            main_keyword: $('#main_keyword').val().trim(),
            sub_keywords: $('#sub_keywords').val().trim(),
            target_words: parseInt($('#target_words').val()),
            article_language: $('#article_language').val() || 'auto'
        };
        
        
        if (!formData.main_keyword) {
            showNotice('Please enter a main keyword', 'error');
            return;
        }
        
        articleData = { ...articleData, ...formData };
        
        console.log('🎯 About to call showStep(structure)...');
        showStep('structure');
        console.log('✅ showStep(structure) called, now calling showLoading...');
        showLoading('#structure-preview', ai_seo_article_generator_ajax.i18n.generating_structure, 'up to one minute');
        console.log('🔄 showLoading called');
        
        const ajaxData = {
            action: 'ai_seo_article_generator_generate_structure',
            nonce: ai_seo_article_generator_ajax.nonce,
            ...formData
        };
        
        // Calculate dynamic timeout using configurable admin settings
        // Use admin-configured maximum timeout values
        const targetWords = parseInt($('#target_words').val()) || 1000;
        const maxStructureTimeout = ai_seo_article_generator_ajax.timeouts.max_structure_timeout || 300;
        const baseTimeout = Math.min(90, maxStructureTimeout * 0.3);
        const backendTimeout = Math.max(baseTimeout, Math.min(maxStructureTimeout, baseTimeout + (targetWords / 1000) * 30));
        const dynamicTimeout = (backendTimeout + 30) * 1000; // Add 30s buffer and convert to ms
        const timeoutMinutes = Math.ceil(dynamicTimeout / 60000);
        
        console.log(`🔧 Structure timeout calculation: ${targetWords} words -> ${backendTimeout}s backend + 30s buffer = ${dynamicTimeout/1000}s frontend (admin max: ${maxStructureTimeout}s)`);
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: ajaxData,
            timeout: dynamicTimeout,
            beforeSend: function() {
                showNotice(`Preparing article structure with Claude Sonnet 4 - this may take up to ${timeoutMinutes} minutes for long articles...`, 'info');
            },
            success: function(response) {
                console.log('🎉 AJAX Success! Response:', response);
                if (response && response.success) {
                    console.log('✅ Response success=true, structure:', response.structure);
                    articleData.structure = response.structure;
                    console.log('📝 articleData.structure set, calling displayStructure...');
                    displayStructure(response.structure);
                    console.log('🎯 displayStructure called, showing success notice...');
                    showNotice('Article structure created successfully!', 'success');
                    console.log('✅ Success handler completed');
                } else {
                    const errorMessage = response && response.message ? response.message : 'Unknown error creating article structure';
                    console.log('❌ Response success=false, error:', errorMessage);
                    showNotice(errorMessage, 'error');
                    showStep('keywords');
                }
            },
            error: function(xhr, status, error) {
                console.log('❌ AJAX Error Handler Called!');
                console.log('📊 XHR Status:', xhr.status);
                console.log('📊 Status:', status);
                console.log('📊 Error:', error);
                console.log('📊 Response Text:', xhr.responseText);
                console.log('📊 Response Headers:', xhr.getAllResponseHeaders());
                
                // Try to parse response text as JSON to see if it's actually successful
                try {
                    const parsedResponse = JSON.parse(xhr.responseText);
                    console.log('🔍 Parsed Response:', parsedResponse);
                    if (parsedResponse && parsedResponse.success) {
                        console.log('⚠️ Response was actually successful! Calling success handler manually...');
                        // Manually call success handler if response is actually successful
                        if (parsedResponse.structure) {
                            articleData.structure = parsedResponse.structure;
                            displayStructure(parsedResponse.structure);
                            showNotice('Article structure created successfully! (completed after timeout)', 'success');
                            return;
                        }
                    }
                } catch (parseError) {
                    console.log('❌ Could not parse response as JSON:', parseError);
                }
                
                let errorMessage = 'Error creating article structure';
                let showBackgroundOption = false;
                
                if (xhr.status === 503) {
                    errorMessage = 'Server is temporarily overloaded (503 error). This is common with long article generation.';
                    showBackgroundOption = true;
                } else if (xhr.status === 502 || xhr.status === 504) {
                    errorMessage = 'Server gateway timeout. The generation process may be too intensive for immediate processing.';
                    showBackgroundOption = true;
                } else if (status === 'timeout') {
                    const timeoutSeconds = Math.floor(dynamicTimeout / 1000);
                    errorMessage = `Article structure creation is taking longer than expected (${timeoutSeconds} seconds). This can happen with complex articles of ${targetWords} words.`;
                    showBackgroundOption = true;
                } else if (error.includes('timeout')) {
                    errorMessage = 'Claude Sonnet 4 takes time to create quality structure. The generation process may be too intensive for immediate processing.';
                    showBackgroundOption = true;
                } else {
                    errorMessage = `Error creating article structure: ${status} - ${error}`;
                }
                
                console.log('🔄 Showing error notice and returning to keywords step');
                showNotice(errorMessage, 'error');
                
                // Show background generation suggestion for server overload errors
                if (showBackgroundOption) {
                    const backgroundSuggestion = '<div class="notice notice-info" style="margin-top: 10px;"><p><strong>💡 Suggestion:</strong> Try using <strong>"Queue Background Generation"</strong> instead. This will process your article on the server without timeout issues and email you when ready.</p></div>';
                    $('.ai-seo-article-generator-wrap').prepend(backgroundSuggestion);
                }
                
                showStep('keywords');
            },
            complete: function() {
            }
        });
    }
    
    function displayStructure(structure) {
        console.log('🏗️ displayStructure called with:', structure);
        console.log('📍 Target element #structure-preview exists:', $('#structure-preview').length > 0);
        
        // Validate structure before displaying
        if (!structure || typeof structure !== 'object') {
            console.log('⚠️ Invalid structure, creating default');
            structure = createDefaultStructure();
        }
        
        let html = '<div class=\"structure-display\">';
        
        // Safely display title
        const title = structure.title || 'Article Structure';
        html += '<h2>' + escapeHtml(title) + '</h2>';
        
        // Safely display introduction
        if (structure.introduction) {
            html += '<div class=\"structure-section\">';
            html += '<h3>Introduction</h3>';
            html += '<p>' + escapeHtml(structure.introduction) + '</p>';
            html += '</div>';
        }
        
        if (structure.sections && Array.isArray(structure.sections) && structure.sections.length > 0) {
            structure.sections.forEach(function(section, index) {
                // Safety check for section object
                if (!section || typeof section !== 'object') {
                    return;
                }
                
                html += '<div class=\"structure-section\">';
                
                // Safely display heading
                const heading = section.heading || `Chapter ${index + 1}`;
                html += '<h3>' + escapeHtml(heading) + '</h3>';
                
                // Safely display subheadings
                if (section.subheadings && Array.isArray(section.subheadings) && section.subheadings.length > 0) {
                    html += '<ul>';
                    section.subheadings.forEach(function(subheading) {
                        if (subheading && typeof subheading === 'string') {
                            html += '<li>' + escapeHtml(subheading) + '</li>';
                        }
                    });
                    html += '</ul>';
                }
                
                // Safely display topics
                if (section.topics && Array.isArray(section.topics) && section.topics.length > 0) {
                    const validTopics = section.topics.filter(topic => topic && typeof topic === 'string');
                    if (validTopics.length > 0) {
                        html += '<p><strong>Topics:</strong> ' + validTopics.map(escapeHtml).join(', ') + '</p>';
                    }
                }
                
                // Safely display word count
                if (section.word_count && typeof section.word_count === 'number') {
                    html += '<p><strong>Estimated words:</strong> ' + section.word_count + '</p>';
                }
                
                html += '</div>';
            });
        }
        
        // Safely display conclusion
        if (structure.conclusion && typeof structure.conclusion === 'string') {
            html += '<div class=\"structure-section\">';
            html += '<h3>Summary</h3>';
            html += '<p>' + escapeHtml(structure.conclusion) + '</p>';
            html += '</div>';
        }
        
        // Safely display multimedia suggestions
        if (structure.multimedia_suggestions && Array.isArray(structure.multimedia_suggestions) && structure.multimedia_suggestions.length > 0) {
            const validSuggestions = structure.multimedia_suggestions.filter(suggestion => suggestion && typeof suggestion === 'string');
            if (validSuggestions.length > 0) {
                html += '<div class=\"structure-section\">';
                html += '<h3>הצעות מולטימדיה</h3>';
                html += '<ul>';
                validSuggestions.forEach(function(suggestion) {
                    html += '<li>' + escapeHtml(suggestion) + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }
        }
        
        html += '</div>';
        
        console.log('🎯 Setting HTML content, length:', html.length);
        $('#structure-preview').html(html);
        console.log('✅ displayStructure completed. Content in preview:', $('#structure-preview').html().length);
    }
    
    function approveStructure() {
        if (!articleData.structure) {
            showNotice('אין מבנה לאישור', 'error');
            return;
        }
        
        showStep('content');
        generateContent();
    }
    
    function generateBackgroundFromStructure() {
        if (!articleData.structure) {
            showNotice('No structure available for background generation', 'error');
            return;
        }
        
        if (!articleData.main_keyword) {
            showNotice('Missing main keyword data', 'error');
            return;
        }
        
        const $button = $('#generate-background-from-structure');
        $button.prop('disabled', true).text('Queuing...');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_queue_background_generation',
                nonce: ai_seo_article_generator_ajax.nonce,
                main_keyword: articleData.main_keyword,
                sub_keywords: articleData.sub_keywords,
                target_words: articleData.target_words,
                structure_data: JSON.stringify(articleData.structure)
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.message, 'success');
                    
                    // Show additional info about background processing
                    const jobsPageUrl = window.location.origin + window.location.pathname + '?page=ai-seo-article-generator-jobs';
                    const additionalInfo = '<div class="notice notice-info"><p><strong>Background Generation Started!</strong><br>' +
                        'Job ID: ' + response.job_id + '<br>' +
                        'Article will be generated from your approved structure.<br>' +
                        'You will receive an email when your article is ready.<br>' +
                        'You can track progress in <a href="' + jobsPageUrl + '">Background Jobs</a>.</p></div>';
                    
                    $('.ai-seo-article-generator-wrap').prepend(additionalInfo);
                    
                    // Optionally redirect to jobs page after a few seconds
                    setTimeout(function() {
                        if (confirm('Would you like to view the Background Jobs page to track progress?')) {
                            window.location.href = jobsPageUrl;
                        }
                    }, 3000);
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('Error queuing background generation', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text('Generate Article in Background');
            }
        });
    }
    
    function generateContent() {
        // More realistic time estimates for Hebrew content generation
        const estimatedMinutes = Math.ceil(articleData.target_words / 300); // More conservative: 300 words per minute
        const timeEstimate = estimatedMinutes === 1 ? 'כדקה אחת' : `כ-${estimatedMinutes} דקות`;
        
        showLoading('#content-progress', ai_seo_article_generator_ajax.i18n.generating_content, timeEstimate);
        
        // Calculate timeout using configurable admin settings
        // Use admin-configured maximum timeout values for content generation
        const maxContentTimeout = ai_seo_article_generator_ajax.timeouts.max_content_timeout || 600;
        const baseContentTimeout = Math.min(180, maxContentTimeout * 0.3);
        const backendContentTimeout = Math.max(baseContentTimeout, Math.min(maxContentTimeout, articleData.target_words / 4));
        const contentTimeout = (backendContentTimeout + 60) * 1000; // Add 60s buffer and convert to ms
        
        console.log(`🔧 Content timeout calculation: ${articleData.target_words} words -> ${backendContentTimeout}s backend + 60s buffer = ${contentTimeout/1000}s frontend (admin max: ${maxContentTimeout}s)`);
        
        const estimatedTimeouts = Math.ceil(contentTimeout / 60000);
        const formattedMessage = getUIText('generating_content').replace('%d', articleData.target_words).replace('%d', estimatedTimeouts);
        showNotice(formattedMessage, 'info');
        
        // Add progress indicator with periodic updates
        let progressCounter = 0;
        const progressInterval = setInterval(() => {
            progressCounter += 15;
            const progressPercent = Math.min(95, (progressCounter / (contentTimeout / 1000)) * 100);
            const remainingSeconds = Math.max(0, Math.floor((contentTimeout - progressCounter * 1000) / 1000));
            const remainingMinutes = Math.floor(remainingSeconds / 60);
            const remainingSecondsDisplay = remainingSeconds % 60;
            
            if (remainingMinutes > 0) {
                showNotice(`Generating article... Estimated time remaining: ${remainingMinutes}:${remainingSecondsDisplay.toString().padStart(2, '0')} minutes (${Math.round(progressPercent)}%)`, 'info');
            } else if (remainingSeconds > 10) {
                showNotice(`Generating article... Estimated time remaining: ${remainingSeconds} seconds`, 'info');
            } else {
                showNotice('Generating article... Finishing...', 'info');
            }
        }, 15000); // Update every 15 seconds
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_generate_content',
                nonce: ai_seo_article_generator_ajax.nonce,
                structure: JSON.stringify(articleData.structure),
                main_keyword: articleData.main_keyword,
                sub_keywords: articleData.sub_keywords,
                target_words: articleData.target_words,
                article_language: articleData.article_language || 'auto'
            },
            timeout: contentTimeout,
            success: function(response) {
                clearInterval(progressInterval); // Stop progress updates
                
                if (response.success) {
                    articleData.content = response.content;
                    showStep('preview');
                    displayArticlePreview(response.content);
                    
                    // Show word count information with improved messaging
                    if (response.word_count) {
                        const percentage = Math.round((response.word_count / articleData.target_words) * 100);
                        let message = `המאמר נוצר בהצלחה! 🎉`;
                        message += `\nמספר מילים: ${response.word_count} מתוך יעד של ${articleData.target_words} (${percentage}%)`;
                        
                        // Add generation method info if available
                        if (response.generation_method === 'section_by_section') {
                            message += `\n🔧 נוצר בשיטת יצירה מתקדמת (Chapter אחר פרק)`;
                        }
                        
                        let noticeType = 'success';
                        if (response.word_count >= articleData.target_words * 0.9) {
                            message += getUIText('article_meets_target_excellent');
                        } else if (response.word_count >= articleData.target_words * 0.8) {
                            message += getUIText('article_meets_target_good');
                        } else if (response.word_count >= articleData.target_words * 0.6) {
                            message += getUIText('article_has_quality_content');
                            noticeType = 'info';
                        } else {
                            message += getUIText('article_short_of_target');
                            noticeType = 'warning';
                        }
                        
                        showNotice(message, noticeType);
                    } else {
                        showNotice(getUIText('article_created_success') + ' 🎉', 'success');
                    }
                } else {
                    showNotice(response.message, 'error');
                    showStep('structure');
                }
            },
            error: function(xhr, status, error) {
                clearInterval(progressInterval); // Stop progress updates
                
                let errorMessage = 'שגיאה ביצירת התוכן';
                let showBackgroundOption = false;
                
                if (xhr.status === 503) {
                    errorMessage = 'השרת עמוס זמנית (שגיאת 503). זה נפוץ עם יצירת מאמרים ארוכים.';
                    showBackgroundOption = true;
                } else if (xhr.status === 502 || xhr.status === 504) {
                    errorMessage = 'השרת לא מגיב. תהליך היצירה עלול להיות כבד מדי לעיבוד מיידי.';
                    showBackgroundOption = true;
                } else if (status === 'timeout') {
                    const timeoutMinutesContent = Math.ceil(contentTimeout / 60000);
                    errorMessage = `יצירת מאמר של ${articleData.target_words} מילים לוקחת זמן רב מהצפוי (${timeoutMinutesContent} דקות). זה נפוץ עם מאמרים מורכבים.`;
                    showBackgroundOption = true;
                } else if (error.includes('timeout')) {
                    errorMessage = 'Claude Sonnet 4 לוקח זמן רב ליצירת תוכן איכותי. תהליך היצירה עלול להיות כבד מדי לעיבוד מיידי.';
                    showBackgroundOption = true;
                } else {
                    errorMessage = `שגיאה ביצירת התוכן: ${status} - ${error}`;
                }
                
                showNotice(errorMessage, 'error');
                
                // Show background generation suggestion for server overload errors
                if (showBackgroundOption) {
                    const backgroundSuggestion = '<div class="notice notice-info" style="margin-top: 10px;"><p><strong>💡 הצעה:</strong> נסה להשתמש ב-<strong>"יצירה ברקע"</strong> במקום. זה יעבד את המאמר בשרת ללא בעיות זמן ויישלח לך מייל כשמוכן.</p></div>';
                    $('.ai-seo-article-generator-wrap').prepend(backgroundSuggestion);
                }
                
                showStep('structure');
            }
        });
    }
    
    function displayArticlePreview(content) {
        let enhancedContent = addEnhancementControls(content);
        $('#article-preview').html(enhancedContent);
        
        // Perform content analysis
        analyzeContent(content);
    }
    
    function analyzeContent(content) {
        try {
            if (!content || !articleData.main_keyword) {
                console.log('Skipping content analysis - missing content or keyword');
                return;
            }
            
            console.log('Analyzing content with keyword:', articleData.main_keyword);
            
            // Basic content analysis
            const analysis = performContentAnalysis(content, articleData.main_keyword, articleData.sub_keywords);
            displayContentAnalysis(analysis);
            
        } catch (error) {
            console.error('Error in analyzeContent:', error);
            $('#content-analysis-results').html('<div class="analysis-loading">Error analyzing content</div>');
        }
    }
    
    function performContentAnalysis(content, mainKeyword, subKeywords) {
        try {
            // Clean content for analysis
            const cleanContent = cleanContentForAnalysis(content);
            const text = cleanContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
            const words = text.split(' ').filter(word => word.length > 0);
            const wordCount = words.length;
        
            // Keyword density analysis
            const mainKeywordCount = (text.match(new RegExp(escapeRegExp(mainKeyword), 'gi')) || []).length;
            const mainKeywordDensity = ((mainKeywordCount / words.length) * 100).toFixed(2);
            
            // Subkeywords analysis
            const subKeywordsList = subKeywords ? subKeywords.split(',').map(k => k.trim()) : [];
            const subKeywordAnalysis = subKeywordsList.map(keyword => {
                const count = (text.match(new RegExp(escapeRegExp(keyword), 'gi')) || []).length;
                const density = ((count / words.length) * 100).toFixed(2);
                return { keyword, count, density };
            });
            
            // Heading analysis (use cleaned content)
            const headings = {
                h1: (cleanContent.match(/<h1[^>]*>/gi) || []).length,
                h2: (cleanContent.match(/<h2[^>]*>/gi) || []).length,
                h3: (cleanContent.match(/<h3[^>]*>/gi) || []).length,
                h4: (cleanContent.match(/<h4[^>]*>/gi) || []).length
            };
            
            // Readability analysis (simplified)
            const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
            const avgWordsPerSentence = sentences > 0 ? (wordCount / sentences).toFixed(1) : '0';
        
        // SEO scoring
        let seoScore = 0;
        let seoIssues = [];
        
        // Word count scoring
        if (wordCount >= 800) seoScore += 20;
        else if (wordCount >= 500) seoScore += 15;
        else seoIssues.push(getUIText('content_too_short'));
        
        // Keyword density scoring
        if (mainKeywordDensity >= 1 && mainKeywordDensity <= 3) seoScore += 20;
        else if (mainKeywordDensity > 3) seoIssues.push(getUIText('keyword_density_too_high'));
        else seoIssues.push(getUIText('keyword_density_too_low'));
        
        // Heading structure scoring
        if (headings.h2 >= 3) seoScore += 15;
        else seoIssues.push(getUIText('too_few_h2'));
        
        if (headings.h1 <= 1) seoScore += 10;
        else seoIssues.push(getUIText('too_many_h1'));
        
        // Content structure scoring
        if (avgWordsPerSentence <= 20) seoScore += 10;
        else seoIssues.push(getUIText('sentences_too_long'));
        
            // TOC detection
            const hasTOC = cleanContent.includes('ai-seo-article-generator-toc');
            if (hasTOC) seoScore += 10;
            else seoIssues.push(getUIText('missing_toc'));
            
            // FAQ detection
            const hasFAQ = /(?:שאלה|ש:)|(?:מהו|מה זה|איך|למה|מתי|איפה)/i.test(text);
            if (hasFAQ) seoScore += 15;
            
            // Outbound links analysis (safe counting)
            const outboundLinksCount = Object.keys(articleData.outbound_links || {}).length;
            let contentLinksCount = 0;
            try {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = cleanContent;
                contentLinksCount = tempDiv.querySelectorAll('a[data-link-id]').length;
            } catch (e) {
                // Fallback to regex if DOM parsing fails
                contentLinksCount = (cleanContent.match(/data-link-id/g) || []).length;
            }
            const actualLinksCount = Math.max(outboundLinksCount, contentLinksCount);
            
            if (actualLinksCount >= 2 && actualLinksCount <= 5) {
                seoScore += 10;
            } else if (actualLinksCount === 0) {
                seoIssues.push(getUIText('no_external_links'));
            } else if (actualLinksCount > 8) {
                seoIssues.push(getUIText('too_many_external_links'));
            }
            
            return {
                wordCount,
                mainKeyword: {
                    keyword: mainKeyword,
                    count: mainKeywordCount,
                    density: mainKeywordDensity
                },
                subKeywords: subKeywordAnalysis,
                headings,
                readability: {
                    sentences,
                    avgWordsPerSentence
                },
                outboundLinks: {
                    count: actualLinksCount,
                    links: Object.values(articleData.outbound_links || {})
                },
                seo: {
                    score: Math.min(100, seoScore),
                    issues: seoIssues
                },
                features: {
                    hasTOC,
                    hasFAQ
                }
            };
            
        } catch (error) {
            console.error('Error in performContentAnalysis:', error);
            // Return a fallback analysis
            return {
                wordCount: 0,
                mainKeyword: { keyword: mainKeyword, count: 0, density: 0 },
                subKeywords: [],
                headings: { h1: 0, h2: 0, h3: 0, h4: 0 },
                readability: { sentences: 0, avgWordsPerSentence: 0 },
                outboundLinks: { count: 0, links: [] },
                seo: { score: 0, issues: ['שגיאה בניתוח התוכן'] },
                features: { hasTOC: false, hasFAQ: false }
            };
        }
    }
    
    function cleanContentForAnalysis(content) {
        if (!content) return '';
        
        // Remove enhancement controls first
        let cleaned = removeEnhancementControls(content);
        
        // Remove markdown artifacts
        cleaned = cleaned
            .replace(/```html\s*/g, '')
            .replace(/```\s*/g, '')
            .replace(/<!--.*?-->/gs, ''); // Remove HTML comments
            
        // Decode URL-encoded characters (common in Hebrew content)
        try {
            cleaned = decodeURIComponent(cleaned.replace(/\+/g, ' '));
        } catch (e) {
            // If decoding fails, continue with original content
        }
        
        // Clean up excessive whitespace
        cleaned = cleaned.replace(/\s+/g, ' ').trim();
        
        return cleaned;
    }
    
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    function displayContentAnalysis(analysis) {
        try {
            let html = '<div class="content-analysis-grid">';
            
            // SEO Score
            const scoreClass = analysis.seo.score >= 80 ? 'excellent' : analysis.seo.score >= 60 ? 'good' : 'needs-improvement';
            html += '<div class="analysis-card seo-score ' + scoreClass + '">';
            html += '<h4>🎯 ' + getUIText('seo_score') + '</h4>';
            html += '<div class="score-circle">' + analysis.seo.score + '</div>';
            html += '<div class="score-label">' + getScoreLabel(analysis.seo.score) + '</div>';
            html += '</div>';
            
            // Word Count
            html += '<div class="analysis-card word-count">';
            html += '<h4>📝 ' + getUIText('word_count') + '</h4>';
            html += '<div class="metric-value">' + analysis.wordCount.toLocaleString() + '</div>';
            html += '<div class="metric-label">' + getUIText('words') + '</div>';
            html += '</div>';
            
            // Main Keyword
            const keywordClass = analysis.mainKeyword.density >= 1 && analysis.mainKeyword.density <= 3 ? 'good' : 'warning';
            html += '<div class="analysis-card keyword-density ' + keywordClass + '">';
            html += '<h4>🔑 ' + getUIText('main_keyword') + '</h4>';
            html += '<div class="metric-value">' + analysis.mainKeyword.density + '%</div>';
            html += '<div class="metric-label">' + analysis.mainKeyword.count + ' ' + getUIText('times') + '</div>';
            html += '</div>';
            
            // Headings Structure
            html += '<div class="analysis-card headings">';
            html += '<h4>📋 ' + getUIText('heading_structure') + '</h4>';
            html += '<div class="headings-breakdown">';
            html += 'H1: ' + analysis.headings.h1 + ' | ';
            html += 'H2: ' + analysis.headings.h2 + ' | ';
            html += 'H3: ' + analysis.headings.h3;
            html += '</div>';
            html += '</div>';
            
            // Outbound Links
            const linksClass = analysis.outboundLinks.count >= 2 && analysis.outboundLinks.count <= 5 ? 'good' : 
                              analysis.outboundLinks.count === 0 ? 'needs-improvement' : 'warning';
            html += '<div class="analysis-card outbound-links ' + linksClass + '">';
            html += '<h4>🔗 ' + getUIText('external_links') + '</h4>';
            html += '<div class="metric-value">' + analysis.outboundLinks.count + '</div>';
            html += '<div class="metric-label">' + getUIText('links') + '</div>';
            if (analysis.outboundLinks.count > 0) {
                html += '<div class="links-preview">';
                analysis.outboundLinks.links.slice(0, 2).forEach(function(link) {
                    html += '<div class="link-preview-item">' + escapeHtml(link.text) + '</div>';
                });
                if (analysis.outboundLinks.count > 2) {
                    html += '<div class="link-preview-more">ועוד ' + (analysis.outboundLinks.count - 2) + ' קישורים...</div>';
                }
                html += '</div>';
            }
            html += '</div>';
            
            html += '</div>';
            
            // SEO Issues
            if (analysis.seo.issues.length > 0) {
                html += '<div class="seo-issues">';
                html += '<h4>⚠️ ' + getUIText('issues_to_improve') + '</h4>';
                html += '<ul>';
                analysis.seo.issues.forEach(issue => {
                    // Translate issue key if it exists, otherwise use as-is
                    const translatedIssue = getUIText(issue) !== issue ? getUIText(issue) : issue;
                    html += '<li>' + translatedIssue + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }
            
            // Features detected
            html += '<div class="detected-features">';
            html += '<h4>' + getUIText('detected_features') + '</h4>';
            html += '<div class="features-list">';
            if (analysis.features.hasTOC) html += '<span class="feature-badge">📋 ' + getUIText('feature_toc') + '</span>';
            if (analysis.features.hasFAQ) html += '<span class="feature-badge">❓ ' + getUIText('feature_faq') + '</span>';
            if (!analysis.features.hasTOC && !analysis.features.hasFAQ) {
                html += '<span class="feature-badge inactive">' + getUIText('no_special_features') + '</span>';
            }
            html += '</div>';
            html += '</div>';
            
            // Ensure container exists before updating
            const $container = $('#content-analysis-results');
            if ($container.length === 0) {
                console.error('Content analysis container not found');
                return;
            }
            
            $container.html(html);
            
        } catch (error) {
            console.error('Error displaying content analysis:', error);
            $('#content-analysis-results').html('<div class="analysis-loading">Error analyzing content</div>');
        }
    }
    
    function getScoreLabel(score) {
        if (score >= 90) return getUIText('score_excellent');
        if (score >= 80) return getUIText('score_very_good');
        if (score >= 70) return getUIText('score_good');
        if (score >= 60) return getUIText('score_ok');
        if (score >= 50) return getUIText('score_needs_improvement');
        return getUIText('score_needs_fixes');
    }
    
    function addEnhancementControls(content) {
        // Remove any existing enhancement controls first
        let cleanContent = removeEnhancementControls(content);
        
        // Add enhancement controls to paragraphs only (not inside existing enhancement divs)
        let enhanced = cleanContent.replace(/<p>(?!.*class="section-enhance")(.*?)<\/p>/g, function(match, p1) {
            // Skip if paragraph is empty or contains only whitespace
            if (!p1 || p1.trim() === '') {
                return match;
            }
            
            return '<div class="section-enhance">' +
                   '<div class="section-enhance-controls">' +
                   '<button class="enhance-section" data-type="improve">' + getUIText('enhance_improve') + '</button>' +
                   '<button class="enhance-section" data-type="seo">' + getUIText('enhance_seo') + '</button>' +
                   '<button class="enhance-section" data-type="expand">' + getUIText('enhance_expand') + '</button>' +
                   '</div>' +
                   '<p>' + p1 + '</p>' +
                   '</div>';
        });
        
        return enhanced;
    }
    
    function removeEnhancementControls(content) {
        // Remove enhancement control wrappers while preserving the actual content
        return content
            .replace(/<div class="section-enhance-controls">.*?<\/div>/g, '')
            .replace(/<div class="section-enhance">/g, '')
            .replace(/<\/div>(?=\s*<\/div>)/g, '') // Remove closing divs that were enhancement wrappers
            .replace(/(<p>.*?<\/p>)\s*<\/div>/g, '$1'); // Clean up remaining wrapper closings
    }
    
    function getCleanContentForAnalysis(content) {
        // Get content without enhancement controls for analysis and storage
        return removeEnhancementControls(content);
    }
    
    function enhanceSection(e) {
        e.preventDefault();
        
        const $button = $(this);
        const $section = $button.closest('.section-enhance');
        const sectionContent = $section.find('p').html();
        const enhancementType = $button.data('type');
        
        $button.prop('disabled', true).text('משפר...');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_enhance_section',
                nonce: ai_seo_article_generator_ajax.nonce,
                section_content: sectionContent,
                main_keyword: articleData.main_keyword,
                enhancement_type: enhancementType
            },
            success: function(response) {
                if (response.success) {
                    $section.find('p').html(response.content);
                    showNotice('הקטע שופר בהצלחה', 'success');
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('שגיאה בשיפור הקטע', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text($button.data('type') === 'improve' ? 'שפר' : 
                                                      $button.data('type') === 'seo' ? 'SEO' : 'הרחב');
            }
        });
    }
    
    function openStructureEditor() {
        if (!articleData.structure) {
            showNotice('אין מבנה לעריכה', 'error');
            return;
        }
        
        buildStructureEditor();
        $('#structure-editor-modal').show();
    }
    
    function buildStructureEditor() {
        let html = '<div class=\"structure-editor-form\">';
        
        // Title
        html += '<div class=\"structure-item\">';
        html += '<label>כותרת ראשית:</label>';
        html += '<input type=\"text\" id=\"edit-title\" value=\"' + escapeHtml(articleData.structure.title || '') + '\">';
        html += '</div>';
        
        // Introduction
        html += '<div class=\"structure-item\">';
        html += '<label>Introduction:</label>';
        html += '<textarea id=\"edit-introduction\" rows=\"3\">' + escapeHtml(articleData.structure.introduction || '') + '</textarea>';
        html += '</div>';
        
        // Sections
        if (articleData.structure.sections) {
            html += '<h3>פרקים:</h3>';
            articleData.structure.sections.forEach(function(section, index) {
                html += '<div class=\"structure-item\" data-section=\"' + index + '\">';
                html += '<span class=\"remove-item\" onclick=\"removeStructureItem(this)\">×</span>';
                html += '<label>כותרת פרק:</label>';
                html += '<input type=\"text\" class=\"section-heading\" value=\"' + escapeHtml(section.heading || '') + '\">';
                html += '<label>תת-כותרות (הפרד בפסיקים):</label>';
                html += '<input type=\"text\" class=\"section-subheadings\" value=\"' + escapeHtml((section.subheadings || []).join(', ')) + '\">';
                html += '<label>נושאים (הפרד בפסיקים):</label>';
                html += '<input type=\"text\" class=\"section-topics\" value=\"' + escapeHtml((section.topics || []).join(', ')) + '\">';
                html += '<label>מספר מילים:</label>';
                html += '<input type=\"number\" class=\"section-words\" value=\"' + (section.word_count || 200) + '\">';
                html += '</div>';
            });
        }
        
        html += '<a href=\"#\" class=\"add-structure-item\" onclick=\"addStructureItem()\">+ הוסף פרק</a>';
        
        // Conclusion
        html += '<div class=\"structure-item\">';
        html += '<label>Summary:</label>';
        html += '<textarea id=\"edit-conclusion\" rows=\"3\">' + escapeHtml(articleData.structure.conclusion || '') + '</textarea>';
        html += '</div>';
        
        html += '</div>';
        
        $('#structure-editor').html(html);
    }
    
    function saveStructureEdit() {
        // Collect edited structure data
        const editedStructure = {
            title: $('#edit-title').val(),
            introduction: $('#edit-introduction').val(),
            sections: [],
            conclusion: $('#edit-conclusion').val(),
            multimedia_suggestions: articleData.structure.multimedia_suggestions || []
        };
        
        $('.structure-item[data-section]').each(function() {
            const $item = $(this);
            const section = {
                heading: $item.find('.section-heading').val(),
                subheadings: $item.find('.section-subheadings').val().split(',').map(s => s.trim()).filter(s => s),
                topics: $item.find('.section-topics').val().split(',').map(s => s.trim()).filter(s => s),
                word_count: parseInt($item.find('.section-words').val()) || 200
            };
            editedStructure.sections.push(section);
        });
        
        articleData.structure = editedStructure;
        displayStructure(editedStructure);
        closeStructureEditor();
        showNotice('מבנה המאמר עודכן', 'success');
    }
    
    function closeStructureEditor() {
        $('#structure-editor-modal').hide();
    }
    
    function regenerateStructure() {
        showLoading('#structure-preview', ai_seo_article_generator_ajax.i18n.generating_structure);
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_generate_structure',
                nonce: ai_seo_article_generator_ajax.nonce,
                main_keyword: articleData.main_keyword,
                sub_keywords: articleData.sub_keywords,
                target_words: articleData.target_words
            },
            success: function(response) {
                if (response.success) {
                    articleData.structure = response.structure;
                    displayStructure(response.structure);
                    showNotice('מבנה חדש נוצר', 'success');
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('שגיאה ביצירת מבנה חדש', 'error');
            }
        });
    }
    
    function saveDraft() {
        // Sync current preview content with articleData before saving
        syncContentFromPreview();
        
        const draftData = {
            title: articleData.structure ? articleData.structure.title : '',
            main_keyword: articleData.main_keyword,
            sub_keywords: articleData.sub_keywords,
            target_words: articleData.target_words,
            structure_data: JSON.stringify(articleData.structure),
            content: articleData.content, // This now includes current links
            outbound_links: JSON.stringify(articleData.outbound_links),
            status: 'draft'
        };
        
        if (articleData.draft_id) {
            draftData.draft_id = articleData.draft_id;
        }
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_save_draft',
                nonce: ai_seo_article_generator_ajax.nonce,
                ...draftData
            },
            success: function(response) {
                if (response.success) {
                    articleData.draft_id = response.draft_id;
                    showNotice(getUIText('draft_saved_success'), 'success');
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('שגיאה בשמירת הטיוטה', 'error');
            }
        });
    }
    
    function copyArticleHTML() {
        
        if (!articleData.content) {
            showNotice('אין תוכן מאמר להעתקה. יש ליצור תוכן תחילה.', 'error');
            return;
        }
        
        // Create fallback structure if missing
        if (!articleData.structure) {
            articleData.structure = {
                title: articleData.main_keyword || 'מאמר ללא כותרת',
                introduction: 'Introduction למאמר',
                conclusion: 'Summary המאמר',
                sections: [],
                multimedia_suggestions: []
            };
        }
        
        // Create SEO-optimized HTML structure
        const title = articleData.structure.title || articleData.main_keyword;
        let content = articleData.content;
        
        // Add table of contents to content if it doesn't already have one
        if (content.indexOf('ai-seo-article-generator-toc') === -1) {
            content = addTableOfContentsToHTML(content);
        }
        
        // Generate proper HTML structure with SEO best practices
        let seoOptimizedHTML = generateSEOOptimizedHTML(title, content, articleData);
        
        // Copy to clipboard
        if (navigator.clipboard) {
            navigator.clipboard.writeText(seoOptimizedHTML).then(function() {
                showNotice('המאמר הועתק ללוח! כולל מבנה HTML מאופטימז ל-SEO', 'success');
                $('#copy-article').text('✅ הועתק!').addClass('copied');
                setTimeout(function() {
                    $('#copy-article').text('📋 העתק מאמר (HTML)').removeClass('copied');
                }, 3000);
            }).catch(function() {
                fallbackCopyToClipboard(seoOptimizedHTML);
            });
        } else {
            fallbackCopyToClipboard(seoOptimizedHTML);
        }
    }
    
    function generateSEOOptimizedHTML(title, content, data) {
        // Clean text to prevent escaping issues
        const mainKeyword = cleanHebrewText(data.main_keyword);
        const subKeywords = cleanHebrewText(data.sub_keywords);
        const structure = data.structure;
        
        // Meta description from introduction
        const metaDescription = structure.introduction ? 
            cleanHebrewText(structure.introduction.substring(0, 150)) + '...' : 
            `מדריך מקיף על ${mainKeyword}. כל מה שצריך לדעת בנושא.`;
            
        // Clean title as well
        const cleanTitle = cleanHebrewText(title);
        
        // Create structured HTML with proper SEO elements
        let html = `<!-- SEO Optimized Article Generated by Postinor -->
<!-- Main Keyword: ${mainKeyword} -->
<!-- Sub Keywords: ${subKeywords} -->

<!-- SEO Meta Tags (for WordPress post) -->
<!--
<meta name="description" content="${metaDescription.replace(/"/g, '&quot;')}">
<meta name="keywords" content="${mainKeyword}, ${subKeywords}">
<meta property="og:title" content="${cleanTitle.replace(/"/g, '&quot;')}">
<meta property="og:description" content="${metaDescription.replace(/"/g, '&quot;')}">
<meta property="og:type" content="article">
-->

<!-- Article Content -->
<article itemscope itemtype="https://schema.org/Article">
    <!-- Article Header -->
    <header>
        <h1 itemprop="headline">${cleanTitle}</h1>
        <meta itemprop="keywords" content="${mainKeyword}, ${subKeywords}">
        <meta itemprop="description" content="${metaDescription.replace(/"/g, '&quot;')}">
    </header>

    <!-- Article Body -->
    <div itemprop="articleBody">
${formatContentWithSEOStructure(content, structure)}
    </div>

    <!-- Article Footer -->
    <footer>
        <div class="article-meta">
            <span class="main-topic">נושא ראשי: <strong>${mainKeyword}</strong></span>
            ${structure.multimedia_suggestions && structure.multimedia_suggestions.length > 0 ? 
                `<div class="suggested-media">
                    <h3>הצעות תוכן נוסף:</h3>
                    <ul>
${structure.multimedia_suggestions.map(suggestion => `                        <li>${suggestion}</li>`).join('\n')}
                    </ul>
                </div>` : ''}
        </div>
    </footer>
</article>

<!-- Schema.org JSON-LD for better SEO -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "${cleanTitle.replace(/"/g, '\\"')}",
    "description": "${metaDescription.replace(/"/g, '\\"')}",
    "keywords": "${mainKeyword}, ${subKeywords}",
    "author": {
        "@type": "Organization",
        "name": "Your Website Name"
    },
    "publisher": {
        "@type": "Organization", 
        "name": "Your Website Name"
    },
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "YOUR_ARTICLE_URL"
    }
}
</script>`;

        return html;
    }
    
    function addTableOfContentsToHTML(content) {
        // Extract headings (H2-H6) from content
        const headingRegex = /<h([2-6])([^>]*)>(.*?)<\/h[2-6]>/gi;
        const headings = [];
        let match;
        
        while ((match = headingRegex.exec(content)) !== null) {
            const level = parseInt(match[1]);
            const text = match[3].replace(/<[^>]*>/g, ''); // Strip HTML tags
            const id = text.replace(/[^\u0590-\u05FFa-zA-Z0-9\s]/g, '')
                          .replace(/\s+/g, '-')
                          .toLowerCase();
            
            headings.push({
                level: level,
                text: text,
                id: id
            });
        }
        
        if (headings.length < 2) {
            return content; // Don't add TOC if fewer than 2 headings
        }
        
        // Add IDs to headings in content
        let contentWithIds = content.replace(headingRegex, function(match, level, attrs, text) {
            const id = text.replace(/<[^>]*>/g, '')
                          .replace(/[^\u0590-\u05FFa-zA-Z0-9\s]/g, '')
                          .replace(/\s+/g, '-')
                          .toLowerCase();
            
            // Only add ID if not already present
            if (!attrs.includes('id=')) {
                attrs += ' id="' + id + '"';
            }
            
            return '<h' + level + attrs + '>' + text + '</h' + level + '>';
        });
        
        // Generate TOC HTML
        let tocHTML = '<div class="ai-seo-article-generator-toc ai-seo-article-generator-toc-numbered">';
        tocHTML += '<div class="toc-header">';
        tocHTML += '<h3 class="toc-title">' + getUIText('toc_title') + '</h3>';
        tocHTML += '<button class="toc-toggle" onclick="toggleTOC(this)">' + getUIText('toggle_show_hide') + '</button>';
        tocHTML += '</div>';
        tocHTML += '<div class="toc-content">';
        tocHTML += '<ol class="toc-list">';
        
        let counter = 1;
        for (const heading of headings) {
            if (heading.level === 2) {
                tocHTML += '<li class="toc-level-2">';
                tocHTML += '<a href="#' + heading.id + '" class="toc-link">';
                tocHTML += '<span class="toc-number">' + counter + '.</span> ' + heading.text;
                tocHTML += '</a></li>';
                counter++;
            } else {
                tocHTML += '<li class="toc-level-' + heading.level + '">';
                tocHTML += '<a href="#' + heading.id + '" class="toc-link">' + heading.text + '</a>';
                tocHTML += '</li>';
            }
        }
        
        tocHTML += '</ol></div></div>';
        
        // Insert TOC after first paragraph or before first H2
        const insertAfterP = contentWithIds.indexOf('</p>');
        const insertBeforeH2 = contentWithIds.indexOf('<h2');
        
        let insertPos = -1;
        if (insertAfterP !== -1 && (insertBeforeH2 === -1 || insertAfterP < insertBeforeH2)) {
            insertPos = insertAfterP + 4; // After </p>
        } else if (insertBeforeH2 !== -1) {
            insertPos = insertBeforeH2;
        }
        
        if (insertPos !== -1) {
            const before = contentWithIds.substring(0, insertPos);
            const after = contentWithIds.substring(insertPos);
            return before + '\n\n' + tocHTML + '\n\n' + after;
        }
        
        // Fallback: insert at beginning
        return tocHTML + '\n\n' + contentWithIds;
    }
    
    function formatContentWithSEOStructure(content, structure) {
        // Convert content to properly structured HTML with semantic tags
        let formattedContent = content;
        
        // Ensure H1 is only used for main title (already handled above)
        // Convert any existing H1 in content to H2
        formattedContent = formattedContent.replace(/<h1([^>]*)>/gi, '<h2$1>');
        formattedContent = formattedContent.replace(/<\/h1>/gi, '</h2>');
        
        // Add introduction section if available
        if (structure.introduction) {
            formattedContent = `        <section class="article-introduction">
            <h2>Introduction</h2>
            <p>${structure.introduction}</p>
        </section>

${formattedContent}`;
        }
        
        // Add conclusion section if available  
        if (structure.conclusion) {
            formattedContent += `
        <section class="article-conclusion">
            <h2>Summary</h2>
            <p>${structure.conclusion}</p>
        </section>`;
        }
        
        // Wrap sections with semantic HTML5 tags
        formattedContent = formattedContent.replace(/<h2([^>]*)>(.*?)<\/h2>/gi, function(match, attrs, title) {
            return `        <section>
            <h2${attrs}>${title}</h2>`;
        });
        
        // Close sections (simple approach - assumes content is well-structured)
        formattedContent = formattedContent.replace(/(<section>\s*<h2[^>]*>.*?<\/h2>[\s\S]*?)(?=<section>|$)/gi, '$1\n        </section>\n');
        
        // Ensure proper indentation
        formattedContent = formattedContent.replace(/^/gm, '        ');
        
        return formattedContent;
    }
    
    function fallbackCopyToClipboard(text) {
        // Fallback method for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '-9999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showNotice('המאמר הועתק ללוח! כולל מבנה HTML מאופטימז ל-SEO', 'success');
                $('#copy-article').text('✅ הועתק!').addClass('copied');
                setTimeout(function() {
                    $('#copy-article').text('📋 העתק מאמר (HTML)').removeClass('copied');
                }, 3000);
            } else {
                throw new Error('Copy command failed');
            }
        } catch (err) {
            showNotice('לא ניתן להעתיק את המאמר. נסה להעתיק ידנית מהתצוגה המקדימה.', 'error');
        }
        
        document.body.removeChild(textArea);
    }
    
    function publishArticle() {
        // Get current content from preview (includes any added links but clean of enhancement controls)
        const currentContent = $('#article-preview').html();
        
        if (!currentContent || currentContent.trim() === '') {
            showNotice('אין תוכן לפרסום', 'error');
            return;
        }
        
        // Clean content for publishing (remove enhancement controls but keep links)
        const cleanContentForPublishing = getCleanContentForAnalysis(currentContent);
        
        const title = articleData.structure ? articleData.structure.title : articleData.main_keyword;
        
        // Show loading state
        $('#publish-article').prop('disabled', true).text(getUIText('creating_post'));
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_publish_article',
                nonce: ai_seo_article_generator_ajax.nonce,
                title: title,
                content: cleanContentForPublishing, // Use clean content with links but no enhancement controls
                main_keyword: articleData.main_keyword,
                sub_keywords: articleData.sub_keywords,
                structure_data: JSON.stringify(articleData.structure),
                draft_id: articleData.draft_id,
                article_language: $('#article_language').val() || 'auto',
                outbound_links: JSON.stringify(articleData.outbound_links) // Include links metadata
            },
            success: function(response) {
                if (response.success) {
                    showNotice(getUIText('article_created_editing'), 'success');
                    // Redirect to WordPress post editor
                    setTimeout(() => {
                        window.open(response.edit_url, '_blank');
                    }, 1500);
                } else {
                    showNotice(response.message || getUIText('error_publishing'), 'error');
                    $('#publish-article').prop('disabled', false).text(getUIText('publish_article'));
                }
            },
            error: function() {
                showNotice(getUIText('error_publishing'), 'error');
                $('#publish-article').prop('disabled', false).text(getUIText('publish_article'));
            }
        });
    }
    
    function retryFailedJob(e) {
        e.preventDefault();
        
        const jobId = $(this).data('job-id');
        const $button = $(this);
        const $row = $button.closest('tr');
        
        if (!confirm('Are you sure you want to retry this failed job?')) {
            return;
        }
        
        $button.prop('disabled', true).text('Retrying...');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_retry_failed_job',
                nonce: ai_seo_article_generator_ajax.nonce,
                job_id: jobId
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.message, 'success');
                    
                    // Update row to show queued status
                    $row.find('.job-status').removeClass('job-status-failed').addClass('job-status-queued').text('Queued');
                    $row.find('.retry-job, .delete-failed-job').remove();
                    $row.find('td:last-child').append('<span class="queued-indicator">⏳ In Queue</span>');
                    
                } else {
                    showNotice(response.message, 'error');
                    $button.prop('disabled', false).text('Retry');
                }
            },
            error: function() {
                showNotice('Error retrying job', 'error');
                $button.prop('disabled', false).text('Retry');
            }
        });
    }
    
    function manualTriggerBackground() {
        const $button = $('#manual-trigger-background');
        const $status = $('#manual-trigger-status');
        
        $button.prop('disabled', true).text('Processing...');
        $status.text('Triggering background processing...').css('color', 'orange');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_manual_trigger_background',
                nonce: ai_seo_article_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $status.text('✅ ' + response.message).css('color', 'green');
                    // Auto-refresh page after 3 seconds to show updated job status
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    $status.text('❌ Error: ' + response.message).css('color', 'red');
                }
                $button.prop('disabled', false).text('⚡ Manual Trigger Processing');
            },
            error: function() {
                $status.text('❌ Network error occurred').css('color', 'red');
                $button.prop('disabled', false).text('⚡ Manual Trigger Processing');
            }
        });
    }
    
    function debugStructures() {
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_debug_structures',
                nonce: ai_seo_article_generator_ajax.nonce
            },
            success: function(response) {
                
                if (response.success) {
                    let message = `נמצאו ${response.structures_count} מבנים במסד הנתונים:\n\n`;
                    
                    response.debug_info.forEach(function(info) {
                        message += `ID: ${info.id}, שם: ${info.name}\n`;
                        message += `אורך נתונים: ${info.data_length}, JSON תקין: ${info.is_json_valid ? 'כן' : 'לא'}\n`;
                        if (info.json_error) {
                            message += `שגיאת JSON: ${info.json_error}\n`;
                        }
                        message += `תצוגה מקדימה: ${info.data_preview}\n\n`;
                    });
                    
                    alert(message);
                    showNotice('בדיקת מסד הנתונים הושלמה - ראה Console ו-Log', 'info');
                } else {
                    showNotice('שגיאה בבדיקת מסד הנתונים', 'error');
                }
            },
            error: function() {
                showNotice('שגיאה בבדיקת מסד הנתונים', 'error');
            }
        });
    }
    
    function deleteDraft(e) {
        e.preventDefault();
        
        if (!confirm('האם אתה בטוח שברצונך למחוק את הטיוטה?')) {
            return;
        }
        
        const draftId = $(this).data('id');
        const $row = $(this).closest('tr');
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_delete_draft',
                nonce: ai_seo_article_generator_ajax.nonce,
                draft_id: draftId
            },
            success: function(response) {
                if (response.success) {
                    $row.fadeOut();
                    showNotice('הטיוטה נמחקה', 'success');
                } else {
                    showNotice(response.message, 'error');
                }
            },
            error: function() {
                showNotice('שגיאה במחיקת הטיוטה', 'error');
            }
        });
    }
    
    function syncContentFromPreview() {
        // Update articleData.content with current preview content (includes links but without enhancement controls)
        const currentContent = $('#article-preview').html();
        if (currentContent && currentContent.trim() !== '') {
            // Store clean content without enhancement controls for data persistence
            articleData.content = getCleanContentForAnalysis(currentContent);
        }
    }
    
    function loadDraft(draftId) {
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_load_draft',
                nonce: ai_seo_article_generator_ajax.nonce,
                draft_id: draftId
            },
            success: function(response) {
                if (response.success) {
                    const draft = response.draft;
                    // Parse structure data safely
                    let parsedStructure = null;
                    if (draft.structure_data) {
                        try {
                            parsedStructure = JSON.parse(draft.structure_data);
                        } catch (e) {
                            // Create fallback structure
                            parsedStructure = {
                                title: draft.main_keyword || 'מאמר שמור',
                                introduction: 'Introduction למאמר',
                                conclusion: 'Summary המאמר',
                                sections: [],
                                multimedia_suggestions: []
                            };
                        }
                    }
                    
                    articleData = {
                        draft_id: draft.id,
                        main_keyword: draft.main_keyword,
                        sub_keywords: draft.sub_keywords,
                        target_words: draft.target_words,
                        structure: parsedStructure,
                        content: draft.content,
                        outbound_links: draft.outbound_links || {}
                    };
                    
                    
                    // Populate form fields
                    $('#main_keyword').val(draft.main_keyword);
                    $('#sub_keywords').val(draft.sub_keywords);
                    $('#target_words').val(draft.target_words);
                    
                    // Show appropriate step based on draft status
                    if (draft.content) {
                        showStep('preview');
                        displayArticlePreview(draft.content);
                    } else if (draft.structure_data) {
                        showStep('structure');
                        displayStructure(articleData.structure);
                    }
                }
            }
        });
    }
    
    function loadSavedStructure(structureId) {
        
        $.ajax({
            url: ai_seo_article_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ai_seo_article_generator_load_structure',
                nonce: ai_seo_article_generator_ajax.nonce,
                structure_id: structureId
            },
            success: function(response) {
                
                if (response.success && response.structure) {
                    const structure = response.structure;
                    
                    // Validate and fix the structure data
                    const validatedStructure = validateStructure(structure.structure_data);
                    
                    if (validatedStructure) {
                        articleData = {
                            main_keyword: structure.main_keyword,
                            sub_keywords: structure.sub_keywords,
                            target_words: structure.target_words,
                            structure: validatedStructure,
                            content: '',
                            draft_id: null
                        };
                        
                        // Populate form fields
                        $('#main_keyword').val(structure.main_keyword);
                        $('#sub_keywords').val(structure.sub_keywords);
                        $('#target_words').val(structure.target_words);
                        
                        // Show structure step
                        showStep('structure');
                        displayStructure(articleData.structure);
                        
                        showNotice(`נטען מבנה שמור: ${structure.name}`, 'success');
                    } else {
                        showNotice('המבנה השמור פגום ולא ניתן להשתמש בו', 'error');
                    }
                } else {
                    showNotice(response.message || 'שגיאה בטעינת המבנה השמור', 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotice('שגיאה בטעינת המבנה השמור', 'error');
            }
        });
    }
    
    function showStep(step) {
        console.log('🔄 showStep called with step:', step);
        console.log('📍 Current elements with active class:', $('.ai-seo-article-generator-step.active').length);
        
        $('.ai-seo-article-generator-step').removeClass('active');
        $('#step-' + step).addClass('active');
        currentStep = step;
        
        console.log('✅ showStep completed. New active step:', $('#step-' + step).hasClass('active'));
        console.log('📍 Elements with active class after change:', $('.ai-seo-article-generator-step.active').length);
    }
    
    function showLoading(selector, message, estimatedTime = null) {
        let html = '<div class=\"ai-seo-article-generator-loading-container\">' +
                   '<div class=\"ai-seo-article-generator-spinner\"></div>' +
                   '<p>' + message + '</p>';
        
        if (estimatedTime) {
            // Extract the number from estimatedTime (e.g., "5 minutes" -> "5")
            const timeMatch = estimatedTime.match(/\d+/);
            const timeValue = timeMatch ? parseInt(timeMatch[0]) : estimatedTime;
            html += '<p class="estimated-time">' + getUIText('estimated_time').replace('%d', timeValue) + '</p>';
        }
        
        html += '</div>';
        
        $(selector).html(html);
    }
    
    function showNotice(message, type) {
        const noticeClass = 'notice notice-' + type;
        const $notice = $('<div class=\"' + noticeClass + ' is-dismissible\"><p>' + message + '</p></div>');
        
        $('.ai-seo-article-generator-wrap').prepend($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function cleanHebrewText(text) {
        if (!text) return '';
        
        // Remove multiple backslashes before Hebrew apostrophes
        return text
            .replace(/\\+'/g, "'")  // Replace multiple backslashes with single apostrophe
            .replace(/\\+"/g, '"')  // Replace multiple backslashes with single quote
            .replace(/\\\\/g, "\\") // Replace double backslashes with single
            .trim();
    }
    
    function validateStructure(structure) {
        
        // Check if structure exists and is an object
        if (!structure || typeof structure !== 'object') {
            return createDefaultStructure();
        }
        
        // Create a valid structure with defaults for missing properties
        const validStructure = {
            title: structure.title || 'Article Structure',
            introduction: structure.introduction || 'Introduction למאמר',
            sections: Array.isArray(structure.sections) ? structure.sections : [],
            conclusion: structure.conclusion || 'Summary המאמר',
            multimedia_suggestions: Array.isArray(structure.multimedia_suggestions) ? structure.multimedia_suggestions : []
        };
        
        // Validate sections
        if (validStructure.sections.length === 0) {
            validStructure.sections = [{
                heading: 'Chapter ראשי',
                subheadings: ['תת-נושא 1', 'תת-נושא 2'],
                topics: ['נושא 1', 'נושא 2'],
                word_count: 300
            }];
        }
        
        return validStructure;
    }
    
    function createDefaultStructure() {
        return {
            title: 'Article Structure לא זמין',
            introduction: 'לא ניתן לטעון את המבנה השמור. נא לנסות שוב או ליצור מבנה חדש.',
            sections: [{
                heading: 'Chapter ראשי',
                subheadings: ['יש ליצור מבנה חדש'],
                topics: ['המבנה השמור פגום'],
                word_count: 300
            }],
            conclusion: 'נא ליצור מבנה חדש',
            multimedia_suggestions: []
        };
    }
    
    // Global functions for structure editor
    window.addStructureItem = function() {
        const index = $('.structure-item[data-section]').length;
        const html = '<div class=\"structure-item\" data-section=\"' + index + '\">' +
                    '<span class=\"remove-item\" onclick=\"removeStructureItem(this)\">×</span>' +
                    '<label>כותרת פרק:</label>' +
                    '<input type=\"text\" class=\"section-heading\" value=\"\">' +
                    '<label>תת-כותרות (הפרד בפסיקים):</label>' +
                    '<input type=\"text\" class=\"section-subheadings\" value=\"\">' +
                    '<label>נושאים (הפרד בפסיקים):</label>' +
                    '<input type=\"text\" class=\"section-topics\" value=\"\">' +
                    '<label>מספר מילים:</label>' +
                    '<input type=\"number\" class=\"section-words\" value=\"200\">' +
                    '</div>';
        
        $('.add-structure-item').before(html);
    };
    
    window.removeStructureItem = function(element) {
        $(element).closest('.structure-item').remove();
        
        // Reindex sections
        $('.structure-item[data-section]').each(function(index) {
            $(this).attr('data-section', index);
        });
    };
    
    // Table of Contents Functions
    window.toggleTOC = function(button) {
        const toc = button.closest('.ai-seo-article-generator-toc');
        toc.classList.toggle('collapsed');
        
        const isCollapsed = toc.classList.contains('collapsed');
        button.textContent = isCollapsed ? 'הצג' : 'הסתר';
    };
    
    window.smoothScrollTo = function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Add highlight effect
            element.style.backgroundColor = '#fff3cd';
            element.style.transition = 'background-color 0.3s ease';
            
            setTimeout(() => {
                element.style.backgroundColor = '';
            }, 2000);
        }
        return false;
    };
    
    // Structure Management Functions
    function saveStructure() {
        
        if (!articleData.structure) {
            showNotice('אין מבנה לשמירה', 'error');
            return;
        }
        
        
        // Create save structure modal
        const modalHtml = `
            <div id="save-structure-modal" class="ai-seo-article-generator-modal">
                <div class="ai-seo-article-generator-modal-content">
                    <span class="ai-seo-article-generator-close">&times;</span>
                    <h2>${getUIText('save_structure')}</h2>
                    <form id="save-structure-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">${getUIText('structure_name')}</th>
                                <td>
                                    <input type="text" id="structure-name" class="regular-text" required>
                                    <p class="description">${getUIText('structure_name_desc')}</p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">${getUIText('structure_description')}</th>
                                <td>
                                    <textarea id="structure-description" rows="3" class="large-text"></textarea>
                                    <p class="description">${getUIText('structure_description_desc')}</p>
                                </td>
                            </tr>
                        </table>
                        <div class="ai-seo-article-generator-modal-actions">
                            <button type="submit" class="button button-primary">${getUIText('save')}</button>
                            <button type="button" class="button button-secondary ai-seo-article-generator-close">${getUIText('cancel')}</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        $('body').append(modalHtml);
        $('#save-structure-modal').show();
        
        // Pre-fill with current data
        $('#structure-name').val(articleData.structure.title || '');
        
        // Handle form submission with debugging
        $('#save-structure-form').on('submit', function(e) {
            e.preventDefault();
            
            const structureName = $('#structure-name').val();
            const structureDescription = $('#structure-description').val();
            
            
            if (!structureName.trim()) {
                showNotice('נא להזין שם למבנה', 'error');
                return;
            }
            
            if (!articleData.structure) {
                showNotice('אין מבנה לשמירה', 'error');
                return;
            }
            
            const structureDataString = JSON.stringify(articleData.structure);
            
            $.ajax({
                url: ai_seo_article_generator_ajax.ajax_url,
                type: 'POST',
                contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                data: {
                    action: 'ai_seo_article_generator_save_structure',
                    nonce: ai_seo_article_generator_ajax.nonce,
                    name: structureName,
                    description: structureDescription,
                    main_keyword: articleData.main_keyword,
                    sub_keywords: articleData.sub_keywords,
                    target_words: articleData.target_words,
                    structure_data: structureDataString
                },
                beforeSend: function() {
                    showNotice(getUIText('saving_structure'), 'info');
                },
                success: function(response) {
                    
                    if (response.success) {
                        showNotice(getUIText('structure_saved_success'), 'success');
                        $('#save-structure-modal').remove();
                    } else {
                        showNotice(response.message || getUIText('structure_save_error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showNotice(getUIText('structure_save_error') + ': ' + error, 'error');
                }
            });
        });
        
        // Handle modal close specifically for save structure modal
        $(document).on('click', '#save-structure-modal .ai-seo-article-generator-close', function() {
            $('#save-structure-modal').remove();
        });
    }
    
    // === LINKS MANAGER FUNCTIONS ===
    
    function handleTextSelection() {
        const selection = window.getSelection();
        const selectedTextContent = selection.toString().trim();
        
        // Clear previous selection tracking
        selectedText = '';
        selectedRange = null;
        
        if (selectedTextContent.length > 0 && selectedTextContent.length <= 100) {
            // Valid text selection within article preview
            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;
            
            // Check if selection is within article preview
            if ($(container).closest('#article-preview').length > 0 || 
                $(container.parentElement).closest('#article-preview').length > 0) {
                
                selectedText = selectedTextContent;
                selectedRange = range.cloneRange();
                
                // Update manage links button state
                $('#manage-links').addClass('has-selection').text('🔗 קישורים (' + selectedText.substring(0, 20) + '...)');
            }
        } else {
            // No selection or invalid selection
            $('#manage-links').removeClass('has-selection').text('🔗 קישורים');
        }
    }
    
    function openLinksManager() {
        // Show the modal
        $('#links-manager-modal').show();
        
        // Update existing links list
        updateExistingLinksList();
        
        // Handle selected text
        if (selectedText && selectedRange) {
            $('#selected-text-display').text(selectedText);
            $('#add-link-section').show();
            $('#add-link-btn').show();
            
            // Check if this text already has a link
            const hasLink = checkIfTextHasLink(selectedText);
            if (hasLink) {
                $('#remove-link-btn').show();
                const existingUrl = getExistingLinkUrl(selectedText);
                $('#link-url').val(existingUrl);
            } else {
                $('#remove-link-btn').hide();
            }
        } else {
            $('#add-link-section').hide();
            $('#add-link-btn').hide();
            $('#remove-link-btn').hide();
        }
    }
    
    function closeLinksManager() {
        $('#links-manager-modal').hide();
        // Clear form
        $('#link-url').val('');
        $('#link-title').val('');
        $('#link-target').val('_blank');
    }
    
    function addLinkToSelection() {
        if (!selectedText || !selectedRange) {
            showNotice('נא לבחור טקסט במאמר תחילה', 'error');
            return;
        }
        
        const url = $('#link-url').val().trim();
        const title = $('#link-title').val().trim();
        const target = $('#link-target').val();
        
        if (!url) {
            showNotice('נא להזין כתובת אתר', 'error');
            return;
        }
        
        // Validate URL format
        if (!isValidUrl(url)) {
            showNotice('כתובת האתר אינה תקינה', 'error');
            return;
        }
        
        // Create link HTML
        let linkHtml = '<a href="' + escapeHtml(url) + '" target="' + target + '"';
        if (title) {
            linkHtml += ' title="' + escapeHtml(title) + '"';
        }
        linkHtml += '>' + selectedText + '</a>';
        
        // Replace selected text with link
        try {
            // Store link data
            const linkId = generateLinkId();
            articleData.outbound_links[linkId] = {
                text: selectedText,
                url: url,
                title: title,
                target: target,
                id: linkId
            };
            
            // Replace the text in the DOM
            selectedRange.deleteContents();
            const linkElement = $(linkHtml)[0];
            linkElement.setAttribute('data-link-id', linkId);
            selectedRange.insertNode(linkElement);
            
            // Clear selection
            window.getSelection().removeAllRanges();
            selectedText = '';
            selectedRange = null;
            
            // Update UI
            showNotice('הקישור נוסף בהצלחה', 'success');
            updateExistingLinksList();
            $('#manage-links').removeClass('has-selection').text('🔗 קישורים');
            closeLinksManager();
            
            // Update content analysis (use clean content)
            analyzeContent(getCleanContentForAnalysis($('#article-preview').html()));
            
            // Sync content to articleData
            syncContentFromPreview();
            
        } catch (error) {
            showNotice('שגיאה בהוספת הקישור', 'error');
            console.error('Link insertion error:', error);
        }
    }
    
    function removeLinkFromSelection() {
        if (!selectedText) {
            showNotice('נא לבחור טקסט מקושר במאמר', 'error');
            return;
        }
        
        // Find and remove the link
        const linkElements = $('#article-preview a').filter(function() {
            return $(this).text().includes(selectedText);
        });
        
        if (linkElements.length > 0) {
            linkElements.each(function() {
                const $link = $(this);
                const linkId = $link.attr('data-link-id');
                const linkText = $link.text();
                
                // Remove from data
                if (linkId && articleData.outbound_links[linkId]) {
                    delete articleData.outbound_links[linkId];
                }
                
                // Replace link with plain text
                $link.replaceWith(linkText);
            });
            
            showNotice('הקישור הוסר בהצלחה', 'success');
            updateExistingLinksList();
            closeLinksManager();
            
            // Update content analysis (use clean content)
            analyzeContent(getCleanContentForAnalysis($('#article-preview').html()));
            
            // Sync content to articleData
            syncContentFromPreview();
        } else {
            showNotice('לא נמצא קישור להסרה', 'error');
        }
    }
    
    function updateExistingLinksList() {
        const $linksList = $('#existing-links-list');
        const links = Object.values(articleData.outbound_links);
        
        if (links.length === 0) {
            $linksList.html('<p class="no-links">אין קישורים במאמר</p>');
            return;
        }
        
        let html = '<div class="links-grid">';
        links.forEach(function(link) {
            html += '<div class="link-item">';
            html += '<div class="link-text"><strong>' + escapeHtml(link.text) + '</strong></div>';
            html += '<div class="link-url"><a href="' + escapeHtml(link.url) + '" target="_blank">' + escapeHtml(link.url) + '</a></div>';
            if (link.title) {
                html += '<div class="link-title">כותרת: ' + escapeHtml(link.title) + '</div>';
            }
            html += '<div class="link-actions">';
            html += '<button class="button button-small remove-link" data-link-id="' + link.id + '">הסר</button>';
            html += '</div>';
            html += '</div>';
        });
        html += '</div>';
        
        $linksList.html(html);
        
        // Bind remove link events
        $('.remove-link').on('click', function() {
            const linkId = $(this).data('link-id');
            removeLinkById(linkId);
        });
    }
    
    function removeLinkById(linkId) {
        const link = articleData.outbound_links[linkId];
        if (!link) return;
        
        // Find and remove the link element
        const $linkElement = $('[data-link-id="' + linkId + '"]');
        if ($linkElement.length > 0) {
            const linkText = $linkElement.text();
            $linkElement.replaceWith(linkText);
            
            // Remove from data
            delete articleData.outbound_links[linkId];
            
            showNotice('הקישור הוסר בהצלחה', 'success');
            updateExistingLinksList();
            
            // Update content analysis (use clean content)
            analyzeContent(getCleanContentForAnalysis($('#article-preview').html()));
            
            // Sync content to articleData
            syncContentFromPreview();
        }
    }
    
    function checkIfTextHasLink(text) {
        return Object.values(articleData.outbound_links).some(link => link.text === text);
    }
    
    function getExistingLinkUrl(text) {
        const link = Object.values(articleData.outbound_links).find(link => link.text === text);
        return link ? link.url : '';
    }
    
    function generateLinkId() {
        return 'link_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    function searchInGoogle() {
        if (!selectedText || selectedText.trim() === '') {
            showNotice('נא לבחור טקסט לחיפוש', 'error');
            return;
        }
        
        // Create Google search URL with Hebrew-friendly encoding
        const searchQuery = encodeURIComponent(selectedText.trim());
        const googleSearchUrl = `https://www.google.com/search?q=${searchQuery}&hl=he`;
        
        // Open in new tab
        window.open(googleSearchUrl, '_blank');
        
        showNotice(`חיפוש בגוגל: "${selectedText}"`, 'info');
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
});