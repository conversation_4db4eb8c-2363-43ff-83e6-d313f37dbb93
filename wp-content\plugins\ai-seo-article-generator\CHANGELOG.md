# Changelog

All notable changes to AI SEO Article Generator will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.4] - 2025-01-25

### Added
- **Feedback System**: Complete user support system with contact form and WhatsApp integration
  - Contact form with categorized feedback types (Bug, Feature, Support, Review)
  - Direct WhatsApp integration with support number (972546330446)
  - Floating WhatsApp button on all plugin pages
  - Email <NAME_EMAIL>
  - Quick action buttons for common tasks
- **Hebrew Language Support**: Full Hebrew translations for feedback system
- **Responsive Design**: Mobile-friendly feedback interface

### Changed
- **AI Model Update**: Updated all references from Claude 3.5 Sonnet to Claude 4
- **UI Text Improvements**: Replaced hardcoded Hebrew progress messages with English
- **JavaScript Error Handling**: Fixed aiSeoFeedback object initialization issues

### Fixed
- JavaScript error in feedback system when localized data wasn't available
- Hebrew text display in progress messages during article generation
- WhatsApp button functionality across all plugin pages

### Technical
- Added class-ai-seo-article-generator-feedback.php for feedback functionality
- Added feedback.css and feedback.js for styling and interaction
- Extended Hebrew translation file with feedback system strings

## [1.0.3] - 2025-07-20

### Added
- Comprehensive documentation (README.md, DEVELOPER.md, WORDPRESS-COMPLIANCE.md)
- Proper .gitignore file for clean distribution
- phpcs:ignore comments for acceptable warnings
- Complete release notes and changelog

### Changed
- Reorganized file structure with /docs/ directory
- Improved debug logging to be conditional only
- Enhanced code comments and documentation
- Updated all version numbers consistently

### Fixed
- All WordPress Plugin Check errors resolved
- InterpolatedNotPrepared warnings fixed
- Translator comment placement corrected
- Escaping and sanitization issues resolved
- API key validation bug in generate_article_structure

### Security
- Enhanced input sanitization throughout
- Better output escaping implementation
- Improved nonce verification

## [1.0.2] - 2025-07-15

### Added
- OpenAI integration (GPT-4, GPT-4 Turbo, GPT-3.5 Turbo)
- Background processing for long articles (2000+ words)
- Saved structures library feature
- Email notifications for background jobs
- Retry mechanism for failed generation
- Readability analysis (Flesch score)
- Word count tracking
- Progress tracking for background jobs
- Table of contents with smooth scrolling
- Debug logging system

### Changed
- Improved error messages and user feedback
- Enhanced Hebrew/English content detection
- Better handling of API timeouts
- Optimized database queries

### Fixed
- API key validation issues
- MySQL strict mode compatibility
- Content truncation problems
- Timeout issues for large articles
- Table structure for TEXT columns

### Security
- Better nonce handling
- Improved data validation

## [1.0.1] - 2025-06-01

### Added
- English language support
- Multi-language content generation
- Automatic language detection
- Initial WordPress.org compliance improvements

### Changed
- Plugin renamed from "Postinor" to "AI SEO Article Generator"
- Improved timeout handling
- Better error reporting

### Fixed
- Basic timeout issues
- Initial security vulnerabilities

## [1.0.0] - 2025-05-01

### Added
- Initial release
- Claude API integration
- Hebrew content generation with RTL support
- SEO optimization features
- Article structure templates
- Draft management system
- Table of contents generation
- Keyword integration
- Meta description generation
- Admin dashboard interface

[1.0.3]: https://github.com/ytrofr/ai-seo-article-generator/compare/v1.0.2...v1.0.3
[1.0.2]: https://github.com/ytrofr/ai-seo-article-generator/compare/v1.0.1...v1.0.2
[1.0.1]: https://github.com/ytrofr/ai-seo-article-generator/compare/v1.0.0...v1.0.1
[1.0.0]: https://github.com/ytrofr/ai-seo-article-generator/releases/tag/v1.0.0