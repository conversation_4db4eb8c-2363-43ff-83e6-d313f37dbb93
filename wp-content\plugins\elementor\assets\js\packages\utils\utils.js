/*! For license information please see utils.js.LICENSE.txt */
!function(){"use strict";var e={"./packages/packages/libs/utils/src/debounce.ts":function(e,r,t){function debounce(e,r){let t=null;const cancel=()=>{t&&(clearTimeout(t),t=null)},run=(...n)=>{cancel(),t=setTimeout(()=>{e(...n),t=null},r)};return run.flush=(...r)=>{cancel(),e(...r)},run.cancel=cancel,run.pending=()=>!!t,run}t.r(r),t.d(r,{debounce:function(){return debounce}})},"./packages/packages/libs/utils/src/errors/create-error.ts":function(e,r,t){t.r(r),t.d(r,{createError:function(){return createError}});var n=t("./packages/packages/libs/utils/src/errors/elementor-error.ts");const createError=({code:e,message:r})=>class extends n.ElementorError{constructor({cause:t,context:n}={}){super(r,{cause:t,code:e,context:n})}}},"./packages/packages/libs/utils/src/errors/elementor-error.ts":function(e,r,t){t.r(r),t.d(r,{ElementorError:function(){return ElementorError}});class ElementorError extends Error{constructor(e,{code:r,context:t=null,cause:n=null}){super(e,{cause:n}),this.context=t,this.code=r}}},"./packages/packages/libs/utils/src/errors/ensure-error.ts":function(e,r,t){t.r(r),t.d(r,{ensureError:function(){return ensureError}});const ensureError=e=>{if(e instanceof Error)return e;let r,t=null;try{r=JSON.stringify(e)}catch(e){t=e,r="Unable to stringify the thrown value"}return new Error(`Unexpected non-error thrown: ${r}`,{cause:t})}},"./packages/packages/libs/utils/src/errors/index.ts":function(e,r,t){t.r(r),t.d(r,{ElementorError:function(){return n.ElementorError},createError:function(){return s.createError},ensureError:function(){return c.ensureError}});var n=t("./packages/packages/libs/utils/src/errors/elementor-error.ts"),s=t("./packages/packages/libs/utils/src/errors/create-error.ts"),c=t("./packages/packages/libs/utils/src/errors/ensure-error.ts")},"./packages/packages/libs/utils/src/hash.ts":function(e,r,t){function hash(e){return JSON.stringify(e,(e,r)=>function isPlainObject(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}(r)?Object.keys(r).sort().reduce((e,t)=>(e[t]=r[t],e),{}):r)}t.r(r),t.d(r,{hash:function(){return hash}})},"./packages/packages/libs/utils/src/use-debounce-state.ts":function(e,r,t){t.r(r),t.d(r,{useDebounceState:function(){return useDebounceState}});var n=t("react"),s=t("./packages/packages/libs/utils/src/debounce.ts");function useDebounceState(e={}){const{delay:r=300,initialValue:t=""}=e,[c,u]=(0,n.useState)(t),[o,a]=(0,n.useState)(t),i=(0,n.useRef)(null);(0,n.useEffect)(()=>()=>{i.current?.cancel?.()},[]);const l=(0,n.useCallback)(e=>{i.current?.cancel?.(),i.current=(0,s.debounce)(()=>{u(e)},r),i.current()},[r]);return{debouncedValue:c,inputValue:o,handleChange:e=>{a(e),l(e)},setInputValue:a}}},react:function(e){e.exports=window.React}},r={};function __webpack_require__(t){var n=r[t];if(void 0!==n)return n.exports;var s=r[t]={exports:{}};return e[t](s,s.exports,__webpack_require__),s.exports}__webpack_require__.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(r,{a:r}),r},__webpack_require__.d=function(e,r){for(var t in r)__webpack_require__.o(r,t)&&!__webpack_require__.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},__webpack_require__.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var t={};!function(){__webpack_require__.r(t),__webpack_require__.d(t,{ElementorError:function(){return e.ElementorError},createError:function(){return e.createError},debounce:function(){return n.debounce},ensureError:function(){return e.ensureError},hash:function(){return s.hash},useDebounceState:function(){return r.useDebounceState}});var e=__webpack_require__("./packages/packages/libs/utils/src/errors/index.ts"),r=__webpack_require__("./packages/packages/libs/utils/src/use-debounce-state.ts"),n=__webpack_require__("./packages/packages/libs/utils/src/debounce.ts"),s=__webpack_require__("./packages/packages/libs/utils/src/hash.ts")}(),(window.elementorV2=window.elementorV2||{}).utils=t}(),window.elementorV2.utils?.init?.();
//# sourceMappingURL=utils.js.map