<?php

declare (strict_types=1);
namespace ElementorDeps\DI\Definition\Exception;

use ElementorDeps\DI\Definition\Definition;
use ElementorDeps\Psr\Container\ContainerExceptionInterface;
/**
 * Invalid DI definitions.
 *
 * <AUTHOR> <<EMAIL>>
 */
class InvalidDefinition extends \Exception implements ContainerExceptionInterface
{
    public static function create(Definition $definition, string $message, \Exception $previous = null) : self
    {
        return new self(\sprintf('%s' . \PHP_EOL . 'Full definition:' . \PHP_EOL . '%s', $message, (string) $definition), 0, $previous);
    }
}
