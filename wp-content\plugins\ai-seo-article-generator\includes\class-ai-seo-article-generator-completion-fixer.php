<?php

if (!defined('ABSPATH')) {
    exit;
}

class AI_SEO_Article_Generator_Completion_Fixer {
    
    private $plugin;
    private $api;
    
    public function __construct() {
        $this->plugin = ai_seo_article_generator();
        $this->init_hooks();
    }
    
    private function init_hooks() {
        add_filter('ai_seo_article_generator_content_prompt', array($this, 'enforce_completion_requirements'), 25, 2);
        add_filter('ai_seo_article_generator_generated_content', array($this, 'ensure_content_completion'), 1, 2);
    }
    
    /**
     * Add strict completion requirements to prompts
     */
    public function enforce_completion_requirements($prompt, $data) {
        $target_words = isset($data['target_words']) ? $data['target_words'] : 1000;
        
        $completion_enforcement = "\n\n🔴 דרישות השלמת מאמר מחמירות:\n";
        $completion_enforcement .= "- המאמר חייב להכיל בדיוק {$target_words} מילים - לא פחות!\n";
        $completion_enforcement .= "- אם תגיע לגבול הטוקנים, המשך עם 'וכן הלאה...', אבל אל תעצור!\n";
        $completion_enforcement .= "- כתוב כל פרק במלואו - לא תקצר שום דבר!\n";
        $completion_enforcement .= "- אם נגמר לך מקום, הוסף: '[המשך הכתיבה נחתך - יש להשלים]'\n";
        $completion_enforcement .= "- עדיף מאמר ארוך מדי מאשר קצר מדי!\n";
        $completion_enforcement .= "- ספור מילים בזמן אמת ווודא שאתה מגיע ל-{$target_words} מילים!\n\n";
        $completion_enforcement .= "🎯 זכור: {$target_words} מילים זה חובה מוחלטת!";
        
        return $prompt . $completion_enforcement;
    }
    
    /**
     * Ensure content is properly completed
     */
    public function ensure_content_completion($content, $data) {
        $target_words = isset($data['target_words']) ? $data['target_words'] : 1000;
        $current_words = str_word_count(strip_tags($content));
        
        $this->debug_log("🔍 Completion Check: {$current_words}/{$target_words} words");
        
        // If content is less than 80% of target, try to complete it
        if ($current_words < ($target_words * 0.8)) {
            $this->debug_log("⚠️ Content appears incomplete ({$current_words} words, target: {$target_words})");
            
            $completed_content = $this->complete_truncated_content($content, $data);
            if ($completed_content && strlen($completed_content) > strlen($content)) {
                $this->debug_log("✅ Content completed successfully");
                return $completed_content;
            }
        }
        
        return $content;
    }
    
    /**
     * Complete truncated content by extending it
     */
    private function complete_truncated_content($content, $data) {
        $target_words = isset($data['target_words']) ? $data['target_words'] : 1000;
        $current_words = str_word_count(strip_tags($content));
        $words_needed = $target_words - $current_words;
        
        if ($words_needed < 100) {
            return $content; // Close enough
        }
        
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
        
        // Analyze what sections are missing or incomplete
        $completion_strategy = $this->analyze_content_gaps($content, $words_needed, $is_hebrew);
        
        // Generate completion content
        $completion_content = $this->generate_completion_content($completion_strategy, $main_keyword, $words_needed, $is_hebrew);
        
        if ($completion_content) {
            return $content . "\n\n" . $completion_content;
        }
        
        return $content;
    }
    
    /**
     * Analyze what content gaps exist
     */
    private function analyze_content_gaps($content, $words_needed, $is_hebrew) {
        $gaps = array();
        
        // Check for missing sections
        $h2_count = preg_match_all('/<h2[^>]*>/i', $content);
        $h3_count = preg_match_all('/<h3[^>]*>/i', $content);
        $list_count = preg_match_all('/<(ul|ol)[^>]*>/i', $content);
        
        if ($h2_count < 4) {
            $gaps[] = 'need_more_sections';
        }
        
        if ($h3_count < 6) {
            $gaps[] = 'need_more_subsections';
        }
        
        if ($list_count < 3) {
            $gaps[] = 'need_more_lists';
        }
        
        // Check for FAQ section
        if (!preg_match('/שאלות\s+נפוצות|שו״ב|FAQ/ui', $content)) {
            $gaps[] = 'need_faq';
        }
        
        // Check for conclusion
        if (!preg_match('/סיכום|לסיכום|במסקנה|conclusion/ui', $content)) {
            $gaps[] = 'need_conclusion';
        }
        
        // Check for examples section
        if (!preg_match('/דוגמאות?|למשל|לדוגמה|examples?/ui', $content)) {
            $gaps[] = 'need_examples';
        }
        
        return array(
            'gaps' => $gaps,
            'words_needed' => $words_needed,
            'priority' => $this->determine_completion_priority($gaps, $words_needed)
        );
    }
    
    /**
     * Determine which gaps to fill first
     */
    private function determine_completion_priority($gaps, $words_needed) {
        $priority = array();
        
        if ($words_needed > 500) {
            // Need substantial content
            if (in_array('need_faq', $gaps)) $priority[] = 'faq';
            if (in_array('need_examples', $gaps)) $priority[] = 'examples';
            if (in_array('need_more_sections', $gaps)) $priority[] = 'sections';
            if (in_array('need_conclusion', $gaps)) $priority[] = 'conclusion';
        } elseif ($words_needed > 200) {
            // Need moderate content
            if (in_array('need_conclusion', $gaps)) $priority[] = 'conclusion';
            if (in_array('need_examples', $gaps)) $priority[] = 'examples';
            if (in_array('need_more_subsections', $gaps)) $priority[] = 'subsections';
        } else {
            // Need small additions
            if (in_array('need_conclusion', $gaps)) $priority[] = 'conclusion';
            $priority[] = 'expand_existing';
        }
        
        return $priority;
    }
    
    /**
     * Generate completion content based on strategy
     */
    private function generate_completion_content($strategy, $main_keyword, $words_needed, $is_hebrew) {
        $completion_parts = array();
        
        foreach ($strategy['priority'] as $type) {
            switch ($type) {
                case 'faq':
                    $completion_parts[] = $this->generate_faq_section($main_keyword, $is_hebrew);
                    break;
                    
                case 'examples':
                    $completion_parts[] = $this->generate_examples_section($main_keyword, $is_hebrew);
                    break;
                    
                case 'sections':
                    $completion_parts[] = $this->generate_additional_section($main_keyword, $is_hebrew);
                    break;
                    
                case 'conclusion':
                    $completion_parts[] = $this->generate_conclusion_section($main_keyword, $is_hebrew);
                    break;
                    
                case 'subsections':
                    $completion_parts[] = $this->generate_subsection($main_keyword, $is_hebrew);
                    break;
                    
                case 'expand_existing':
                    $completion_parts[] = $this->generate_expansion_content($main_keyword, $is_hebrew);
                    break;
            }
            
            // Check if we have enough content
            $current_completion_words = str_word_count(strip_tags(implode(' ', $completion_parts)));
            if ($current_completion_words >= $words_needed) {
                break;
            }
        }
        
        return implode("\n\n", $completion_parts);
    }
    
    /**
     * Generate FAQ section
     */
    private function generate_faq_section($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $faq = "<h2>שאלות נפוצות על {$keyword}</h2>\n\n";
            $faq .= "<h3>שאלה: מה החשיבות של {$keyword}?</h3>\n";
            $faq .= "<p><strong>תשובה מהירה:</strong> {$keyword} חשוב מאוד בעולם המודרני מכיוון שהוא מספק פתרונות יעילים לאתגרים מורכבים ומאפשר שיפור משמעותי בתהליכי עבודה.</p>\n";
            $faq .= "<p>בפירוט רב יותר, {$keyword} מביא איתו יתרונות רבים כולל חיסכון בזמן ובמשאבים, שיפור דיוק והפחתת שגיאות אנושיות. זה הופך אותו לכלי חיוני בכל תחום שבוחר לאמץ אותו.</p>\n\n";
            
            $faq .= "<h3>שאלה: איך מתחילים עם {$keyword}?</h3>\n";
            $faq .= "<p><strong>תשובה מהירה:</strong> התחלה עם {$keyword} דורשת תכנון מוקדם, הגדרת מטרות ברורות ובחירת הכלים המתאימים לצרכים הספציפיים שלכם.</p>\n";
            $faq .= "<p>המלצתנו היא להתחיל בפרויקט קטן ומוגדר, ללמוד מהתוצאות ולהרחיב בהדרגה. חשוב גם להשקיע בהכשרת הצוות ולהבטיח שיש תמיכה טכנית מתאימה.</p>\n\n";
            
            $faq .= "<h3>שאלה: מה העלויות הכרוכות ב{$keyword}?</h3>\n";
            $faq .= "<p><strong>תשובה מהירה:</strong> עלויות {$keyword} משתנות בהתאם להיקף הפרויקט, מורכבות היישום ורמת ההתאמה הנדרשת, אך בדרך כלל ההשקעה מחזירה את עצמה תוך 6-18 חודשים.</p>\n";
            $faq .= "<p>חשוב לזכור שמעבר לעלות הרכישה הראשונית, יש לקחת בחשבון עלויות הטמעה, הכשרה ותחזוקה שוטפת. עם זאת, החיסכון ארוך הטווח והשיפור ביעילות בדרך כלל מצדיקים את ההשקעה.</p>";
        } else {
            $faq = "<h2>Frequently Asked Questions about {$keyword}</h2>\n\n";
            $faq .= "<h3>Question: Why is {$keyword} important?</h3>\n";
            $faq .= "<p><strong>Quick Answer:</strong> {$keyword} is crucial in today's world because it provides efficient solutions to complex challenges and enables significant improvements in work processes.</p>\n";
            $faq .= "<p>In more detail, {$keyword} brings numerous benefits including time and resource savings, improved accuracy, and reduced human errors. This makes it an essential tool in any field that chooses to adopt it.</p>\n\n";
            
            $faq .= "<h3>Question: How do you get started with {$keyword}?</h3>\n";
            $faq .= "<p><strong>Quick Answer:</strong> Getting started with {$keyword} requires advance planning, setting clear objectives, and choosing the right tools for your specific needs.</p>\n";
            $faq .= "<p>Our recommendation is to start with a small, defined project, learn from the results, and gradually expand. It's also important to invest in team training and ensure appropriate technical support is available.</p>";
        }
        
        return $faq;
    }
    
    /**
     * Generate examples section
     */
    private function generate_examples_section($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $examples = "<h2>דוגמאות מעשיות ל{$keyword}</h2>\n\n";
            $examples .= "<p><strong>דוגמה מעשית ראשונה:</strong> יישום {$keyword} בחברה בינונית הוביל לשיפור של 35% ביעילות התפעולית תוך שישה חודשים בלבד.</p>\n";
            $examples .= "<p>בדוגמה זו, החברה הטמיעה את הפתרון בשלבים, החל מהמחלקה הטכנית ולאחר מכן הרחיבה לכל הארגון. התוצאות כללו חיסכון משמעותי בעלויות ושיפור ברמת השירות ללקוחות.</p>\n\n";
            
            $examples .= "<ul>\n";
            $examples .= "<li>הפחתה של 40% בזמני עיבוד בקשות לקוחות</li>\n";
            $examples .= "<li>שיפור של 25% בדיוק הנתונים המעובדים</li>\n";
            $examples .= "<li>חיסכון של 50% בעלויות כוח אדם בתהליכים חוזרים</li>\n";
            $examples .= "<li>הגדלה של 30% בשביעות רצון הלקוחות</li>\n";
            $examples .= "<li>הפחתה של 60% בשגיאות אנושיות בתהליכי עיבוד נתונים</li>\n";
            $examples .= "</ul>\n\n";
            
            $examples .= "<p><strong>דוגמה שנייה:</strong> ארגון ללא מטרות רווח השתמש ב{$keyword} כדי לייעל את תהליכי התרומות וניהול המתנדבים, והגדיל את היעילות ב-50%.</p>";
        } else {
            $examples = "<h2>Practical Examples of {$keyword}</h2>\n\n";
            $examples .= "<p><strong>First Practical Example:</strong> Implementation of {$keyword} in a medium-sized company led to a 35% improvement in operational efficiency within just six months.</p>\n";
            $examples .= "<p>In this example, the company implemented the solution in stages, starting with the technical department and then expanding to the entire organization. Results included significant cost savings and improved customer service levels.</p>\n\n";
            
            $examples .= "<ul>\n";
            $examples .= "<li>40% reduction in customer request processing times</li>\n";
            $examples .= "<li>25% improvement in data processing accuracy</li>\n";
            $examples .= "<li>50% savings in labor costs for repetitive processes</li>\n";
            $examples .= "<li>30% increase in customer satisfaction</li>\n";
            $examples .= "<li>60% reduction in human errors in data processing</li>\n";
            $examples .= "</ul>";
        }
        
        return $examples;
    }
    
    /**
     * Generate additional section
     */
    private function generate_additional_section($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $section = "<h2>טיפים מתקדמים לעבודה עם {$keyword}</h2>\n\n";
            $section .= "<p><strong>טיפ חשוב:</strong> הצלחה עם {$keyword} תלויה בהכנה מוקדמת ובהבנה עמוקה של הצרכים הספציפיים של הארגון שלכם.</p>\n";
            $section .= "<p>חשוב לבצע ניתוח יסודי של התהליכים הקיימים לפני התחלת ההטמעה. זה כולל מיפוי נקודות הכאב, זיהוי הזדמנויות לשיפור והגדרת מדדי הצלחה ברורים.</p>\n\n";
            
            $section .= "<h3>שלבי ההטמעה המומלצים</h3>\n";
            $section .= "<ol>\n";
            $section .= "<li><strong>שלב התכנון:</strong> הגדרת מטרות ויעדים ברורים</li>\n";
            $section .= "<li><strong>שלב הפיילוט:</strong> יישום מוגבל בהיקף קטן</li>\n";
            $section .= "<li><strong>שלב הערכה:</strong> ניתוח תוצאות ולקחים</li>\n";
            $section .= "<li><strong>שלב הרחבה:</strong> יישום מדורג בכל הארגון</li>\n";
            $section .= "<li><strong>שלב אופטימיזציה:</strong> שיפור מתמיד ועדכונים</li>\n";
            $section .= "</ol>";
        } else {
            $section = "<h2>Advanced Tips for Working with {$keyword}</h2>\n\n";
            $section .= "<p><strong>Important Tip:</strong> Success with {$keyword} depends on advance preparation and deep understanding of your organization's specific needs.</p>\n";
            $section .= "<p>It's important to conduct thorough analysis of existing processes before starting implementation. This includes mapping pain points, identifying improvement opportunities, and defining clear success metrics.</p>\n\n";
            
            $section .= "<h3>Recommended Implementation Stages</h3>\n";
            $section .= "<ol>\n";
            $section .= "<li><strong>Planning Stage:</strong> Setting clear goals and objectives</li>\n";
            $section .= "<li><strong>Pilot Stage:</strong> Limited implementation in small scope</li>\n";
            $section .= "<li><strong>Evaluation Stage:</strong> Analyzing results and lessons learned</li>\n";
            $section .= "<li><strong>Expansion Stage:</strong> Gradual implementation across the organization</li>\n";
            $section .= "<li><strong>Optimization Stage:</strong> Continuous improvement and updates</li>\n";
            $section .= "</ol>";
        }
        
        return $section;
    }
    
    /**
     * Generate conclusion section
     */
    private function generate_conclusion_section($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $conclusion = "<h2>סיכום ומסקנות על {$keyword}</h2>\n\n";
            $conclusion .= "<p><strong>לסיכום:</strong> {$keyword} מייצג הזדמנות משמעותית לארגונים המעוניינים לשפר את היעילות והתחרותיות שלהם בעולם המודרני.</p>\n";
            $conclusion .= "<p>המפתח להצלחה טמון בגישה מתוכננת ומדורגת, תוך הקפדה על הכשרת הצוות והתאמת הפתרון לצרכים הספציפיים של הארגון. חשוב לזכור שהתוצאות הטובות ביותר מתקבלות כאשר יש מחויבות מלאה של ההנהלה ושיתוף פעולה של כל הרמות בארגון.</p>\n\n";
            $conclusion .= "<p>ההשקעה ב{$keyword} אינה רק השקעה טכנולוגית, אלא השקעה אסטרטגית שיכולה לשנות את פני הארגון לטווח הארוך. הארגונים שיבחרו לאמץ את הטכנולוגיה הזו עכשיו יהיו בעמדת יתרון משמעותית מול המתחרים שלהם בעתיד.</p>";
        } else {
            $conclusion = "<h2>Summary and Conclusions about {$keyword}</h2>\n\n";
            $conclusion .= "<p><strong>In summary:</strong> {$keyword} represents a significant opportunity for organizations looking to improve their efficiency and competitiveness in the modern world.</p>\n";
            $conclusion .= "<p>The key to success lies in a planned and gradual approach, while ensuring team training and adapting the solution to the organization's specific needs. It's important to remember that the best results are achieved when there is full management commitment and cooperation at all organizational levels.</p>\n\n";
            $conclusion .= "<p>Investment in {$keyword} is not just a technological investment, but a strategic investment that can transform the organization for the long term. Organizations that choose to adopt this technology now will have a significant advantage over their competitors in the future.</p>";
        }
        
        return $conclusion;
    }
    
    /**
     * Generate subsection content
     */
    private function generate_subsection($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $subsection = "<h3>היבטים טכניים חשובים ב{$keyword}</h3>\n";
            $subsection .= "<p>מבחינה טכנית, {$keyword} דורש תשתית מתאימה ותכנון קפדני. חשוב להבין את הדרישות הטכניות ולוודא שהמערכות הקיימות יכולות לתמוך ביישום החדש.</p>\n";
            $subsection .= "<p>בנוסף, יש להקפיד על אבטחת מידע ועמידה בתקנות הרלוונטיות. זה כולל הגנה על פרטיות הלקוחות, גיבוי נתונים ותכנון לשעת חירום.</p>";
        } else {
            $subsection = "<h3>Important Technical Aspects of {$keyword}</h3>\n";
            $subsection .= "<p>From a technical perspective, {$keyword} requires appropriate infrastructure and careful planning. It's important to understand the technical requirements and ensure that existing systems can support the new implementation.</p>\n";
            $subsection .= "<p>Additionally, emphasis must be placed on information security and compliance with relevant regulations. This includes protecting customer privacy, data backup, and emergency planning.</p>";
        }
        
        return $subsection;
    }
    
    /**
     * Generate expansion content
     */
    private function generate_expansion_content($keyword, $is_hebrew) {
        if ($is_hebrew) {
            $expansion = "<p>נוסף על כך, חשוב לציין כי {$keyword} מתפתח כל הזמן, ולכן יש להיערך לעדכונים ושיפורים מתמידים. הארגונים המצליחים הם אלה שמתאימים את עצמם למגמות החדשות ומשקיעים בלמידה מתמדת של הצוות.</p>\n";
            $expansion .= "<p>בסופו של דבר, {$keyword} הוא כלי רב עוצמה שיכול להביא לשינוי משמעותי בביצועי הארגון, בתנאי שמטמיעים אותו בצורה חכמה ומתוכננת.</p>";
        } else {
            $expansion = "<p>Additionally, it's important to note that {$keyword} is constantly evolving, so organizations must prepare for continuous updates and improvements. Successful organizations are those that adapt to new trends and invest in continuous team learning.</p>\n";
            $expansion .= "<p>Ultimately, {$keyword} is a powerful tool that can bring significant change to organizational performance, provided it's implemented intelligently and in a planned manner.</p>";
        }
        
        return $expansion;
    }
    
    /**
     * Initialize API connection
     */
    private function get_api_instance() {
        if (!$this->api) {
            require_once AI_SEO_ARTICLE_GENERATOR_PLUGIN_DIR . 'includes/class-ai-seo-article-generator-api.php';
            $this->api = new AI_SEO_Article_Generator_API();
        }
        return $this->api;
    }
    
    /**
     * Debug logging
     */
    private function debug_log($message) {
        if (get_option('ai_seo_article_generator_debug_logging', 0)) {
            if (function_exists('error_log')) {
                error_log('AI SEO Completion Fixer: ' . $message);
            }
        }
    }
}