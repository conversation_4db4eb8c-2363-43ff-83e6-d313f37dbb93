<?php
/**
 * Test script per verificare il salvataggio dell'immagine banner
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test di salvataggio manuale
 */
function wpb_test_manual_save() {
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    echo '<div class="wrap">';
    echo '<h1>Test Salvataggio Banner</h1>';
    
    // Test 1: Verifica stato attuale
    echo '<h2>1. Stato Attuale</h2>';
    $current_banner = get_option('wpb_banner_image', 'not_found');
    echo '<p>Valore corrente opzione wpb_banner_image: <strong>' . esc_html($current_banner) . '</strong></p>';
    
    // Test 2: Test salvataggio diretto
    echo '<h2>2. Test Salvataggio Diretto</h2>';
    
    if (isset($_POST['test_save'])) {
        $test_id = absint($_POST['test_image_id']);
        
        if ($test_id > 0) {
            echo '<p>Tentativo di salvare ID: ' . $test_id . '</p>';
            
            // Test con update_option diretto
            $result = update_option('wpb_banner_image', $test_id);
            echo '<p>Risultato update_option: ' . ($result ? 'SUCCESS' : 'FAILED') . '</p>';
            
            // Verifica immediata
            $saved_value = get_option('wpb_banner_image');
            echo '<p>Valore salvato: ' . $saved_value . '</p>';
            
            // Test con classe database se disponibile
            if (class_exists('WPB_Database')) {
                $db = new WPB_Database();
                $db_result = $db->set_banner_image($test_id);
                echo '<p>Risultato database class: ' . ($db_result ? 'SUCCESS' : 'FAILED') . '</p>';
                
                $db_value = $db->get_banner_image();
                echo '<p>Valore da database class: ' . $db_value . '</p>';
            }
        }
    }
    
    // Form di test
    echo '<form method="post">';
    echo '<p>';
    echo '<label>ID Immagine di Test: <input type="number" name="test_image_id" value="1" /></label>';
    echo '<input type="submit" name="test_save" value="Test Salvataggio" class="button button-primary" />';
    echo '</p>';
    echo '</form>';
    
    // Test 3: Verifica immagini disponibili
    echo '<h2>3. Immagini Disponibili</h2>';
    $images = get_posts(array(
        'post_type' => 'attachment',
        'post_mime_type' => 'image',
        'numberposts' => 10,
        'post_status' => 'inherit'
    ));
    
    if ($images) {
        echo '<table border="1" cellpadding="5">';
        echo '<tr><th>ID</th><th>Titolo</th><th>URL</th><th>Test</th></tr>';
        foreach ($images as $image) {
            $url = wp_get_attachment_url($image->ID);
            echo '<tr>';
            echo '<td>' . $image->ID . '</td>';
            echo '<td>' . esc_html($image->post_title) . '</td>';
            echo '<td><a href="' . esc_url($url) . '" target="_blank">Vedi</a></td>';
            echo '<td>';
            echo '<form method="post" style="display:inline;">';
            echo '<input type="hidden" name="test_image_id" value="' . $image->ID . '" />';
            echo '<input type="submit" name="test_save" value="Usa Questa" class="button button-small" />';
            echo '</form>';
            echo '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>Nessuna immagine trovata nella Media Library.</p>';
    }
    
    // Test 4: Debug informazioni
    echo '<h2>4. Debug Informazioni</h2>';
    echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
    echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
    echo '<p><strong>WooCommerce Active:</strong> ' . (class_exists('WooCommerce') ? 'YES' : 'NO') . '</p>';
    echo '<p><strong>Database Class Available:</strong> ' . (class_exists('WPB_Database') ? 'YES' : 'NO') . '</p>';
    
    // Test 5: Log recenti
    echo '<h2>5. Log Recenti</h2>';
    $log_file = WPB_PLUGIN_PATH . 'debug.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -20); // Ultime 20 righe
        
        echo '<textarea rows="10" cols="100" readonly>';
        echo esc_textarea(implode("\n", $recent_lines));
        echo '</textarea>';
        
        echo '<p><a href="#" onclick="location.reload();" class="button">Aggiorna Log</a></p>';
    } else {
        echo '<p>File di log non trovato.</p>';
    }
    
    // Test 6: Simulazione form admin
    echo '<h2>6. Simulazione Form Admin</h2>';
    
    if (isset($_POST['simulate_admin_form'])) {
        echo '<p>Simulazione invio form admin...</p>';
        
        // Simula $_POST del form admin
        $_POST['wpb_banner_image'] = $_POST['sim_image_id'];
        $_POST['wpb_nonce'] = wp_create_nonce('wpb_save_banner');
        $_POST['wpb_submit'] = 'Save Banner';
        
        // Ottieni istanza admin
        $plugin_instance = WooCommerce_Product_Banner::get_instance();
        if ($plugin_instance && $plugin_instance->admin) {
            // Simula il processo di salvataggio
            echo '<p>Chiamata handle_banner_upload...</p>';
            
            // Usa reflection per chiamare il metodo privato
            $reflection = new ReflectionClass($plugin_instance->admin);
            $method = $reflection->getMethod('handle_banner_upload');
            $method->setAccessible(true);
            
            try {
                $method->invoke($plugin_instance->admin);
                echo '<p>Metodo eseguito senza errori.</p>';
            } catch (Exception $e) {
                echo '<p>Errore: ' . esc_html($e->getMessage()) . '</p>';
            }
        }
    }
    
    echo '<form method="post">';
    echo '<p>';
    echo '<label>ID Immagine per Simulazione: <input type="number" name="sim_image_id" value="1" /></label>';
    echo '<input type="submit" name="simulate_admin_form" value="Simula Form Admin" class="button button-secondary" />';
    echo '</p>';
    echo '</form>';
    
    echo '</div>';
}

// Aggiungi pagina di test al menu admin
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Test Salvataggio Banner',
            'Test Salvataggio Banner',
            'manage_options',
            'wpb-test-save',
            'wpb_test_manual_save'
        );
    }
});
?>
