/*! elementor - v3.31.0 - 11-08-2025 */
(()=>{var e={10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,o)=>{var r=o(10564).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},15118:(e,t,o)=>{var r=o(10564).default,n=o(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,o)=>{var r=o(45498);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,o)=>{var r=o(10564).default,n=o(11327);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},66117:(e,t,o)=>{"use strict";var r=o(12470).__,n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(o(39805)),u=n(o(40989)),a=n(o(15118)),s=n(o(29402)),l=n(o(87861)),c=n(o(89758));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function BetaTesterLayout(){return(0,i.default)(this,BetaTesterLayout),function _callSuper(e,t,o){return t=(0,s.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,s.default)(e).constructor):t.apply(e,o))}(this,BetaTesterLayout,arguments)}return(0,l.default)(BetaTesterLayout,e),(0,u.default)(BetaTesterLayout,[{key:"ui",value:function ui(){return{closeModal:".elementor-templates-modal__header__close",dontShowAgain:".elementor-beta-tester-do-not-show-again"}}},{key:"events",value:function events(){return{"click @ui.closeModal":this.onCloseModalClick,"click @ui.dontShowAgain":this.onDontShowAgainClick}}},{key:"getModalOptions",value:function getModalOptions(){return{id:"elementor-beta-tester-modal",hide:{onBackgroundClick:!1}}}},{key:"getLogoOptions",value:function getLogoOptions(){return{title:r("Sign Up","elementor")}}},{key:"initialize",value:function initialize(){elementorModules.common.views.modal.Layout.prototype.initialize.apply(this,arguments),this.showLogo(),this.showContentView();var e=r("Don't Show Again","elementor");this.modalHeader.currentView.ui.closeModal.after(jQuery("<div>",{class:"elementor-beta-tester-do-not-show-again"}).text(e))}},{key:"showContentView",value:function showContentView(){this.modalContent.show(new c.default)}},{key:"onDontShowAgainClick",value:function onDontShowAgainClick(){this.hideModal(),this.onCloseModalClick()}},{key:"onCloseModalClick",value:function onCloseModalClick(){elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:elementorAdmin.config.beta_tester.beta_tester_signup}})}}])}(elementorModules.common.views.modal.Layout)},87861:(e,t,o)=>{var r=o(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},89758:(e,t,o)=>{"use strict";var r=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(o(39805)),i=r(o(40989)),u=r(o(15118)),a=r(o(29402)),s=r(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function BetaTesterView(){var e;return(0,n.default)(this,BetaTesterView),(e=function _callSuper(e,t,o){return t=(0,a.default)(t),(0,u.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,a.default)(e).constructor):t.apply(e,o))}(this,BetaTesterView)).id="elementor-beta-tester-dialog-content",e.template="#tmpl-elementor-beta-tester",e}return(0,s.default)(BetaTesterView,e),(0,i.default)(BetaTesterView,[{key:"ui",value:function ui(){return{betaForm:"#elementor-beta-tester-form",betaEmail:"#elementor-beta-tester-form__email",betaButton:"#elementor-beta-tester-form__submit"}}},{key:"events",value:function events(){return{"submit @ui.betaForm":"onBetaFormSubmit"}}},{key:"onBetaFormSubmit",value:function onBetaFormSubmit(e){e.preventDefault();var t=this.ui.betaEmail.val();this.ui.betaButton.addClass("elementor-button-state"),elementorCommon.ajax.addRequest("beta_tester_signup",{data:{betaTesterEmail:t}}),elementorBetaTester.layout.hideModal()}},{key:"onRender",value:function onRender(){}}])}(Marionette.ItemView)},91270:e=>{function _setPrototypeOf(t,o){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,o)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var r=t[o];if(void 0!==r)return r.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(96784),t=e(__webpack_require__(39805)),o=e(__webpack_require__(40989)),r=e(__webpack_require__(15118)),n=e(__webpack_require__(29402)),i=e(__webpack_require__(87861)),u=e(__webpack_require__(66117));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var a=function(e){function BetaTesterModule(){return(0,t.default)(this,BetaTesterModule),function _callSuper(e,t,o){return t=(0,n.default)(t),(0,r.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,n.default)(e).constructor):t.apply(e,o))}(this,BetaTesterModule,arguments)}return(0,i.default)(BetaTesterModule,e),(0,o.default)(BetaTesterModule,[{key:"onInit",value:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.showLayout(!1)}},{key:"showLayout",value:function showLayout(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0]||elementorAdmin.config.beta_tester.option_enabled&&!elementorAdmin.config.beta_tester.signup_dismissed&&"#tab-fontawesome4_migration"!==location.hash)&&(this.layout=new u.default,this.layout.showModal())}},{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{betaTesterFirstToKnow:"#beta-tester-first-to-know"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e={},t=this.getSettings("selectors");return e.$betaTesterFirstToKnow=jQuery(t.betaTesterFirstToKnow),e}},{key:"bindEvents",value:function bindEvents(){this.elements.$betaTesterFirstToKnow.on("click",this.showLayout.bind(this))}}])}(elementorModules.ViewModule);jQuery(function(){window.elementorBetaTester=new a})})()})();