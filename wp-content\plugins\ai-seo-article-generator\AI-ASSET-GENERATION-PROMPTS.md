# AI Asset Generation Prompts for WordPress Plugin

## 🎨 Plugin Icon Prompts

### Icon 256x256 (Primary)
```
Create a WordPress plugin icon with these exact specifications:
- Dimensions: 256x256 pixels
- Background: Modern gradient from #667eea (purple-blue) to #764ba2 (purple)
- Style: Flat design, minimal, professional
- Main element: Large "AI" text in bold, white color, centered
- Secondary element: Small upward arrow below the text representing growth/SEO
- Corner radius: 32px (12.5% of width)
- Additional details: Subtle circuit board pattern or dots in background at 10% opacity
- No shadows, no 3D effects, no complex textures
- Must be clearly visible and recognizable when scaled down to 32x32 pixels
- Export as PNG with transparent corners outside the rounded rectangle
```

### Icon 128x128 (Standard)
```
Create a WordPress plugin icon with these exact specifications:
- Dimensions: 128x128 pixels
- Background: Modern gradient from #667eea (purple-blue) to #764ba2 (purple)
- Style: Flat design, minimal, professional
- Main element: Large "AI" text in bold, white color, centered
- Secondary element: Small upward arrow below the text representing growth/SEO
- Corner radius: 16px (12.5% of width)
- Additional details: Subtle circuit board pattern or dots in background at 10% opacity
- No shadows, no 3D effects, no complex textures
- Must be clearly visible and recognizable at this size and when scaled to 32x32
- Export as PNG with transparent corners outside the rounded rectangle
```

### Icon SVG (Scalable)
```
Create a scalable vector WordPress plugin icon with these specifications:
- Format: SVG
- Viewbox: 0 0 256 256
- Background: Modern gradient from #667eea (purple-blue) to #764ba2 (purple)
- Style: Flat design, minimal, professional
- Main element: "AI" text in bold, white color, centered
- Secondary element: Upward arrow below the text representing growth/SEO
- Corner radius: 12.5% of width
- Use simple paths, no embedded images
- Optimize for small file size
- Must remain crisp at any scale
```

---

## 🖼️ Plugin Banner Prompts

### Banner 772x250 (Standard)
```
Create a WordPress plugin banner with these exact specifications:
- Dimensions: 772x250 pixels (exactly, no deviation)
- Background: Gradient blend - start with #667eea, transition to #764ba2 in middle, back to #667eea
- Layout: Horizontal banner, professional tech style
- Main title: "AI SEO Article Generator" - large, white, bold font, centered at 30% height
- Subtitle: "Powered by Claude & OpenAI" - smaller, slightly transparent white, centered at 45% height
- Feature badges at 70% height: 
  * "⚡ Generate in Seconds"
  * "🌐 Hebrew & English" 
  * "🔄 Background Processing"
- Design elements: Subtle AI circuit patterns, neural network lines, or abstract tech shapes at 10% opacity
- Style: Modern, clean, professional, tech-focused
- No photographs, no complex illustrations, no busy patterns
- Text must be clearly readable
- Export as PNG or JPG, max 1MB file size
```

### Banner 1544x500 (Retina/HiDPI)
```
Create a WordPress plugin banner with these exact specifications:
- Dimensions: 1544x500 pixels (exactly, no deviation)
- This is the 2x retina version of the 772x250 banner
- Background: Gradient blend - start with #667eea, transition to #764ba2 in middle, back to #667eea
- Layout: Horizontal banner, professional tech style
- Main title: "AI SEO Article Generator" - large, white, bold font, centered at 30% height
- Subtitle: "Powered by Claude & OpenAI" - smaller, slightly transparent white, centered at 45% height
- Feature badges at 70% height: 
  * "⚡ Generate in Seconds"
  * "🌐 Hebrew & English" 
  * "🔄 Background Processing"
- Design elements: Subtle AI circuit patterns, neural network lines, or abstract tech shapes at 10% opacity
- Style: Modern, clean, professional, tech-focused
- Ensure all text and elements are 2x the size of standard banner for retina clarity
- No photographs, no complex illustrations, no busy patterns
- Export as PNG or JPG, max 2MB file size
```

---

## 📸 Screenshot Prompts

### Screenshot 1 - Main Interface
```
Create a WordPress admin interface screenshot showing:
- Dimensions: 1280x800 pixels (recommended) or similar widescreen ratio
- Show WordPress admin dashboard with plugin active
- Plugin page title: "AI SEO Article Generator"
- Form fields visible:
  * Keyword input field (filled with "How to optimize WordPress SEO")
  * AI Model dropdown showing "Claude 3.5 Sonnet" selected
  * Language toggle showing both Hebrew and English options
  * Article structure dropdown
  * "Generate Article" button (prominent, blue)
- Progress indicator showing "Ready to generate"
- Clean, modern WordPress admin UI
- Browser chrome optional but recommended
- Highlight the simplicity and ease of use
```

### Screenshot 2 - Generated Article Preview
```
Create a screenshot showing generated article output:
- Dimensions: 1280x800 pixels or similar
- Split view or preview showing:
  * Beautiful formatted article with H1, H2, H3 headings
  * Table of contents visible
  * Meta description box
  * Word count indicator showing "2,847 words"
  * SEO score indicator (green checkmark)
- Show both Hebrew RTL and English examples if possible
- Highlight the quality and structure of output
- Include "Copy to Clipboard" and "Create Post" buttons
```

### Screenshot 3 - Structure Templates Library
```
Create a screenshot of the saved structures feature:
- Show a library/grid of saved article templates
- Include template names like:
  * "Ultimate Guide Template"
  * "How-to Article Structure"
  * "Product Review Format"
- Show options to edit, delete, or use templates
- Include "Save New Structure" button
- Clean card-based or list UI design
```

### Screenshot 4 - API Settings
```
Create a screenshot of the settings page showing:
- API configuration section
- Fields for:
  * Claude API Key (partially hidden)
  * OpenAI API Key (partially hidden)
  * Model selection preferences
- Test connection buttons with green success indicators
- Clean, organized settings layout
- Help text under each field
```

### Screenshot 5 - Background Processing Queue
```
Create a screenshot showing the background processing feature:
- Queue management interface
- List of articles being generated:
  * Show progress bars
  * Status indicators (Processing, Completed, Queued)
  * Time estimates
- Bulk actions available
- Clear, informative UI
```

### Screenshot 6 - Generated Articles List
```
Create a screenshot of the articles management page:
- Table/list of generated articles
- Columns: Title, Language, AI Model, Date, Status, Actions
- Mix of Hebrew and English articles
- Action buttons: Edit, Publish, Delete
- Filters and search functionality visible
```

---

## 🎨 General Design Guidelines for All Assets

### Color Palette
- Primary: #667eea (purple-blue)
- Secondary: #764ba2 (purple)
- Accent: #FFFFFF (white)
- Text: #FFFFFF on colored backgrounds
- Subtle elements: 10-20% opacity white

### Typography
- Use clean, modern sans-serif fonts
- Ensure readability at all sizes
- Bold for headlines, regular for body text

### Style Guidelines
- Modern, professional, tech-focused
- Flat design (no skeuomorphism)
- Minimal shadows if any
- Clean lines and shapes
- Consistent spacing and alignment
- Avoid clutter and complexity

### Technical Requirements
- PNG format for all assets (except SVG icon)
- sRGB color space
- Optimized file sizes
- Exact dimensions as specified
- No compression artifacts
- Sharp, clear images

### Brand Consistency
- Emphasize AI and SEO capabilities
- Highlight speed and ease of use
- Show support for multiple languages
- Professional but approachable
- Technology-forward appearance

---

## 📝 Usage Instructions

1. Copy the entire prompt for each asset you want to generate
2. Paste into your preferred AI image generator (DALL-E 3, Midjourney, Stable Diffusion, etc.)
3. Generate multiple variations and choose the best
4. Ensure exact dimensions before saving
5. Optimize file sizes using tools like TinyPNG
6. Save with exact filenames as specified in WordPress guidelines

## 🚀 Quick Generation Order

1. Start with icon-256x256.png (most important)
2. Create banner-772x250.png (second most visible)
3. Generate screenshot-1.png (main interface)
4. Complete remaining assets as needed

Remember: First impressions matter. Your icon and banner are the first things potential users see!