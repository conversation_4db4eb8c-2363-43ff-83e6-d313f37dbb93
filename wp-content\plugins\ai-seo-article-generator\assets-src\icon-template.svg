<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="256" height="256" rx="32" fill="url(#gradient)"/>
  
  <!-- AI Brain Circuit Pattern -->
  <g opacity="0.1">
    <path d="M128 64 L128 192 M64 128 L192 128" stroke="white" stroke-width="2"/>
    <circle cx="128" cy="64" r="4" fill="white"/>
    <circle cx="128" cy="192" r="4" fill="white"/>
    <circle cx="64" cy="128" r="4" fill="white"/>
    <circle cx="192" cy="128" r="4" fill="white"/>
  </g>
  
  <!-- Main AI Text -->
  <text x="128" y="120" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="white">AI</text>
  
  <!-- SEO Arrow -->
  <g transform="translate(128, 160)">
    <path d="M-20 10 L0 -10 L20 10" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    <line x1="0" y1="-10" x2="0" y2="15" stroke="white" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>