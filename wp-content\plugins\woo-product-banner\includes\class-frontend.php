<?php
/**
 * Frontend functionality for WooCommerce Product Banner
 */

if (!defined('ABSPATH')) {
    exit;
}

class WPB_Frontend {

    /**
     * Database instance
     */
    private $database;

    /**
     * Constructor
     */
    public function __construct($database = null) {
        $this->database = $database;
        add_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 25);
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_filter('woocommerce_single_product_summary', array($this, 'maybe_adjust_banner_position'));
    }
    
    /**
     * Display product banner on frontend
     */
    public function display_product_banner() {
        if (!is_product()) {
            return;
        }

        $banner_id = $this->database ? $this->database->get_banner_image() : get_option('wpb_banner_image', '');

        if (empty($banner_id) || !wp_attachment_is_image($banner_id)) {
            return;
        }
        
        $banner_url = wp_get_attachment_url($banner_id);
        if (!$banner_url) {
            return;
        }
        
        $banner_alt = get_post_meta($banner_id, '_wp_attachment_image_alt', true);
        $banner_title = get_the_title($banner_id);
        
        // Get image metadata for responsive images
        $image_meta = wp_get_attachment_metadata($banner_id);
        $image_sizes = $this->get_responsive_image_sizes($banner_id);
        
        // Apply filters to allow customization
        $banner_html = apply_filters('wpb_banner_html_before', '', $banner_id);
        
        $banner_html .= '<div class="wpb-product-banner" data-banner-id="' . esc_attr($banner_id) . '">';
        
        if (!empty($image_sizes)) {
            $banner_html .= '<img src="' . esc_url($banner_url) . '" ';
            $banner_html .= 'srcset="' . esc_attr($image_sizes['srcset']) . '" ';
            $banner_html .= 'sizes="' . esc_attr($image_sizes['sizes']) . '" ';
            $banner_html .= 'alt="' . esc_attr($banner_alt) . '" ';
            $banner_html .= 'title="' . esc_attr($banner_title) . '" ';
            $banner_html .= 'class="wpb-banner-image" ';
            $banner_html .= 'loading="lazy" ';
            $banner_html .= '/>';
        } else {
            $banner_html .= '<img src="' . esc_url($banner_url) . '" ';
            $banner_html .= 'alt="' . esc_attr($banner_alt) . '" ';
            $banner_html .= 'title="' . esc_attr($banner_title) . '" ';
            $banner_html .= 'class="wpb-banner-image" ';
            $banner_html .= 'loading="lazy" ';
            $banner_html .= '/>';
        }
        
        $banner_html .= '</div>';
        
        $banner_html = apply_filters('wpb_banner_html_after', $banner_html, $banner_id);
        
        echo $banner_html;
        
        // Track banner display for analytics (if needed)
        do_action('wpb_banner_displayed', $banner_id, get_the_ID());
    }
    
    /**
     * Get responsive image sizes
     */
    private function get_responsive_image_sizes($attachment_id) {
        $image_sizes = wp_get_attachment_image_srcset($attachment_id, 'full');
        
        if (!$image_sizes) {
            return false;
        }
        
        return array(
            'srcset' => $image_sizes,
            'sizes' => '(max-width: 768px) 100vw, (max-width: 1024px) 90vw, 1200px'
        );
    }
    
    /**
     * Maybe adjust banner position based on theme
     */
    public function maybe_adjust_banner_position() {
        // This can be used to adjust banner position for specific themes
        $current_theme = get_template();
        
        switch ($current_theme) {
            case 'storefront':
                // Storefront theme adjustments
                remove_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 25);
                add_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 30);
                break;
                
            case 'astra':
                // Astra theme adjustments
                remove_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 25);
                add_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 35);
                break;
                
            case 'woodmart':
                // WoodMart theme adjustments
                remove_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 25);
                add_action('woocommerce_single_product_summary', array($this, 'display_product_banner'), 40);
                break;
        }
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        if (is_product()) {
            wp_enqueue_style('wpb-frontend-style', WPB_PLUGIN_URL . 'assets/css/frontend.css', array(), WPB_PLUGIN_VERSION);
            
            // Add inline styles for theme compatibility
            $custom_css = $this->get_theme_compatibility_css();
            if (!empty($custom_css)) {
                wp_add_inline_style('wpb-frontend-style', $custom_css);
            }
        }
    }
    
    /**
     * Get theme compatibility CSS
     */
    private function get_theme_compatibility_css() {
        $current_theme = get_template();
        $css = '';
        
        switch ($current_theme) {
            case 'storefront':
                $css = '
                    .woocommerce div.product .wpb-product-banner {
                        margin-top: 2em;
                        margin-bottom: 1.5em;
                    }
                ';
                break;
                
            case 'astra':
                $css = '
                    .ast-woocommerce-container .wpb-product-banner {
                        margin-top: 2em;
                        margin-bottom: 2em;
                    }
                ';
                break;
                
            case 'woodmart':
                $css = '
                    .single-product-content .wpb-product-banner {
                        margin-top: 30px;
                        margin-bottom: 20px;
                    }
                ';
                break;
                
            case 'oceanwp':
                $css = '
                    .woo-single-post-class .wpb-product-banner {
                        margin-top: 25px;
                        margin-bottom: 20px;
                    }
                ';
                break;
        }
        
        return apply_filters('wpb_theme_compatibility_css', $css, $current_theme);
    }
    
    /**
     * Check if banner should be displayed for current product
     */
    public function should_display_banner($product_id = null) {
        if (!$product_id) {
            $product_id = get_the_ID();
        }
        
        // Allow filtering per product
        $display = apply_filters('wpb_display_banner_for_product', true, $product_id);
        
        // Check if banner image exists
        $banner_id = get_option('wpb_banner_image', '');
        if (empty($banner_id) || !wp_attachment_is_image($banner_id)) {
            $display = false;
        }
        
        return $display;
    }
    
    /**
     * Get banner data for current product
     */
    public function get_banner_data($product_id = null) {
        if (!$product_id) {
            $product_id = get_the_ID();
        }
        
        if (!$this->should_display_banner($product_id)) {
            return false;
        }
        
        $banner_id = get_option('wpb_banner_image', '');
        $banner_url = wp_get_attachment_url($banner_id);
        
        if (!$banner_url) {
            return false;
        }
        
        return array(
            'id' => $banner_id,
            'url' => $banner_url,
            'alt' => get_post_meta($banner_id, '_wp_attachment_image_alt', true),
            'title' => get_the_title($banner_id),
            'metadata' => wp_get_attachment_metadata($banner_id)
        );
    }
}
