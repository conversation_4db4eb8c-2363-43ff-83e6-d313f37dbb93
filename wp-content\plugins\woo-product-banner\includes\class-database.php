<?php
/**
 * Database functionality for WooCommerce Product Banner
 */

if (!defined('ABSPATH')) {
    exit;
}

class WPB_Database {
    
    /**
     * Option name for banner image
     */
    const BANNER_OPTION = 'wpb_banner_image';
    
    /**
     * Option name for plugin settings
     */
    const SETTINGS_OPTION = 'wpb_settings';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize database functionality
     */
    public function init() {
        // Hook into WordPress actions for data management
        add_action('delete_attachment', array($this, 'handle_attachment_deletion'));
        add_action('wp_ajax_wpb_get_banner_data', array($this, 'ajax_get_banner_data'));
    }
    
    /**
     * Get banner image ID
     */
    public function get_banner_image() {
        return get_option(self::BANNER_OPTION, '');
    }
    
    /**
     * Set banner image ID
     */
    public function set_banner_image($image_id) {
        if (function_exists('wpb_log')) {
            wpb_log('set_banner_image called with ID: ' . $image_id);
        }

        $image_id = absint($image_id);

        if (function_exists('wpb_log')) {
            wpb_log('Sanitized image ID: ' . $image_id);
        }

        if (empty($image_id) || !wp_attachment_is_image($image_id)) {
            if (function_exists('wpb_log')) {
                wpb_log('Image validation failed - empty ID or not an image');
            }
            return false;
        }

        if (function_exists('wpb_log')) {
            wpb_log('Attempting to update option: ' . self::BANNER_OPTION . ' with value: ' . $image_id);
        }

        $result = update_option(self::BANNER_OPTION, $image_id);

        if (function_exists('wpb_log')) {
            wpb_log('update_option result: ' . ($result ? 'SUCCESS' : 'FAILED'));

            // Verify the option was actually saved
            $saved_value = get_option(self::BANNER_OPTION);
            wpb_log('Verification - saved value: ' . $saved_value);

            // update_option returns false if the value is the same as before
            // So we need to check if the value is actually correct
            if (!$result && $saved_value == $image_id) {
                wpb_log('update_option returned false but value is correct (no change needed)');
                $result = true;
            }
        }

        return $result;
    }
    
    /**
     * Remove banner image
     */
    public function remove_banner_image() {
        return delete_option(self::BANNER_OPTION);
    }
    
    /**
     * Get banner image data
     */
    public function get_banner_data() {
        $banner_id = $this->get_banner_image();
        
        if (empty($banner_id) || !wp_attachment_is_image($banner_id)) {
            return false;
        }
        
        $banner_url = wp_get_attachment_url($banner_id);
        if (!$banner_url) {
            return false;
        }
        
        $image_meta = wp_get_attachment_metadata($banner_id);
        
        return array(
            'id' => $banner_id,
            'url' => $banner_url,
            'alt' => get_post_meta($banner_id, '_wp_attachment_image_alt', true),
            'title' => get_the_title($banner_id),
            'caption' => wp_get_attachment_caption($banner_id),
            'description' => get_post_field('post_content', $banner_id),
            'metadata' => $image_meta,
            'file_size' => $this->get_file_size($banner_id),
            'mime_type' => get_post_mime_type($banner_id),
            'upload_date' => get_the_date('Y-m-d H:i:s', $banner_id)
        );
    }
    
    /**
     * Get file size in human readable format
     */
    private function get_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            return '';
        }
        
        $bytes = filesize($file_path);
        $units = array('B', 'KB', 'MB', 'GB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * Validate banner image
     */
    public function validate_banner_image($image_id) {
        if (empty($image_id)) {
            return new WP_Error('empty_image', __('No image selected.', 'woo-product-banner'));
        }
        
        $image_id = absint($image_id);
        
        if (!wp_attachment_is_image($image_id)) {
            return new WP_Error('invalid_image', __('Selected file is not a valid image.', 'woo-product-banner'));
        }
        
        // Check if attachment exists
        if (!get_post($image_id)) {
            return new WP_Error('image_not_found', __('Selected image not found.', 'woo-product-banner'));
        }
        
        // Check file size (optional - can be configured)
        $max_size = apply_filters('wpb_max_image_size', 5 * 1024 * 1024); // 5MB default
        $file_path = get_attached_file($image_id);
        
        if ($file_path && file_exists($file_path)) {
            $file_size = filesize($file_path);
            if ($file_size > $max_size) {
                return new WP_Error('image_too_large', sprintf(
                    __('Image is too large. Maximum size allowed: %s', 'woo-product-banner'),
                    size_format($max_size)
                ));
            }
        }
        
        return true;
    }
    
    /**
     * Handle attachment deletion
     */
    public function handle_attachment_deletion($attachment_id) {
        $current_banner = $this->get_banner_image();
        
        if ($current_banner == $attachment_id) {
            $this->remove_banner_image();
            
            // Log the automatic removal
            error_log('WPB: Banner image automatically removed due to attachment deletion. ID: ' . $attachment_id);
            
            // Optionally notify admin
            if (apply_filters('wpb_notify_admin_on_banner_deletion', true)) {
                $this->notify_admin_banner_deleted($attachment_id);
            }
        }
    }
    
    /**
     * Notify admin when banner is automatically deleted
     */
    private function notify_admin_banner_deleted($attachment_id) {
        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        
        $subject = sprintf(__('[%s] Product Banner Removed', 'woo-product-banner'), $site_name);
        $message = sprintf(
            __('The product banner image (ID: %d) has been automatically removed because the associated media file was deleted.', 'woo-product-banner'),
            $attachment_id
        );
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * AJAX handler to get banner data
     */
    public function ajax_get_banner_data() {
        check_ajax_referer('wpb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions.', 'woo-product-banner'));
        }
        
        $banner_data = $this->get_banner_data();
        
        if ($banner_data) {
            wp_send_json_success($banner_data);
        } else {
            wp_send_json_error(__('No banner image found.', 'woo-product-banner'));
        }
    }
    
    /**
     * Get plugin settings
     */
    public function get_settings() {
        $defaults = array(
            'banner_position' => 25,
            'enable_lazy_loading' => true,
            'enable_responsive_images' => true,
            'show_on_all_products' => true,
            'excluded_categories' => array(),
            'excluded_products' => array()
        );
        
        $settings = get_option(self::SETTINGS_OPTION, array());
        return wp_parse_args($settings, $defaults);
    }
    
    /**
     * Update plugin settings
     */
    public function update_settings($settings) {
        $current_settings = $this->get_settings();
        $new_settings = wp_parse_args($settings, $current_settings);
        
        return update_option(self::SETTINGS_OPTION, $new_settings);
    }
    
    /**
     * Reset plugin data
     */
    public function reset_plugin_data() {
        delete_option(self::BANNER_OPTION);
        delete_option(self::SETTINGS_OPTION);
        delete_option('wpb_plugin_version');
        
        return true;
    }
    
    /**
     * Export plugin data
     */
    public function export_data() {
        return array(
            'banner_image' => $this->get_banner_image(),
            'banner_data' => $this->get_banner_data(),
            'settings' => $this->get_settings(),
            'export_date' => current_time('mysql'),
            'plugin_version' => WPB_PLUGIN_VERSION
        );
    }
    
    /**
     * Import plugin data
     */
    public function import_data($data) {
        if (!is_array($data)) {
            return new WP_Error('invalid_data', __('Invalid import data.', 'woo-product-banner'));
        }
        
        $imported = array();
        
        // Import banner image
        if (isset($data['banner_image']) && !empty($data['banner_image'])) {
            $validation = $this->validate_banner_image($data['banner_image']);
            if (!is_wp_error($validation)) {
                $this->set_banner_image($data['banner_image']);
                $imported['banner_image'] = true;
            }
        }
        
        // Import settings
        if (isset($data['settings']) && is_array($data['settings'])) {
            $this->update_settings($data['settings']);
            $imported['settings'] = true;
        }
        
        return $imported;
    }
}
