# WooCommerce Product Banner - Risoluzione Problemi

## Errore Critico durante l'Attivazione

Se ricevi un errore critico quando attivi il plugin, segui questi passaggi per identificare e risolvere il problema:

### 1. Abilita il Debug di WordPress

Aggiungi queste righe al tuo file `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### 2. Controlla i Log di Errore

Dopo aver tentato di attivare il plugin, controlla questi file per errori:

- `wp-content/debug.log` - Log generale di WordPress
- `wp-content/plugins/woo-product-banner/debug.log` - Log specifico del plugin
- `wp-content/plugins/woo-product-banner/activation-debug.log` - Log di attivazione

### 3. Verifica i Requisiti

Il plugin richiede:

- **PHP**: 7.4 o superiore
- **WordPress**: 5.0 o superiore  
- **WooCommerce**: 5.0 o superiore

Verifica le tue versioni:
- PHP: Vai su **Strumenti > Salute del sito > Info** in WordPress
- WordPress: Visibile nel footer dell'admin
- WooCommerce: Vai su **WooCommerce > Stato**

### 4. Test di Attivazione Manuale

Esegui questo test dalla directory del plugin:

```bash
php quick-test.php
```

Questo ti mostrerà se ci sono errori di sintassi o problemi di caricamento.

### 5. Problemi Comuni e Soluzioni

#### Errore: "Class not found"
**Causa**: File delle classi mancanti o non caricati
**Soluzione**: 
1. Verifica che tutti i file siano presenti nella cartella `includes/`
2. Controlla i permessi dei file (devono essere leggibili)
3. Ricarica i file del plugin

#### Errore: "Fatal error: Cannot redeclare"
**Causa**: Plugin caricato più volte o conflitto con altro plugin
**Soluzione**:
1. Disattiva tutti gli altri plugin
2. Riattiva il plugin WooCommerce Product Banner
3. Riattiva gli altri plugin uno alla volta per identificare conflitti

#### Errore: "WooCommerce not found"
**Causa**: WooCommerce non è installato o attivo
**Soluzione**:
1. Installa e attiva WooCommerce
2. Verifica che WooCommerce funzioni correttamente
3. Riattiva il plugin

#### Errore: "Permission denied"
**Causa**: Permessi insufficienti sui file
**Soluzione**:
```bash
chmod 755 wp-content/plugins/woo-product-banner/
chmod 644 wp-content/plugins/woo-product-banner/*.php
chmod 644 wp-content/plugins/woo-product-banner/includes/*.php
```

### 6. Debug Avanzato

#### Modalità Debug Completa

Aggiungi al file `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
define('SCRIPT_DEBUG', true);
define('SAVEQUERIES', true);
```

#### Test delle Singole Classi

Testa ogni classe individualmente:

```php
// Test Database Class
if (class_exists('WPB_Database')) {
    $db = new WPB_Database();
    echo "Database class OK\n";
} else {
    echo "Database class FAILED\n";
}

// Test Admin Class  
if (class_exists('WPB_Admin')) {
    $admin = new WPB_Admin();
    echo "Admin class OK\n";
} else {
    echo "Admin class FAILED\n";
}
```

### 7. Ripristino di Emergenza

Se il plugin causa problemi gravi:

#### Metodo 1: Via FTP/File Manager
1. Rinomina la cartella del plugin da `woo-product-banner` a `woo-product-banner-disabled`
2. Il plugin verrà automaticamente disattivato

#### Metodo 2: Via Database
Esegui questa query nel database:
```sql
UPDATE wp_options 
SET option_value = REPLACE(option_value, 'woo-product-banner/woo-product-banner.php', '') 
WHERE option_name = 'active_plugins';
```

#### Metodo 3: Via wp-config.php
Aggiungi temporaneamente:
```php
define('WP_DEBUG', true);
// Disabilita tutti i plugin
define('WP_PLUGIN_DIR', '/path/to/empty/directory');
```

### 8. Informazioni per il Supporto

Se hai bisogno di supporto, includi queste informazioni:

#### Informazioni di Sistema
- Versione PHP: `<?php echo PHP_VERSION; ?>`
- Versione WordPress: Visibile nel footer admin
- Versione WooCommerce: WooCommerce > Stato
- Tema attivo: Aspetto > Temi
- Altri plugin attivi: Plugin > Plugin installati

#### Log di Errore
- Contenuto di `wp-content/debug.log`
- Contenuto di `wp-content/plugins/woo-product-banner/debug.log`
- Contenuto di `wp-content/plugins/woo-product-banner/activation-debug.log`

#### Test Eseguiti
- Risultato di `php quick-test.php`
- Screenshot dell'errore
- Passaggi per riprodurre il problema

### 9. Prevenzione Problemi Futuri

#### Backup Prima degli Aggiornamenti
```bash
cp -r wp-content/plugins/woo-product-banner wp-content/plugins/woo-product-banner-backup
```

#### Test in Ambiente di Staging
- Testa sempre gli aggiornamenti in un ambiente di test
- Verifica compatibilità con altri plugin
- Controlla funzionalità dopo aggiornamenti WordPress/WooCommerce

#### Monitoraggio Log
- Controlla regolarmente i log di errore
- Imposta notifiche per errori critici
- Mantieni backup recenti

### 10. Contatti per Supporto

Se i passaggi sopra non risolvono il problema:

1. **GitHub Issues**: [Link al repository]
2. **Email Supporto**: [Email di supporto]
3. **Forum WordPress**: [Link al forum]

Includi sempre:
- Descrizione dettagliata del problema
- Passaggi per riprodurre l'errore
- Informazioni di sistema
- Log di errore rilevanti
- Screenshot se applicabili

---

## Note Aggiuntive

- Questo plugin è stato testato con le versioni più recenti di WordPress e WooCommerce
- Per problemi di compatibilità con temi specifici, consulta la documentazione del tema
- Gli aggiornamenti del plugin includono sempre note di compatibilità

**Ultimo aggiornamento**: Gennaio 2024
