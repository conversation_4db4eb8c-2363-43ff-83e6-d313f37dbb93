<?php

if (!defined('ABSPATH')) {
    exit;
}

class AI_SEO_Article_Generator_AEO_Content {
    
    private $plugin;
    
    public function __construct() {
        $this->plugin = ai_seo_article_generator();
        $this->init_hooks();
    }
    
    private function init_hooks() {
        add_filter('ai_seo_article_generator_structure_prompt', array($this, 'enhance_structure_prompt_for_aeo'), 10, 2);
        add_filter('ai_seo_article_generator_content_prompt', array($this, 'enhance_content_prompt_for_aeo'), 10, 2);
        add_filter('ai_seo_article_generator_generated_content', array($this, 'optimize_content_for_aeo'), 10, 2);
    }
    
    /**
     * Enhance structure generation prompt with AEO requirements
     */
    public function enhance_structure_prompt_for_aeo($prompt, $data) {
        $aeo_requirements = $this->get_aeo_structure_requirements($data);
        
        $enhanced_prompt = $prompt . "\n\n" . $aeo_requirements;
        
        return $enhanced_prompt;
    }
    
    /**
     * Enhance content generation prompt with AEO requirements
     */
    public function enhance_content_prompt_for_aeo($prompt, $data) {
        $aeo_requirements = $this->get_aeo_content_requirements($data);
        
        $enhanced_prompt = $prompt . "\n\n" . $aeo_requirements;
        
        return $enhanced_prompt;
    }
    
    /**
     * Post-process generated content to optimize for AEO
     */
    public function optimize_content_for_aeo($content, $data) {
        // Add answer-first structure
        $content = $this->add_answer_first_structure($content, $data);
        
        // Enhance with citation blocks
        $content = $this->add_citation_blocks($content, $data);
        
        // Add question-based headers
        $content = $this->enhance_with_question_headers($content);
        
        // Add structured snippets
        $content = $this->add_structured_snippets($content, $data);
        
        return $content;
    }
    
    /**
     * Get AEO requirements for structure generation
     */
    private function get_aeo_structure_requirements($data) {
        $language = isset($data['is_hebrew']) && $data['is_hebrew'] ? 'Hebrew' : 'English';
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        
        if ($language === 'Hebrew') {
            return "דרישות AEO (אופטימיזציה למנועי תשובות):

1. **מבנה תשובה-ראשון**: כל חלק חייב להתחיל בתשובה ישירה ומלאה (40-60 מילים) למה שהמשתמש מחפש
2. **כותרות מבוססות שאלות**: השתמש בכותרות בצורת שאלה (מה זה {$main_keyword}? איך עובד {$main_keyword}? למה חשוב {$main_keyword}?)
3. **בלוקי מידע מובנים**: צור קטעים קצרים וברורים שקל לחלץ מהם מידע
4. **הגדרות ברורות**: תמיד הגדר מונחים מרכזיים בתחילת הקטע
5. **עובדות מספריות**: כלול נתונים ומספרים מדויקים
6. **מקורות רשמיים**: תכנן להזכיר מקורות אמינים (1-2 לכל קטע מרכזי)
7. **מידע עדכני**: דגש על מידע מ-2024-2025

מבנה מומלץ לכל קטע:
- תשובה ישירה (40-60 מילים)
- הסבר מפורט
- דוגמה או נתון
- קישור למקור אמין";
        } else {
            return "AEO (Answer Engine Optimization) Requirements:

1. **Answer-First Structure**: Each section must start with a direct, complete answer (40-60 words) to what users are searching for
2. **Question-Based Headers**: Use question format headings (What is {$main_keyword}? How does {$main_keyword} work? Why is {$main_keyword} important?)
3. **Structured Information Blocks**: Create short, clear sections that are easy to extract information from
4. **Clear Definitions**: Always define key terms at the beginning of sections
5. **Statistical Data**: Include accurate data and numbers
6. **Authoritative Sources**: Plan to mention reliable sources (1-2 per major section)
7. **Current Information**: Emphasize 2024-2025 data

Recommended structure for each section:
- Direct answer (40-60 words)
- Detailed explanation
- Example or statistic
- Link to authoritative source";
        }
    }
    
    /**
     * Get AEO requirements for content generation
     */
    private function get_aeo_content_requirements($data) {
        $language = isset($data['is_hebrew']) && $data['is_hebrew'] ? 'Hebrew' : 'English';
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        
        if ($language === 'Hebrew') {
            return "הנחיות יצירת תוכן AEO:

1. **תשובות ישירות**: התחל כל קטע עם תשובה מלאה במשפט אחד לשניים
2. **שפה שיחתית**: כתב כמו שאתה עונה על שאלה של חבר
3. **מבנה הגיוני**: ארגן מידע מהכללי לפרטי
4. **דוגמאות ברורות**: תן דוגמאות קונקרטיות לכל מושג
5. **עובדות ניתנות לאימות**: השתמש במידע שניתן לבדוק
6. **קישורים יוצאים**: הכן מקום ל-1-2 קישורים אמינים בכל קטע מרכזי
7. **אותות E-E-A-T**: הדגש מומחיות, ניסיון, סמכות ואמינות

פורמט תשובה אידיאלי:
'המונח {$main_keyword} מתייחס ל[הגדרה קצרה ובהירה]. זה חשוב כי [סיבה מרכזית]. הדרך הטובה ביותר להשתמש בזה היא [הנחיה פרקטית].'";
        } else {
            return "AEO Content Creation Guidelines:

1. **Direct Answers**: Start each section with a complete answer in one to two sentences
2. **Conversational Language**: Write like you're answering a friend's question
3. **Logical Structure**: Organize information from general to specific
4. **Clear Examples**: Provide concrete examples for every concept
5. **Verifiable Facts**: Use information that can be fact-checked
6. **Outbound Links**: Prepare space for 1-2 authoritative links per major section
7. **E-E-A-T Signals**: Emphasize expertise, experience, authority, and trustworthiness

Ideal answer format:
'{$main_keyword} refers to [short, clear definition]. This is important because [main reason]. The best way to use this is [practical guidance].'";
        }
    }
    
    /**
     * Add answer-first structure to content
     */
    private function add_answer_first_structure($content, $data) {
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
        
        // Split content into sections by headings
        $sections = $this->split_content_by_headings($content);
        $optimized_content = '';
        
        foreach ($sections as $section) {
            if (isset($section['heading']) && isset($section['content'])) {
                $heading = $section['heading'];
                $section_content = $section['content'];
                
                // Generate answer-first opener for each section
                $answer_block = $this->generate_answer_block($heading, $section_content, $main_keyword, $is_hebrew);
                
                $optimized_content .= $heading . "\n\n";
                
                if ($answer_block) {
                    $optimized_content .= $answer_block . "\n\n";
                }
                
                $optimized_content .= $section_content . "\n\n";
            } else {
                $optimized_content .= $section . "\n\n";
            }
        }
        
        return trim($optimized_content);
    }
    
    /**
     * Generate answer block for section
     */
    private function generate_answer_block($heading, $content, $main_keyword, $is_hebrew) {
        // Extract first meaningful sentence that contains key information
        $sentences = $this->extract_sentences($content);
        $best_sentence = $this->find_best_answer_sentence($sentences, $main_keyword);
        
        if (!$best_sentence) {
            return null;
        }
        
        // Format as answer block
        if ($is_hebrew) {
            $answer_prefix = "**תשובה מהירה**: ";
        } else {
            $answer_prefix = "**Quick Answer**: ";
        }
        
        // Ensure answer is 40-60 words
        $answer = $this->optimize_answer_length($best_sentence, 40, 60);
        
        return $answer_prefix . $answer;
    }
    
    /**
     * Add citation blocks throughout content
     */
    private function add_citation_blocks($content, $data) {
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
        
        // Find locations to insert citation placeholders
        $paragraphs = explode("\n\n", $content);
        $enhanced_paragraphs = array();
        
        foreach ($paragraphs as $index => $paragraph) {
            $enhanced_paragraphs[] = $paragraph;
            
            // Add citation placeholder after paragraphs with claims or statistics
            if ($this->paragraph_needs_citation($paragraph)) {
                $citation_placeholder = $this->generate_citation_placeholder($is_hebrew);
                $enhanced_paragraphs[] = $citation_placeholder;
            }
        }
        
        return implode("\n\n", $enhanced_paragraphs);
    }
    
    /**
     * Enhance content with question-based headers
     */
    private function enhance_with_question_headers($content) {
        // Convert declarative headers to questions where appropriate
        $patterns = array(
            // Hebrew patterns
            '/^(#{1,6})\s*([^?\n]+)(מהו|מה זה|איך|כיצד|למה|מתי|איפה)([^?\n]+)$/m' => '$1 $3 $2$4?',
            '/^(#{1,6})\s*([^?\n]+)(הגדרה|סיבות|דרכים|שיטות|יתרונות|חסרונות)([^?\n]*)$/m' => '$1 מה $3 $2$4?',
            
            // English patterns
            '/^(#{1,6})\s*(Benefits?|Advantages?)\s+of\s+([^?\n]+)$/mi' => '$1 What Are the $2 of $3?',
            '/^(#{1,6})\s*(How|What|Why|When|Where)\s+([^?\n]+)$/mi' => '$1 $2 $3?',
            '/^(#{1,6})\s*([^?\n]+)\s+(Definition|Explanation|Guide|Tutorial)$/mi' => '$1 What Is $2?'
        );
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * Add structured snippets for better AI extraction
     */
    private function add_structured_snippets($content, $data) {
        $main_keyword = isset($data['main_keyword']) ? $data['main_keyword'] : '';
        $is_hebrew = isset($data['is_hebrew']) && $data['is_hebrew'];
        
        // Look for opportunities to add structured data
        $patterns = array();
        
        if ($is_hebrew) {
            $patterns = array(
                'definition' => '/(?:^|\n)(.*?)(?:מתייחס ל|הוא|היא|זה|זו|מגדיר|מציין)(.+?)\./',
                'steps' => '/(?:שלבים?|צעדים?|הוראות):\s*\n(.+?)(?=\n\n|\n#|$)/s',
                'benefits' => '/(?:יתרונות?|הטבות?|תועלות?):\s*\n(.+?)(?=\n\n|\n#|$)/s'
            );
        } else {
            $patterns = array(
                'definition' => '/(?:^|\n)(.*?)(?:refers to|is|means|defines)(.+?)\./',
                'steps' => '/(?:steps?|instructions?):\s*\n(.+?)(?=\n\n|\n#|$)/s',
                'benefits' => '/(?:benefits?|advantages?):\s*\n(.+?)(?=\n\n|\n#|$)/s'
            );
        }
        
        foreach ($patterns as $type => $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $snippet = $this->create_structured_snippet($type, $matches, $is_hebrew);
                if ($snippet) {
                    $content = str_replace($matches[0], $matches[0] . "\n\n" . $snippet, $content);
                }
            }
        }
        
        return $content;
    }
    
    // Helper methods
    
    private function split_content_by_headings($content) {
        $sections = array();
        $parts = preg_split('/(^#{1,6}\s+.+$)/m', $content, -1, PREG_SPLIT_DELIM_CAPTURE);
        
        for ($i = 0; $i < count($parts); $i++) {
            if (preg_match('/^#{1,6}\s+/', $parts[$i])) {
                $section = array(
                    'heading' => $parts[$i],
                    'content' => isset($parts[$i + 1]) ? trim($parts[$i + 1]) : ''
                );
                $sections[] = $section;
                $i++; // Skip the content part in next iteration
            } else if (trim($parts[$i])) {
                $sections[] = trim($parts[$i]);
            }
        }
        
        return $sections;
    }
    
    private function extract_sentences($content) {
        $text = wp_strip_all_tags($content);
        $sentences = preg_split('/[.!?]+/', $text);
        
        return array_filter(array_map('trim', $sentences), function($sentence) {
            return strlen($sentence) > 20;
        });
    }
    
    private function find_best_answer_sentence($sentences, $main_keyword) {
        $scored_sentences = array();
        
        foreach ($sentences as $sentence) {
            $score = 0;
            
            // Higher score for sentences containing main keyword
            if (stripos($sentence, $main_keyword) !== false) {
                $score += 10;
            }
            
            // Higher score for definition patterns
            $definition_patterns = array(
                '/(?:מתייחס ל|הוא|היא|זה|זו|מגדיר|מציין)/u', // Hebrew
                '/(?:refers to|is|means|defines)/i' // English
            );
            
            foreach ($definition_patterns as $pattern) {
                if (preg_match($pattern, $sentence)) {
                    $score += 15;
                    break;
                }
            }
            
            // Prefer sentences of ideal length (30-80 words)
            $word_count = str_word_count($sentence);
            if ($word_count >= 30 && $word_count <= 80) {
                $score += 5;
            }
            
            // Lower score for very long or very short sentences
            if ($word_count < 15 || $word_count > 100) {
                $score -= 5;
            }
            
            if ($score > 0) {
                $scored_sentences[] = array(
                    'sentence' => $sentence,
                    'score' => $score
                );
            }
        }
        
        if (empty($scored_sentences)) {
            return null;
        }
        
        // Sort by score and return the best one
        usort($scored_sentences, function($a, $b) {
            return $b['score'] - $a['score'];
        });
        
        return $scored_sentences[0]['sentence'];
    }
    
    private function optimize_answer_length($text, $min_words, $max_words) {
        $words = explode(' ', $text);
        $word_count = count($words);
        
        if ($word_count <= $max_words) {
            return $text;
        }
        
        // Truncate to max words and add ellipsis
        $truncated = implode(' ', array_slice($words, 0, $max_words - 1));
        return $truncated . '...';
    }
    
    private function paragraph_needs_citation($paragraph) {
        // Check for patterns that typically need citations
        $citation_indicators = array(
            // Hebrew patterns
            '/(?:\d+%|\d+\s*אחוז|מחקר|לפי|על פי|נתונים|סטטיסטיקה|דו"ח)/u',
            // English patterns
            '/(?:\d+%|percent|study|research|according to|data|statistics|report|survey)/i'
        );
        
        foreach ($citation_indicators as $pattern) {
            if (preg_match($pattern, $paragraph)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function generate_citation_placeholder($is_hebrew) {
        if ($is_hebrew) {
            return "*[מקור: יש להוסיף קישור למקור אמין]*";
        } else {
            return "*[Source: Add link to authoritative source]*";
        }
    }
    
    private function create_structured_snippet($type, $matches, $is_hebrew) {
        switch ($type) {
            case 'definition':
                $term = trim($matches[1]);
                $definition = trim($matches[2]);
                
                if ($is_hebrew) {
                    return "**הגדרה**: {$term} {$definition}";
                } else {
                    return "**Definition**: {$term} {$definition}";
                }
                
            case 'steps':
                if ($is_hebrew) {
                    return "**סיכום שלבים**:\n" . $matches[1];
                } else {
                    return "**Step Summary**:\n" . $matches[1];
                }
                
            case 'benefits':
                if ($is_hebrew) {
                    return "**יתרונות עיקריים**:\n" . $matches[1];
                } else {
                    return "**Key Benefits**:\n" . $matches[1];
                }
        }
        
        return null;
    }
}