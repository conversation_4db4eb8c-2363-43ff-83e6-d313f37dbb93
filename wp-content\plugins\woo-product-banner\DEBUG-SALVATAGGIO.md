# Debug Problema Salvataggio Immagine Banner

## 🔍 Problema Identificato

L'immagine del banner non viene memorizzata quando si invia il form admin.

## 🛠️ Debug Implementato

Ho aggiunto debug completo per identificare esattamente dove si blocca il processo:

### 1. **Debug PHP (Server-side)**

#### Classe Admin (`class-admin.php`)
- ✅ Log in `admin_init()` per verificare se viene chiamato
- ✅ Log quando viene rilevato invio form
- ✅ Log completo dei dati POST
- ✅ Debug info visibile nella pagina admin (se WP_DEBUG attivo)

#### Classe Database (`class-database.php`)
- ✅ Log in `set_banner_image()` con tutti i passaggi
- ✅ Verifica del valore prima e dopo `update_option()`
- ✅ Controllo validazione immagine

### 2. **Debug JavaScript (Client-side)**

#### File `admin.js`
- ✅ Log quando viene selezionata un'immagine
- ✅ Verifica valore input dopo selezione
- ✅ Log durante invio form
- ✅ Validazione con debug

### 3. **Pagine di Test**

#### `Tools > Test Salvataggio Banner`
- ✅ Test salvataggio diretto con `update_option()`
- ✅ Test con classe database
- ✅ Lista immagini disponibili
- ✅ Simulazione form admin
- ✅ Visualizzazione log recenti

## 🧪 Come Fare il Debug

### **Passo 1: Abilita Debug**
Aggiungi a `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### **Passo 2: Vai alla Pagina Admin**
1. Vai su **Prodotti > Banner Prodotti**
2. Dovresti vedere una sezione "Debug Info" in alto (se WP_DEBUG è attivo)

### **Passo 3: Testa il Salvataggio**
1. Clicca **"Seleziona Immagine"**
2. Scegli un'immagine dalla Media Library
3. Clicca **"Usa questa immagine"**
4. Apri Console del Browser (F12) e controlla i log JavaScript
5. Clicca **"Salva Banner"**
6. Controlla i messaggi di debug nella pagina

### **Passo 4: Controlla i Log**
Controlla questi file per errori:
- `wp-content/debug.log`
- `wp-content/plugins/woo-product-banner/debug.log`

### **Passo 5: Test Manuale**
1. Vai su **Strumenti > Test Salvataggio Banner**
2. Prova il salvataggio diretto con un ID immagine
3. Verifica se il problema è nel form o nel salvataggio

## 🔍 Cosa Cercare nei Log

### **Log JavaScript (Console Browser)**
```
WPB Debug: Setting image ID to: 123
WPB Debug: Input value after setting: 123
WPB Debug: Form submitted
WPB Debug: Submit type: wpb_submit
WPB Debug: Image ID value: 123
WPB Debug: Validating image ID: 123
WPB Debug: Validation passed, submitting form
```

### **Log PHP (debug.log)**
```
[timestamp] WPB: admin_init called
[timestamp] WPB: Form submission detected - wpb_submit
[timestamp] WPB: POST data: Array([wpb_banner_image] => 123 ...)
[timestamp] WPB: handle_banner_upload called
[timestamp] WPB: Banner ID from POST: 123
[timestamp] WPB: Database instance available: YES
[timestamp] WPB: Processing banner ID: 123
[timestamp] WPB: Using database class for validation
[timestamp] WPB: Validation passed, attempting to save
[timestamp] WPB: set_banner_image called with ID: 123
[timestamp] WPB: Attempting to update option: wpb_banner_image with value: 123
[timestamp] WPB: update_option result: SUCCESS
[timestamp] WPB: Verification - saved value: 123
[timestamp] WPB: Save result: SUCCESS
```

## 🚨 Possibili Problemi e Soluzioni

### **1. JavaScript non funziona**
**Sintomi**: Nessun log JavaScript nella console
**Soluzioni**:
- Verifica che jQuery sia caricato
- Controlla errori JavaScript nella console
- Verifica che `wpb_admin` object sia disponibile

### **2. Form non viene inviato**
**Sintomi**: Nessun log "Form submission detected"
**Soluzioni**:
- Verifica che il nonce sia corretto
- Controlla che il form abbia `method="post"`
- Verifica che il pulsante submit abbia `name="wpb_submit"`

### **3. Dati POST non arrivano**
**Sintomi**: Log "POST data" vuoto o mancante
**Soluzioni**:
- Verifica che l'input hidden abbia il valore corretto
- Controlla che il form non abbia errori HTML
- Verifica che non ci siano redirect che perdono i dati POST

### **4. Database non salva**
**Sintomi**: `update_option result: FAILED`
**Soluzioni**:
- Verifica permessi database
- Controlla che l'opzione non sia protetta
- Verifica che non ci siano hook che interferiscono

### **5. Validazione fallisce**
**Sintomi**: "Validation failed" nei log
**Soluzioni**:
- Verifica che l'ID immagine esista nella Media Library
- Controlla che sia effettivamente un'immagine
- Verifica che l'attachment non sia stato eliminato

## 📋 Checklist Debug

- [ ] WP_DEBUG attivato
- [ ] Console browser aperta durante test
- [ ] Immagine selezionata dalla Media Library
- [ ] Log JavaScript visibili nella console
- [ ] Form inviato con successo
- [ ] Log PHP presenti in debug.log
- [ ] Valore salvato verificato con test manuale
- [ ] Pagina ricaricata per vedere se il valore persiste

## 🎯 Prossimi Passi

1. **Esegui il debug** seguendo i passi sopra
2. **Raccogli i log** JavaScript e PHP
3. **Identifica il punto di fallimento** confrontando con i log attesi
4. **Applica la soluzione** specifica per il problema trovato

Una volta identificato il punto esatto dove si blocca il processo, potremo applicare la correzione mirata.

## 📞 Informazioni da Fornire

Se il problema persiste, fornisci:

1. **Log JavaScript completi** dalla console del browser
2. **Log PHP** da `wp-content/debug.log` e `wp-content/plugins/woo-product-banner/debug.log`
3. **Screenshot** della sezione "Debug Info" nella pagina admin
4. **Risultato** del test manuale da "Strumenti > Test Salvataggio Banner"
5. **Versioni** di WordPress, WooCommerce, PHP, e browser utilizzato

Con queste informazioni potremo identificare e risolvere rapidamente il problema specifico.
